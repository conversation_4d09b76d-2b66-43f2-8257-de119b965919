{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\AddDailySewingRecord.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\nimport Select from \"react-select\";\nimport { Row, Col, Card, Form, Button, Alert, Spinner, Badge } from \"react-bootstrap\";\nimport { FaInfoCircle, FaTshirt, FaCheck, FaExclamationTriangle, FaClipboardCheck, FaPlus, FaTrash } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddDailySewingRecord = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [selectedProduct, setSelectedProduct] = useState(\"\");\n  const [productColors, setProductColors] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768); // Track sidebar state\n\n  // Array to store multiple color variants\n  const [colorVariants, setColorVariants] = useState([{\n    id: Date.now(),\n    selectedColor: \"\",\n    alreadySewn: null,\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0,\n    damageCount: 0,\n    loading: false\n  }]);\n  const [message, setMessage] = useState(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formValid, setFormValid] = useState(false);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  useEffect(() => {\n    axios.get(\"http://localhost:8000/api/cutting/cutting-records/\").then(res => setProducts(res.data)).catch(err => console.error(\"Error fetching products:\", err));\n  }, []);\n  useEffect(() => {\n    if (selectedProduct) {\n      const product = products.find(p => p.id === parseInt(selectedProduct));\n      if (product !== null && product !== void 0 && product.details) {\n        const options = product.details.map(detail => {\n          var _detail$fabric_varian, _detail$fabric_varian2, _detail$fabric_varian3, _detail$fabric_varian4, _detail$fabric_varian5;\n          const totalCut = (detail.xs || 0) + (detail.s || 0) + (detail.m || 0) + (detail.l || 0) + (detail.xl || 0);\n\n          // Get fabric name and color name for better identification\n          const fabricName = ((_detail$fabric_varian = detail.fabric_variant_data) === null || _detail$fabric_varian === void 0 ? void 0 : (_detail$fabric_varian2 = _detail$fabric_varian.fabric_definition_data) === null || _detail$fabric_varian2 === void 0 ? void 0 : _detail$fabric_varian2.fabric_name) || \"Unknown Fabric\";\n          const colorName = ((_detail$fabric_varian3 = detail.fabric_variant_data) === null || _detail$fabric_varian3 === void 0 ? void 0 : _detail$fabric_varian3.color_name) || ((_detail$fabric_varian4 = detail.fabric_variant_data) === null || _detail$fabric_varian4 === void 0 ? void 0 : _detail$fabric_varian4.color) || \"N/A\";\n          return {\n            value: detail.id,\n            label: `${fabricName} - ${colorName}`,\n            fabricName: fabricName,\n            colorName: colorName,\n            color: ((_detail$fabric_varian5 = detail.fabric_variant_data) === null || _detail$fabric_varian5 === void 0 ? void 0 : _detail$fabric_varian5.color) || \"#ffffff\",\n            totalCut,\n            // Store individual size quantities for validation\n            xs_cut: detail.xs || 0,\n            s_cut: detail.s || 0,\n            m_cut: detail.m || 0,\n            l_cut: detail.l || 0,\n            xl_cut: detail.xl || 0\n          };\n        });\n        setProductColors(options);\n      } else {\n        setProductColors([]);\n      }\n      // Reset all color variants when product changes\n      setColorVariants([{\n        id: Date.now(),\n        selectedColor: \"\",\n        alreadySewn: null,\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0,\n        damageCount: 0,\n        loading: false\n      }]);\n    }\n  }, [selectedProduct, products]);\n\n  // Function to fetch already sewn quantities for a specific variant\n  const fetchAlreadySewn = async (variantId, colorId) => {\n    // Update the loading state for this specific variant\n    setColorVariants(prev => prev.map(variant => variant.id === variantId ? {\n      ...variant,\n      loading: true\n    } : variant));\n    try {\n      const res = await axios.get(`http://localhost:8000/api/sewing/already-sewn/${colorId}/`);\n      setColorVariants(prev => prev.map(variant => variant.id === variantId ? {\n        ...variant,\n        alreadySewn: res.data,\n        loading: false\n      } : variant));\n    } catch (err) {\n      console.error(\"Error fetching already sewn quantities:\", err);\n      // If the endpoint doesn't exist yet, use a fallback of zeros\n      setColorVariants(prev => prev.map(variant => variant.id === variantId ? {\n        ...variant,\n        alreadySewn: {\n          xs: 0,\n          s: 0,\n          m: 0,\n          l: 0,\n          xl: 0\n        },\n        loading: false\n      } : variant));\n    }\n  };\n\n  // Function to add a new color variant\n  const addColorVariant = () => {\n    setColorVariants(prev => [...prev, {\n      id: Date.now(),\n      selectedColor: \"\",\n      alreadySewn: null,\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0,\n      damageCount: 0,\n      loading: false\n    }]);\n  };\n\n  // Function to remove a color variant\n  const removeColorVariant = variantId => {\n    if (colorVariants.length > 1) {\n      setColorVariants(prev => prev.filter(variant => variant.id !== variantId));\n    }\n  };\n\n  // Function to update a specific variant's field\n  const updateVariant = (variantId, field, value) => {\n    setColorVariants(prev => prev.map(variant => variant.id === variantId ? {\n      ...variant,\n      [field]: value\n    } : variant));\n  };\n\n  // Function to handle color selection for a variant\n  const handleColorSelection = (variantId, colorValue) => {\n    updateVariant(variantId, 'selectedColor', colorValue);\n    if (colorValue) {\n      fetchAlreadySewn(variantId, colorValue);\n    } else {\n      updateVariant(variantId, 'alreadySewn', null);\n    }\n  };\n\n  // Check if form is valid\n  useEffect(() => {\n    const hasProduct = !!selectedProduct;\n\n    // Check if all variants are valid\n    const allVariantsValid = colorVariants.every(variant => {\n      const hasColor = !!variant.selectedColor;\n      const hasSizes = parseInt(variant.xs || 0) > 0 || parseInt(variant.s || 0) > 0 || parseInt(variant.m || 0) > 0 || parseInt(variant.l || 0) > 0 || parseInt(variant.xl || 0) > 0;\n\n      // Calculate total sewn for validation\n      const totalSewnItems = parseInt(variant.xs || 0) + parseInt(variant.s || 0) + parseInt(variant.m || 0) + parseInt(variant.l || 0) + parseInt(variant.xl || 0);\n      const isDamageValid = parseInt(variant.damageCount || 0) <= totalSewnItems;\n\n      // Check if total sewn + damage exceeds available quantity\n      let isTotalValid = true;\n      if (variant.selectedColor && variant.alreadySewn) {\n        const option = productColors.find(opt => opt.value === variant.selectedColor);\n        if (option) {\n          const totalAvailable = option.totalCut - (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) - (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) - (variant.alreadySewn.xl || 0) - (variant.alreadySewn.damage_count || 0);\n          isTotalValid = totalSewnItems + parseInt(variant.damageCount || 0) <= totalAvailable;\n        }\n      }\n      return hasColor && hasSizes && isDamageValid && isTotalValid;\n    });\n    setFormValid(hasProduct && allVariantsValid && colorVariants.length > 0);\n  }, [selectedProduct, colorVariants, productColors]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage(\"\");\n    setIsSubmitting(true);\n    if (!selectedProduct) {\n      setMessage(\"Please select a Product.\");\n      setIsSubmitting(false);\n      return;\n    }\n\n    // Validate all variants\n    for (let i = 0; i < colorVariants.length; i++) {\n      const variant = colorVariants[i];\n      if (!variant.selectedColor) {\n        setMessage(`Please select a color for variant ${i + 1}.`);\n        setIsSubmitting(false);\n        return;\n      }\n      const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\n      if (!selectedOption) {\n        setMessage(`Selected color details not found for variant ${i + 1}.`);\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Validate each size individually for this variant\n      const parsedXs = parseInt(variant.xs || 0);\n      const parsedS = parseInt(variant.s || 0);\n      const parsedM = parseInt(variant.m || 0);\n      const parsedL = parseInt(variant.l || 0);\n      const parsedXl = parseInt(variant.xl || 0);\n      const parsedDamage = parseInt(variant.damageCount || 0);\n\n      // Check for negative values\n      if (parsedXs < 0 || parsedS < 0 || parsedM < 0 || parsedL < 0 || parsedXl < 0 || parsedDamage < 0) {\n        setMessage(`All quantities must be non-negative values for variant ${i + 1}.`);\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Validate that damage count doesn't exceed total sewn items\n      const totalSewnItems = parsedXs + parsedS + parsedM + parsedL + parsedXl;\n      if (parsedDamage > totalSewnItems) {\n        setMessage(`Damage count (${parsedDamage}) cannot exceed the total number of sewn items (${totalSewnItems}) for variant ${i + 1}.`);\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Check individual size limits with already sewn quantities\n      if (!variant.alreadySewn) {\n        setMessage(`Unable to validate quantities for variant ${i + 1}. Please try again.`);\n        setIsSubmitting(false);\n        return;\n      }\n      const alreadySewnXs = variant.alreadySewn.xs || 0;\n      const alreadySewnS = variant.alreadySewn.s || 0;\n      const alreadySewnM = variant.alreadySewn.m || 0;\n      const alreadySewnL = variant.alreadySewn.l || 0;\n      const alreadySewnXl = variant.alreadySewn.xl || 0;\n      if (parsedXs + alreadySewnXs > selectedOption.xs_cut) {\n        setMessage(`XS quantity (${parsedXs}) exceeds the available quantity (${selectedOption.xs_cut - alreadySewnXs}) for variant ${i + 1}. Already sewn: ${alreadySewnXs}`);\n        setIsSubmitting(false);\n        return;\n      }\n      if (parsedS + alreadySewnS > selectedOption.s_cut) {\n        setMessage(`S quantity (${parsedS}) exceeds the available quantity (${selectedOption.s_cut - alreadySewnS}) for variant ${i + 1}. Already sewn: ${alreadySewnS}`);\n        setIsSubmitting(false);\n        return;\n      }\n      if (parsedM + alreadySewnM > selectedOption.m_cut) {\n        setMessage(`M quantity (${parsedM}) exceeds the available quantity (${selectedOption.m_cut - alreadySewnM}) for variant ${i + 1}. Already sewn: ${alreadySewnM}`);\n        setIsSubmitting(false);\n        return;\n      }\n      if (parsedL + alreadySewnL > selectedOption.l_cut) {\n        setMessage(`L quantity (${parsedL}) exceeds the available quantity (${selectedOption.l_cut - alreadySewnL}) for variant ${i + 1}. Already sewn: ${alreadySewnL}`);\n        setIsSubmitting(false);\n        return;\n      }\n      if (parsedXl + alreadySewnXl > selectedOption.xl_cut) {\n        setMessage(`XL quantity (${parsedXl}) exceeds the available quantity (${selectedOption.xl_cut - alreadySewnXl}) for variant ${i + 1}. Already sewn: ${alreadySewnXl}`);\n        setIsSubmitting(false);\n        return;\n      }\n      const newDailyTotal = parsedXs + parsedS + parsedM + parsedL + parsedXl;\n      const alreadySewnTotal = alreadySewnXs + alreadySewnS + alreadySewnM + alreadySewnL + alreadySewnXl;\n      if (newDailyTotal + alreadySewnTotal > selectedOption.totalCut) {\n        setMessage(`The total sewing count (${newDailyTotal}) exceeds the available quantity (${selectedOption.totalCut - alreadySewnTotal}) for variant ${i + 1}. Already sewn: ${alreadySewnTotal}`);\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Validate that daily sewing count + damage count doesn't exceed available fabric variant count\n      const totalAlreadyDamage = variant.alreadySewn.damage_count || 0;\n      const totalAvailable = selectedOption.totalCut - alreadySewnTotal - totalAlreadyDamage;\n\n      // Check if daily sewing + damage exceeds available\n      if (newDailyTotal + parsedDamage > totalAvailable) {\n        setMessage(`The total of sewn items (${newDailyTotal}) plus damage count (${parsedDamage}) exceeds the available quantity (${totalAvailable}) for variant ${i + 1}.`);\n        setIsSubmitting(false);\n        return;\n      }\n    }\n\n    // Submit all variants\n    try {\n      const promises = colorVariants.map(variant => {\n        const payload = {\n          cutting_record_fabric: variant.selectedColor,\n          xs: parseInt(variant.xs || 0),\n          s: parseInt(variant.s || 0),\n          m: parseInt(variant.m || 0),\n          l: parseInt(variant.l || 0),\n          xl: parseInt(variant.xl || 0),\n          damage_count: parseInt(variant.damageCount || 0)\n        };\n        return axios.post(\"http://localhost:8000/api/sewing/daily-records/\", payload);\n      });\n      await Promise.all(promises);\n      setMessage(`✅ Daily sewing records added successfully for ${colorVariants.length} color variant${colorVariants.length > 1 ? 's' : ''}!`);\n      setSelectedProduct(\"\");\n      setProductColors([]);\n      setColorVariants([{\n        id: Date.now(),\n        selectedColor: \"\",\n        alreadySewn: null,\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0,\n        damageCount: 0,\n        loading: false\n      }]);\n      setIsSubmitting(false);\n    } catch (err) {\n      var _err$response;\n      console.error(\"Error adding daily sewing records:\", err);\n      let errorMessage = \"Error adding daily sewing records.\";\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && _err$response.data) {\n        errorMessage = typeof err.response.data === \"object\" ? Object.values(err.response.data).flat().join(\"\\n\") : err.response.data;\n      }\n      setMessage(errorMessage);\n      setIsSubmitting(false);\n    }\n  };\n  const ColourOption = ({\n    data,\n    innerRef,\n    innerProps\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: innerRef,\n    ...innerProps,\n    style: {\n      display: \"flex\",\n      alignItems: \"center\",\n      padding: \"8px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 20,\n        height: 20,\n        backgroundColor: data.color,\n        marginRight: 10,\n        border: \"1px solid #ccc\",\n        borderRadius: \"4px\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        flexDirection: \"column\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontWeight: \"500\",\n          fontSize: \"14px\"\n        },\n        children: data.fabricName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: \"12px\",\n          color: \"#666\"\n        },\n        children: data.colorName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 374,\n    columnNumber: 5\n  }, this);\n\n  // Calculate remaining quantities for a specific variant\n  const getRemainingQuantities = variant => {\n    if (!variant.selectedColor || !variant.alreadySewn) return null;\n    const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\n    if (!selectedOption) return null;\n\n    // Calculate total available after accounting for already sewn and damage\n    const totalCut = selectedOption.totalCut;\n    const totalAlreadySewn = (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) + (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) + (variant.alreadySewn.xl || 0);\n    const totalAlreadyDamaged = variant.alreadySewn.damage_count || 0;\n    const totalAvailable = totalCut - totalAlreadySewn - totalAlreadyDamaged;\n\n    // Calculate remaining per size\n    return {\n      xs: selectedOption.xs_cut - (variant.alreadySewn.xs || 0) - parseInt(variant.xs || 0),\n      s: selectedOption.s_cut - (variant.alreadySewn.s || 0) - parseInt(variant.s || 0),\n      m: selectedOption.m_cut - (variant.alreadySewn.m || 0) - parseInt(variant.m || 0),\n      l: selectedOption.l_cut - (variant.alreadySewn.l || 0) - parseInt(variant.l || 0),\n      xl: selectedOption.xl_cut - (variant.alreadySewn.xl || 0) - parseInt(variant.xl || 0),\n      total: totalAvailable - parseInt(variant.xs || 0) - parseInt(variant.s || 0) - parseInt(variant.m || 0) - parseInt(variant.l || 0) - parseInt(variant.xl || 0) - parseInt(variant.damageCount || 0)\n    };\n  };\n\n  // Helper functions for variant validation\n  const getVariantTotalSewn = variant => {\n    return parseInt(variant.xs || 0) + parseInt(variant.s || 0) + parseInt(variant.m || 0) + parseInt(variant.l || 0) + parseInt(variant.xl || 0);\n  };\n  const isVariantDamageExceeded = variant => {\n    return parseInt(variant.damageCount || 0) > getVariantTotalSewn(variant);\n  };\n  const isVariantTotalExceeded = variant => {\n    const selectedColorOption = productColors.find(opt => opt.value === variant.selectedColor);\n    return selectedColorOption && variant.alreadySewn ? getVariantTotalSewn(variant) + parseInt(variant.damageCount || 0) > selectedColorOption.totalCut - (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) - (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) - (variant.alreadySewn.xl || 0) - (variant.alreadySewn.damage_count || 0) : false;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(FaTshirt, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), \"Add Daily Sewing Record\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), message && message.startsWith(\"✅\") && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), message && !message.startsWith(\"✅\") && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: \"#D9EDFB\",\n          borderRadius: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            noValidate: true,\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Product (Cutting Record)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                    value: selectedProduct,\n                    onChange: e => setSelectedProduct(e.target.value),\n                    className: \"form-control shadow-sm\",\n                    style: {\n                      borderRadius: \"8px\",\n                      padding: \"10px\",\n                      transition: \"all 0.2s ease\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Product\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 23\n                    }, this), products.map(prod => {\n                      var _prod$fabric_definiti;\n                      return /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: prod.id,\n                        children: prod.product_name || `${(_prod$fabric_definiti = prod.fabric_definition_data) === null || _prod$fabric_definiti === void 0 ? void 0 : _prod$fabric_definiti.fabric_name} cut on ${prod.cutting_date}`\n                      }, prod.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 25\n                      }, this);\n                    })]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                    className: \"text-muted\",\n                    children: \"Select the product from cutting records\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"mb-0\",\n                  children: \"Color Variants\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-primary\",\n                  size: \"sm\",\n                  onClick: addColorVariant,\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this), \"More Variant\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), colorVariants.map((variant, index) => /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3 border\",\n                style: {\n                  backgroundColor: \"#f8f9fa\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"d-flex justify-content-between align-items-center bg-light\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: [\"Variant \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 25\n                  }, this), colorVariants.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => removeColorVariant(variant.id),\n                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Row, {\n                    children: /*#__PURE__*/_jsxDEV(Col, {\n                      md: 12,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Color\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 550,\n                            columnNumber: 43\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 550,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Select, {\n                          options: productColors,\n                          components: {\n                            Option: ColourOption\n                          },\n                          value: productColors.find(opt => opt.value === variant.selectedColor) || null,\n                          onChange: opt => handleColorSelection(variant.id, opt === null || opt === void 0 ? void 0 : opt.value),\n                          placeholder: \"Select Color\",\n                          isDisabled: !selectedProduct,\n                          styles: {\n                            control: provided => ({\n                              ...provided,\n                              borderColor: \"#ddd\",\n                              boxShadow: \"0 2px 5px rgba(0,0,0,0.1)\",\n                              borderRadius: \"8px\",\n                              \"&:hover\": {\n                                borderColor: \"#aaa\"\n                              },\n                              padding: \"5px\",\n                              transition: \"all 0.2s ease\"\n                            }),\n                            option: (provided, state) => ({\n                              ...provided,\n                              backgroundColor: state.isSelected ? \"#0d6efd\" : state.isFocused ? \"#e9ecef\" : \"white\",\n                              color: state.isSelected ? \"white\" : \"#333\",\n                              cursor: \"pointer\"\n                            })\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 551,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                          className: \"text-muted\",\n                          children: \"Select the color variant for this entry\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 578,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this), variant.selectedColor && /*#__PURE__*/_jsxDEV(Card, {\n                    className: \"mb-3 mt-3 border-0 shadow-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                      className: \"bg-light\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                          className: \"me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 589,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"Available Quantities\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 590,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 588,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                      children: variant.loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center py-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                          animation: \"border\",\n                          variant: \"primary\",\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 596,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-muted\",\n                          children: \"Loading quantities...\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 597,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"table-responsive\",\n                          children: /*#__PURE__*/_jsxDEV(\"table\", {\n                            className: \"table table-hover table-sm\",\n                            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                              className: \"table-light\",\n                              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                                  className: \"text-center\",\n                                  children: \"Size\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 605,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                                  className: \"text-center\",\n                                  children: \"Cut\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 606,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                                  className: \"text-center\",\n                                  children: \"Already Sewn\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 607,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                                  className: \"text-center\",\n                                  children: \"Available\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 608,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 604,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 603,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                              children: [[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map(size => {\n                                const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\n                                const sizeKey = size.toLowerCase();\n                                const cutKey = `${sizeKey}_cut`;\n                                const alreadySewnQty = variant.alreadySewn ? variant.alreadySewn[sizeKey] || 0 : 0;\n                                const availableQty = selectedOption ? Math.max(0, selectedOption[cutKey] - alreadySewnQty) : 0;\n                                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                                      bg: \"secondary\",\n                                      className: \"px-2 py-1\",\n                                      style: {\n                                        fontSize: \"0.75rem\"\n                                      },\n                                      children: size\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 625,\n                                      columnNumber: 49\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 624,\n                                    columnNumber: 47\n                                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                                      bg: \"secondary\",\n                                      pill: true,\n                                      className: \"px-2\",\n                                      style: {\n                                        fontSize: \"0.75rem\"\n                                      },\n                                      children: selectedOption ? selectedOption[cutKey] : 0\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 628,\n                                      columnNumber: 49\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 627,\n                                    columnNumber: 47\n                                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                                      bg: \"info\",\n                                      pill: true,\n                                      className: \"px-2\",\n                                      style: {\n                                        fontSize: \"0.75rem\"\n                                      },\n                                      children: alreadySewnQty\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 633,\n                                      columnNumber: 49\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 632,\n                                    columnNumber: 47\n                                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/_jsxDEV(Badge, {\n                                      bg: availableQty > 0 ? 'success' : 'danger',\n                                      pill: true,\n                                      className: \"px-2\",\n                                      style: {\n                                        fontSize: \"0.75rem\"\n                                      },\n                                      children: availableQty\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 638,\n                                      columnNumber: 49\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 637,\n                                    columnNumber: 47\n                                  }, this)]\n                                }, `avail-${variant.id}-${size}`, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 623,\n                                  columnNumber: 45\n                                }, this);\n                              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                                className: \"table-light\",\n                                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                                    bg: \"warning\",\n                                    text: \"dark\",\n                                    className: \"px-2 py-1\",\n                                    style: {\n                                      fontSize: \"0.75rem\"\n                                    },\n                                    children: \"Damage\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 654,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 653,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: \"-\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 656,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                                    bg: \"warning\",\n                                    text: \"dark\",\n                                    pill: true,\n                                    className: \"px-2\",\n                                    style: {\n                                      fontSize: \"0.75rem\"\n                                    },\n                                    children: variant.alreadySewn ? variant.alreadySewn.damage_count || 0 : 0\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 658,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 657,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: \"-\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 662,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 652,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                                className: \"table-primary\",\n                                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    style: {\n                                      fontSize: \"0.8rem\"\n                                    },\n                                    children: \"TOTAL\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 668,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 667,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                                    bg: \"primary\",\n                                    pill: true,\n                                    className: \"px-2\",\n                                    style: {\n                                      fontSize: \"0.75rem\"\n                                    },\n                                    children: (() => {\n                                      const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\n                                      return selectedOption ? selectedOption.totalCut : 0;\n                                    })()\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 671,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 670,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: /*#__PURE__*/_jsxDEV(Badge, {\n                                    bg: \"primary\",\n                                    pill: true,\n                                    className: \"px-2\",\n                                    style: {\n                                      fontSize: \"0.75rem\"\n                                    },\n                                    children: variant.alreadySewn ? (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) + (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) + (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0) : 0\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 679,\n                                    columnNumber: 45\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 678,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  className: \"text-center\",\n                                  children: (() => {\n                                    const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\n                                    const remainingQuantities = getRemainingQuantities(variant);\n                                    return selectedOption && variant.alreadySewn && /*#__PURE__*/_jsxDEV(Badge, {\n                                      bg: remainingQuantities && remainingQuantities.total >= 0 ? 'success' : 'danger',\n                                      pill: true,\n                                      className: \"px-2\",\n                                      style: {\n                                        fontSize: \"0.75rem\"\n                                      },\n                                      children: Math.max(0, selectedOption.totalCut - ((variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) + (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) + (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0)))\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 691,\n                                      columnNumber: 49\n                                    }, this);\n                                  })()\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 686,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 666,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 611,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-2 p-2 bg-light rounded border\",\n                          children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted d-block mb-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                              className: \"me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 712,\n                              columnNumber: 39\n                            }, this), \"Available quantities for sewing in each size.\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 711,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                            className: \"text-muted d-block\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Important:\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 716,\n                              columnNumber: 39\n                            }, this), \" Total sewn + damage cannot exceed available quantity.\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 715,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 710,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mt-3 mb-2\",\n                    children: \"Size Quantities\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 725,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    className: \"mb-3\",\n                    children: [[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map(size => {\n                      const sizeKey = size.toLowerCase();\n                      const remainingQuantities = getRemainingQuantities(variant);\n                      const isExceeded = remainingQuantities && remainingQuantities[sizeKey] < 0;\n                      return /*#__PURE__*/_jsxDEV(Col, {\n                        xs: 6,\n                        sm: 4,\n                        md: 2,\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"text-center d-block\",\n                            style: {\n                              fontSize: \"0.8rem\"\n                            },\n                            children: size\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 735,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"number\",\n                            min: \"0\",\n                            size: \"sm\",\n                            value: variant[sizeKey],\n                            onChange: e => {\n                              // Ensure value is not negative\n                              const val = Math.max(0, parseInt(e.target.value || 0));\n                              updateVariant(variant.id, sizeKey, val);\n                            },\n                            className: `text-center ${isExceeded ? 'border-danger' : ''}`,\n                            disabled: !variant.selectedColor\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 736,\n                            columnNumber: 35\n                          }, this), isExceeded && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-danger small mt-1 text-center\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                              className: \"me-1\",\n                              size: 10\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 751,\n                              columnNumber: 39\n                            }, this), \"Exceeds limit\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 750,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 734,\n                          columnNumber: 33\n                        }, this)\n                      }, `${variant.id}-${size}`, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 31\n                      }, this);\n                    }), /*#__PURE__*/_jsxDEV(Col, {\n                      xs: 6,\n                      sm: 4,\n                      md: 2,\n                      className: \"mb-2\",\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"text-center d-block\",\n                          style: {\n                            fontSize: \"0.8rem\"\n                          },\n                          children: \"Damage\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 762,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"number\",\n                          min: \"0\",\n                          size: \"sm\",\n                          value: variant.damageCount,\n                          onChange: e => {\n                            // Ensure value is not negative\n                            const val = Math.max(0, parseInt(e.target.value || 0));\n                            updateVariant(variant.id, 'damageCount', val);\n                          },\n                          className: `text-center ${isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? 'border-danger' : ''}`,\n                          disabled: !variant.selectedColor\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 763,\n                          columnNumber: 31\n                        }, this), isVariantDamageExceeded(variant) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-danger small mt-1 text-center\",\n                          style: {\n                            fontSize: \"0.7rem\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"me-1\",\n                            size: 10\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 778,\n                            columnNumber: 35\n                          }, this), \"Exceeds total sewn\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 33\n                        }, this), !isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-danger small mt-1 text-center\",\n                          style: {\n                            fontSize: \"0.7rem\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"me-1\",\n                            size: 10\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 784,\n                            columnNumber: 35\n                          }, this), \"Exceeds available\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 761,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 760,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 25\n                  }, this), getVariantTotalSewn(variant) > 0 && /*#__PURE__*/_jsxDEV(Card, {\n                    className: \"border-0 mb-3\",\n                    style: {\n                      backgroundColor: \"#e8f4fe\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                      className: \"py-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex flex-column\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"me-2\",\n                            style: {\n                              fontSize: \"0.8rem\"\n                            },\n                            children: \"Total Quantities:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 798,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"primary\",\n                            className: \"me-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"XS: \", variant.xs]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 799,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"primary\",\n                            className: \"me-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"S: \", variant.s]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 800,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"primary\",\n                            className: \"me-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"M: \", variant.m]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 801,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"primary\",\n                            className: \"me-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"L: \", variant.l]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 802,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"primary\",\n                            className: \"me-1\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"XL: \", variant.xl]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 803,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"success\",\n                            className: \"ms-2\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [\"Total: \", getVariantTotalSewn(variant)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 804,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 797,\n                          columnNumber: 33\n                        }, this), variant.damageCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"me-2\",\n                            style: {\n                              fontSize: \"0.8rem\"\n                            },\n                            children: \"Damage Count:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 808,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? \"danger\" : \"warning\",\n                            text: isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? \"white\" : \"dark\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [variant.damageCount, \" \", (isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant)) && /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                              className: \"ms-1\",\n                              size: 10\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 812,\n                              columnNumber: 135\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 809,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                            className: \"mx-2\",\n                            style: {\n                              fontSize: \"0.8rem\"\n                            },\n                            children: \"Good Items:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 814,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"success\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: Math.max(0, getVariantTotalSewn(variant) - variant.damageCount)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 815,\n                            columnNumber: 37\n                          }, this), isVariantDamageExceeded(variant) && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-danger ms-2 small\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                              className: \"me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 818,\n                              columnNumber: 41\n                            }, this), \"Damage count cannot exceed total sewn items\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 817,\n                            columnNumber: 39\n                          }, this), !isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-danger ms-2 small\",\n                            style: {\n                              fontSize: \"0.7rem\"\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                              className: \"me-1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 824,\n                              columnNumber: 41\n                            }, this), \"Total sewn + damage exceeds available quantity\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 823,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 807,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 796,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 23\n                }, this)]\n              }, variant.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                disabled: !formValid || isSubmitting,\n                className: \"px-5\",\n                children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 23\n                  }, this), \"Submitting...\"]\n                }, void 0, true) : `Submit Daily Sewing Records (${colorVariants.length} variant${colorVariants.length > 1 ? 's' : ''})`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 15\n            }, this), !formValid && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-3\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 21\n                }, this), !selectedProduct ? \"Please select a product\" : colorVariants.some(v => !v.selectedColor) ? \"Please select colors for all variants\" : colorVariants.every(v => getVariantTotalSewn(v) === 0) ? \"Please enter at least one size quantity in any variant\" : colorVariants.some(v => isVariantDamageExceeded(v)) ? \"Some variants have damage count exceeding total sewn items\" : colorVariants.some(v => isVariantTotalExceeded(v)) ? \"Some variants exceed available quantities\" : \"Please check your inputs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddDailySewingRecord, \"koBG5tkXYXS1aq+LQeBniDJWlAQ=\");\n_c = AddDailySewingRecord;\nexport default AddDailySewingRecord;\nvar _c;\n$RefreshReg$(_c, \"AddDailySewingRecord\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "RoleBasedNavBar", "Select", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Badge", "FaInfoCircle", "FaTshirt", "FaCheck", "FaExclamationTriangle", "FaClipboardCheck", "FaPlus", "FaTrash", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddDailySewingRecord", "_s", "products", "setProducts", "selectedProduct", "setSelectedProduct", "productColors", "setProductColors", "loading", "setLoading", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "colorVariants", "setColorVariants", "id", "Date", "now", "selectedColor", "alreadySewn", "xs", "s", "m", "l", "xl", "damageCount", "message", "setMessage", "isSubmitting", "setIsSubmitting", "formValid", "setFormValid", "handleResize", "addEventListener", "removeEventListener", "get", "then", "res", "data", "catch", "err", "console", "error", "product", "find", "p", "parseInt", "details", "options", "map", "detail", "_detail$fabric_varian", "_detail$fabric_varian2", "_detail$fabric_varian3", "_detail$fabric_varian4", "_detail$fabric_varian5", "totalCut", "fabricName", "fabric_variant_data", "fabric_definition_data", "fabric_name", "colorName", "color_name", "color", "value", "label", "xs_cut", "s_cut", "m_cut", "l_cut", "xl_cut", "fetchAlreadySewn", "variantId", "colorId", "prev", "variant", "addColorVariant", "removeColorVariant", "length", "filter", "updateVariant", "field", "handleColorSelection", "colorValue", "hasProduct", "allVariantsValid", "every", "hasColor", "hasSizes", "totalSewnItems", "isDamageValid", "isTotalValid", "option", "opt", "totalAvailable", "damage_count", "handleSubmit", "e", "preventDefault", "i", "selectedOption", "parsedXs", "parsedS", "parsedM", "parsedL", "parsedXl", "parsedDamage", "alreadySewnXs", "alreadySewnS", "alreadySewnM", "alreadySewnL", "alreadySewnXl", "newDailyTotal", "alreadySewnTotal", "totalAlreadyDamage", "promises", "payload", "cutting_record_fabric", "post", "Promise", "all", "_err$response", "errorMessage", "response", "Object", "values", "flat", "join", "ColourOption", "innerRef", "innerProps", "ref", "style", "display", "alignItems", "padding", "children", "width", "height", "backgroundColor", "marginRight", "border", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexDirection", "fontWeight", "fontSize", "getRemainingQuantities", "totalAlreadySewn", "totalAlreadyDamaged", "total", "getVariantTotalSewn", "isVariantDamageExceeded", "isVariantTotalExceeded", "selectedColorOption", "marginLeft", "transition", "className", "startsWith", "size", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "onChange", "target", "prod", "_prod$fabric_definiti", "product_name", "cutting_date", "Text", "onClick", "index", "Header", "components", "Option", "placeholder", "isDisabled", "styles", "control", "provided", "borderColor", "boxShadow", "state", "isSelected", "isFocused", "cursor", "animation", "sizeKey", "toLowerCase", "<PERSON><PERSON><PERSON>", "alreadySewnQty", "availableQty", "Math", "max", "bg", "pill", "text", "remainingQuantities", "isExceeded", "sm", "Control", "type", "min", "val", "disabled", "as", "role", "some", "v", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddDailySewingRecord.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport Select from \"react-select\";\r\nimport { Row, Col, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, Badge } from \"react-bootstrap\";\r\nimport { FaInfoCircle, FaTshirt, FaCheck, FaExclamationTriangle, FaClipboardCheck, FaPlus, FaTrash } from \"react-icons/fa\";\r\n\r\nconst AddDailySewingRecord = () => {\r\n  const [products, setProducts] = useState([]);\r\n  const [selectedProduct, setSelectedProduct] = useState(\"\");\r\n  const [productColors, setProductColors] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768); // Track sidebar state\r\n\r\n  // Array to store multiple color variants\r\n  const [colorVariants, setColorVariants] = useState([{\r\n    id: Date.now(),\r\n    selectedColor: \"\",\r\n    alreadySewn: null,\r\n    xs: 0,\r\n    s: 0,\r\n    m: 0,\r\n    l: 0,\r\n    xl: 0,\r\n    damageCount: 0,\r\n    loading: false\r\n  }]);\r\n\r\n  const [message, setMessage] = useState(\"\");\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [formValid, setFormValid] = useState(false);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    axios\r\n      .get(\"http://localhost:8000/api/cutting/cutting-records/\")\r\n      .then((res) => setProducts(res.data))\r\n      .catch((err) => console.error(\"Error fetching products:\", err));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (selectedProduct) {\r\n      const product = products.find((p) => p.id === parseInt(selectedProduct));\r\n      if (product?.details) {\r\n        const options = product.details.map((detail) => {\r\n          const totalCut =\r\n            (detail.xs || 0) +\r\n            (detail.s || 0) +\r\n            (detail.m || 0) +\r\n            (detail.l || 0) +\r\n            (detail.xl || 0);\r\n\r\n          // Get fabric name and color name for better identification\r\n          const fabricName = detail.fabric_variant_data?.fabric_definition_data?.fabric_name || \"Unknown Fabric\";\r\n          const colorName = detail.fabric_variant_data?.color_name || detail.fabric_variant_data?.color || \"N/A\";\r\n\r\n          return {\r\n            value: detail.id,\r\n            label: `${fabricName} - ${colorName}`,\r\n            fabricName: fabricName,\r\n            colorName: colorName,\r\n            color: detail.fabric_variant_data?.color || \"#ffffff\",\r\n            totalCut,\r\n            // Store individual size quantities for validation\r\n            xs_cut: detail.xs || 0,\r\n            s_cut: detail.s || 0,\r\n            m_cut: detail.m || 0,\r\n            l_cut: detail.l || 0,\r\n            xl_cut: detail.xl || 0,\r\n          };\r\n        });\r\n        setProductColors(options);\r\n      } else {\r\n        setProductColors([]);\r\n      }\r\n      // Reset all color variants when product changes\r\n      setColorVariants([{\r\n        id: Date.now(),\r\n        selectedColor: \"\",\r\n        alreadySewn: null,\r\n        xs: 0,\r\n        s: 0,\r\n        m: 0,\r\n        l: 0,\r\n        xl: 0,\r\n        damageCount: 0,\r\n        loading: false\r\n      }]);\r\n    }\r\n  }, [selectedProduct, products]);\r\n\r\n  // Function to fetch already sewn quantities for a specific variant\r\n  const fetchAlreadySewn = async (variantId, colorId) => {\r\n    // Update the loading state for this specific variant\r\n    setColorVariants(prev => prev.map(variant =>\r\n      variant.id === variantId\r\n        ? { ...variant, loading: true }\r\n        : variant\r\n    ));\r\n\r\n    try {\r\n      const res = await axios.get(`http://localhost:8000/api/sewing/already-sewn/${colorId}/`);\r\n      setColorVariants(prev => prev.map(variant =>\r\n        variant.id === variantId\r\n          ? { ...variant, alreadySewn: res.data, loading: false }\r\n          : variant\r\n      ));\r\n    } catch (err) {\r\n      console.error(\"Error fetching already sewn quantities:\", err);\r\n      // If the endpoint doesn't exist yet, use a fallback of zeros\r\n      setColorVariants(prev => prev.map(variant =>\r\n        variant.id === variantId\r\n          ? {\r\n              ...variant,\r\n              alreadySewn: { xs: 0, s: 0, m: 0, l: 0, xl: 0 },\r\n              loading: false\r\n            }\r\n          : variant\r\n      ));\r\n    }\r\n  };\r\n\r\n  // Function to add a new color variant\r\n  const addColorVariant = () => {\r\n    setColorVariants(prev => [...prev, {\r\n      id: Date.now(),\r\n      selectedColor: \"\",\r\n      alreadySewn: null,\r\n      xs: 0,\r\n      s: 0,\r\n      m: 0,\r\n      l: 0,\r\n      xl: 0,\r\n      damageCount: 0,\r\n      loading: false\r\n    }]);\r\n  };\r\n\r\n  // Function to remove a color variant\r\n  const removeColorVariant = (variantId) => {\r\n    if (colorVariants.length > 1) {\r\n      setColorVariants(prev => prev.filter(variant => variant.id !== variantId));\r\n    }\r\n  };\r\n\r\n  // Function to update a specific variant's field\r\n  const updateVariant = (variantId, field, value) => {\r\n    setColorVariants(prev => prev.map(variant =>\r\n      variant.id === variantId\r\n        ? { ...variant, [field]: value }\r\n        : variant\r\n    ));\r\n  };\r\n\r\n  // Function to handle color selection for a variant\r\n  const handleColorSelection = (variantId, colorValue) => {\r\n    updateVariant(variantId, 'selectedColor', colorValue);\r\n    if (colorValue) {\r\n      fetchAlreadySewn(variantId, colorValue);\r\n    } else {\r\n      updateVariant(variantId, 'alreadySewn', null);\r\n    }\r\n  };\r\n\r\n  // Check if form is valid\r\n  useEffect(() => {\r\n    const hasProduct = !!selectedProduct;\r\n\r\n    // Check if all variants are valid\r\n    const allVariantsValid = colorVariants.every(variant => {\r\n      const hasColor = !!variant.selectedColor;\r\n      const hasSizes = parseInt(variant.xs || 0) > 0 ||\r\n                      parseInt(variant.s || 0) > 0 ||\r\n                      parseInt(variant.m || 0) > 0 ||\r\n                      parseInt(variant.l || 0) > 0 ||\r\n                      parseInt(variant.xl || 0) > 0;\r\n\r\n      // Calculate total sewn for validation\r\n      const totalSewnItems = parseInt(variant.xs || 0) + parseInt(variant.s || 0) +\r\n                            parseInt(variant.m || 0) + parseInt(variant.l || 0) +\r\n                            parseInt(variant.xl || 0);\r\n      const isDamageValid = parseInt(variant.damageCount || 0) <= totalSewnItems;\r\n\r\n      // Check if total sewn + damage exceeds available quantity\r\n      let isTotalValid = true;\r\n      if (variant.selectedColor && variant.alreadySewn) {\r\n        const option = productColors.find(opt => opt.value === variant.selectedColor);\r\n        if (option) {\r\n          const totalAvailable = option.totalCut -\r\n            (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) -\r\n            (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) -\r\n            (variant.alreadySewn.xl || 0) - (variant.alreadySewn.damage_count || 0);\r\n          isTotalValid = (totalSewnItems + parseInt(variant.damageCount || 0)) <= totalAvailable;\r\n        }\r\n      }\r\n\r\n      return hasColor && hasSizes && isDamageValid && isTotalValid;\r\n    });\r\n\r\n    setFormValid(hasProduct && allVariantsValid && colorVariants.length > 0);\r\n  }, [selectedProduct, colorVariants, productColors]);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setMessage(\"\");\r\n    setIsSubmitting(true);\r\n\r\n    if (!selectedProduct) {\r\n      setMessage(\"Please select a Product.\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    // Validate all variants\r\n    for (let i = 0; i < colorVariants.length; i++) {\r\n      const variant = colorVariants[i];\r\n\r\n      if (!variant.selectedColor) {\r\n        setMessage(`Please select a color for variant ${i + 1}.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const selectedOption = productColors.find(\r\n        (opt) => opt.value === variant.selectedColor\r\n      );\r\n\r\n      if (!selectedOption) {\r\n        setMessage(`Selected color details not found for variant ${i + 1}.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Validate each size individually for this variant\r\n      const parsedXs = parseInt(variant.xs || 0);\r\n      const parsedS = parseInt(variant.s || 0);\r\n      const parsedM = parseInt(variant.m || 0);\r\n      const parsedL = parseInt(variant.l || 0);\r\n      const parsedXl = parseInt(variant.xl || 0);\r\n      const parsedDamage = parseInt(variant.damageCount || 0);\r\n\r\n      // Check for negative values\r\n      if (parsedXs < 0 || parsedS < 0 || parsedM < 0 || parsedL < 0 || parsedXl < 0 || parsedDamage < 0) {\r\n        setMessage(`All quantities must be non-negative values for variant ${i + 1}.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Validate that damage count doesn't exceed total sewn items\r\n      const totalSewnItems = parsedXs + parsedS + parsedM + parsedL + parsedXl;\r\n      if (parsedDamage > totalSewnItems) {\r\n        setMessage(`Damage count (${parsedDamage}) cannot exceed the total number of sewn items (${totalSewnItems}) for variant ${i + 1}.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Check individual size limits with already sewn quantities\r\n      if (!variant.alreadySewn) {\r\n        setMessage(`Unable to validate quantities for variant ${i + 1}. Please try again.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const alreadySewnXs = variant.alreadySewn.xs || 0;\r\n      const alreadySewnS = variant.alreadySewn.s || 0;\r\n      const alreadySewnM = variant.alreadySewn.m || 0;\r\n      const alreadySewnL = variant.alreadySewn.l || 0;\r\n      const alreadySewnXl = variant.alreadySewn.xl || 0;\r\n\r\n      if (parsedXs + alreadySewnXs > selectedOption.xs_cut) {\r\n        setMessage(`XS quantity (${parsedXs}) exceeds the available quantity (${selectedOption.xs_cut - alreadySewnXs}) for variant ${i + 1}. Already sewn: ${alreadySewnXs}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n      if (parsedS + alreadySewnS > selectedOption.s_cut) {\r\n        setMessage(`S quantity (${parsedS}) exceeds the available quantity (${selectedOption.s_cut - alreadySewnS}) for variant ${i + 1}. Already sewn: ${alreadySewnS}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n      if (parsedM + alreadySewnM > selectedOption.m_cut) {\r\n        setMessage(`M quantity (${parsedM}) exceeds the available quantity (${selectedOption.m_cut - alreadySewnM}) for variant ${i + 1}. Already sewn: ${alreadySewnM}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n      if (parsedL + alreadySewnL > selectedOption.l_cut) {\r\n        setMessage(`L quantity (${parsedL}) exceeds the available quantity (${selectedOption.l_cut - alreadySewnL}) for variant ${i + 1}. Already sewn: ${alreadySewnL}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n      if (parsedXl + alreadySewnXl > selectedOption.xl_cut) {\r\n        setMessage(`XL quantity (${parsedXl}) exceeds the available quantity (${selectedOption.xl_cut - alreadySewnXl}) for variant ${i + 1}. Already sewn: ${alreadySewnXl}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const newDailyTotal = parsedXs + parsedS + parsedM + parsedL + parsedXl;\r\n      const alreadySewnTotal = alreadySewnXs + alreadySewnS + alreadySewnM + alreadySewnL + alreadySewnXl;\r\n\r\n      if (newDailyTotal + alreadySewnTotal > selectedOption.totalCut) {\r\n        setMessage(`The total sewing count (${newDailyTotal}) exceeds the available quantity (${selectedOption.totalCut - alreadySewnTotal}) for variant ${i + 1}. Already sewn: ${alreadySewnTotal}`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Validate that daily sewing count + damage count doesn't exceed available fabric variant count\r\n      const totalAlreadyDamage = variant.alreadySewn.damage_count || 0;\r\n      const totalAvailable = selectedOption.totalCut - alreadySewnTotal - totalAlreadyDamage;\r\n\r\n      // Check if daily sewing + damage exceeds available\r\n      if (newDailyTotal + parsedDamage > totalAvailable) {\r\n        setMessage(`The total of sewn items (${newDailyTotal}) plus damage count (${parsedDamage}) exceeds the available quantity (${totalAvailable}) for variant ${i + 1}.`);\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Submit all variants\r\n    try {\r\n      const promises = colorVariants.map(variant => {\r\n        const payload = {\r\n          cutting_record_fabric: variant.selectedColor,\r\n          xs: parseInt(variant.xs || 0),\r\n          s: parseInt(variant.s || 0),\r\n          m: parseInt(variant.m || 0),\r\n          l: parseInt(variant.l || 0),\r\n          xl: parseInt(variant.xl || 0),\r\n          damage_count: parseInt(variant.damageCount || 0),\r\n        };\r\n        return axios.post(\"http://localhost:8000/api/sewing/daily-records/\", payload);\r\n      });\r\n\r\n      await Promise.all(promises);\r\n\r\n      setMessage(`✅ Daily sewing records added successfully for ${colorVariants.length} color variant${colorVariants.length > 1 ? 's' : ''}!`);\r\n      setSelectedProduct(\"\");\r\n      setProductColors([]);\r\n      setColorVariants([{\r\n        id: Date.now(),\r\n        selectedColor: \"\",\r\n        alreadySewn: null,\r\n        xs: 0,\r\n        s: 0,\r\n        m: 0,\r\n        l: 0,\r\n        xl: 0,\r\n        damageCount: 0,\r\n        loading: false\r\n      }]);\r\n      setIsSubmitting(false);\r\n    } catch (err) {\r\n      console.error(\"Error adding daily sewing records:\", err);\r\n      let errorMessage = \"Error adding daily sewing records.\";\r\n      if (err.response?.data) {\r\n        errorMessage =\r\n          typeof err.response.data === \"object\"\r\n            ? Object.values(err.response.data).flat().join(\"\\n\")\r\n            : err.response.data;\r\n      }\r\n      setMessage(errorMessage);\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const ColourOption = ({ data, innerRef, innerProps }) => (\r\n    <div\r\n      ref={innerRef}\r\n      {...innerProps}\r\n      style={{ display: \"flex\", alignItems: \"center\", padding: \"8px\" }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: 20,\r\n          height: 20,\r\n          backgroundColor: data.color,\r\n          marginRight: 10,\r\n          border: \"1px solid #ccc\",\r\n          borderRadius: \"4px\"\r\n        }}\r\n      />\r\n      <div style={{ display: \"flex\", flexDirection: \"column\" }}>\r\n        <span style={{ fontWeight: \"500\", fontSize: \"14px\" }}>\r\n          {data.fabricName}\r\n        </span>\r\n        <span style={{ fontSize: \"12px\", color: \"#666\" }}>\r\n          {data.colorName}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Calculate remaining quantities for a specific variant\r\n  const getRemainingQuantities = (variant) => {\r\n    if (!variant.selectedColor || !variant.alreadySewn) return null;\r\n\r\n    const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\r\n    if (!selectedOption) return null;\r\n\r\n    // Calculate total available after accounting for already sewn and damage\r\n    const totalCut = selectedOption.totalCut;\r\n    const totalAlreadySewn = (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +\r\n                            (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +\r\n                            (variant.alreadySewn.xl || 0);\r\n    const totalAlreadyDamaged = variant.alreadySewn.damage_count || 0;\r\n    const totalAvailable = totalCut - totalAlreadySewn - totalAlreadyDamaged;\r\n\r\n    // Calculate remaining per size\r\n    return {\r\n      xs: selectedOption.xs_cut - (variant.alreadySewn.xs || 0) - parseInt(variant.xs || 0),\r\n      s: selectedOption.s_cut - (variant.alreadySewn.s || 0) - parseInt(variant.s || 0),\r\n      m: selectedOption.m_cut - (variant.alreadySewn.m || 0) - parseInt(variant.m || 0),\r\n      l: selectedOption.l_cut - (variant.alreadySewn.l || 0) - parseInt(variant.l || 0),\r\n      xl: selectedOption.xl_cut - (variant.alreadySewn.xl || 0) - parseInt(variant.xl || 0),\r\n      total: totalAvailable - parseInt(variant.xs || 0) - parseInt(variant.s || 0) -\r\n             parseInt(variant.m || 0) - parseInt(variant.l || 0) - parseInt(variant.xl || 0) -\r\n             parseInt(variant.damageCount || 0)\r\n    };\r\n  };\r\n\r\n  // Helper functions for variant validation\r\n  const getVariantTotalSewn = (variant) => {\r\n    return parseInt(variant.xs || 0) + parseInt(variant.s || 0) + parseInt(variant.m || 0) +\r\n           parseInt(variant.l || 0) + parseInt(variant.xl || 0);\r\n  };\r\n\r\n  const isVariantDamageExceeded = (variant) => {\r\n    return parseInt(variant.damageCount || 0) > getVariantTotalSewn(variant);\r\n  };\r\n\r\n  const isVariantTotalExceeded = (variant) => {\r\n    const selectedColorOption = productColors.find(opt => opt.value === variant.selectedColor);\r\n    return selectedColorOption && variant.alreadySewn ?\r\n      (getVariantTotalSewn(variant) + parseInt(variant.damageCount || 0)) >\r\n      (selectedColorOption.totalCut - (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) -\r\n       (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) - (variant.alreadySewn.xl || 0) -\r\n       (variant.alreadySewn.damage_count || 0))\r\n      : false;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <FaTshirt className=\"me-2\" />\r\n          Add Daily Sewing Record\r\n        </h2>\r\n\r\n        {message && message.startsWith(\"✅\") && (\r\n          <Alert\r\n            variant=\"success\"\r\n            className=\"d-flex align-items-center\"\r\n          >\r\n            <FaCheck className=\"me-2\" size={20} />\r\n            <div>{message}</div>\r\n          </Alert>\r\n        )}\r\n\r\n        {message && !message.startsWith(\"✅\") && (\r\n          <Alert\r\n            variant=\"danger\"\r\n            className=\"d-flex align-items-center\"\r\n          >\r\n            <FaExclamationTriangle className=\"me-2\" size={20} />\r\n            <div>{message}</div>\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={12}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product (Cutting Record)</strong></Form.Label>\r\n                    <Form.Select\r\n                      value={selectedProduct}\r\n                      onChange={(e) => setSelectedProduct(e.target.value)}\r\n                      className=\"form-control shadow-sm\"\r\n                      style={{\r\n                        borderRadius: \"8px\",\r\n                        padding: \"10px\",\r\n                        transition: \"all 0.2s ease\"\r\n                      }}\r\n                    >\r\n                      <option value=\"\">Select Product</option>\r\n                      {products.map((prod) => (\r\n                        <option key={prod.id} value={prod.id}>\r\n                          {prod.product_name ||\r\n                            `${prod.fabric_definition_data?.fabric_name} cut on ${prod.cutting_date}`}\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                    <Form.Text className=\"text-muted\">\r\n                      Select the product from cutting records\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* Color Variants Section */}\r\n              {selectedProduct && (\r\n                <div className=\"mt-4\">\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                    <h4 className=\"mb-0\">Color Variants</h4>\r\n                    <Button\r\n                      variant=\"outline-primary\"\r\n                      size=\"sm\"\r\n                      onClick={addColorVariant}\r\n                      className=\"d-flex align-items-center\"\r\n                    >\r\n                      <FaPlus className=\"me-1\" />\r\n                      More Variant\r\n                    </Button>\r\n                  </div>\r\n\r\n                  {colorVariants.map((variant, index) => (\r\n                    <Card key={variant.id} className=\"mb-3 border\" style={{ backgroundColor: \"#f8f9fa\" }}>\r\n                      <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                        <h6 className=\"mb-0\">Variant {index + 1}</h6>\r\n                        {colorVariants.length > 1 && (\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => removeColorVariant(variant.id)}\r\n                          >\r\n                            <FaTrash />\r\n                          </Button>\r\n                        )}\r\n                      </Card.Header>\r\n                      <Card.Body>\r\n                        <Row>\r\n                          <Col md={12}>\r\n                            <Form.Group className=\"mb-3\">\r\n                              <Form.Label><strong>Color</strong></Form.Label>\r\n                              <Select\r\n                                options={productColors}\r\n                                components={{ Option: ColourOption }}\r\n                                value={\r\n                                  productColors.find((opt) => opt.value === variant.selectedColor) || null\r\n                                }\r\n                                onChange={(opt) => handleColorSelection(variant.id, opt?.value)}\r\n                                placeholder=\"Select Color\"\r\n                                isDisabled={!selectedProduct}\r\n                                styles={{\r\n                                  control: (provided) => ({\r\n                                    ...provided,\r\n                                    borderColor: \"#ddd\",\r\n                                    boxShadow: \"0 2px 5px rgba(0,0,0,0.1)\",\r\n                                    borderRadius: \"8px\",\r\n                                    \"&:hover\": { borderColor: \"#aaa\" },\r\n                                    padding: \"5px\",\r\n                                    transition: \"all 0.2s ease\"\r\n                                  }),\r\n                                  option: (provided, state) => ({\r\n                                    ...provided,\r\n                                    backgroundColor: state.isSelected ? \"#0d6efd\" : state.isFocused ? \"#e9ecef\" : \"white\",\r\n                                    color: state.isSelected ? \"white\" : \"#333\",\r\n                                    cursor: \"pointer\"\r\n                                  })\r\n                                }}\r\n                              />\r\n                              <Form.Text className=\"text-muted\">\r\n                                Select the color variant for this entry\r\n                              </Form.Text>\r\n                            </Form.Group>\r\n                          </Col>\r\n                        </Row>\r\n\r\n                        {variant.selectedColor && (\r\n                          <Card className=\"mb-3 mt-3 border-0 shadow-sm\">\r\n                            <Card.Header className=\"bg-light\">\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <FaClipboardCheck className=\"me-2 text-primary\" />\r\n                                <h6 className=\"mb-0\">Available Quantities</h6>\r\n                              </div>\r\n                            </Card.Header>\r\n                            <Card.Body>\r\n                              {variant.loading ? (\r\n                                <div className=\"text-center py-3\">\r\n                                  <Spinner animation=\"border\" variant=\"primary\" className=\"me-2\" />\r\n                                  <span className=\"text-muted\">Loading quantities...</span>\r\n                                </div>\r\n                              ) : (\r\n                                <>\r\n                                  <div className=\"table-responsive\">\r\n                                    <table className=\"table table-hover table-sm\">\r\n                                      <thead className=\"table-light\">\r\n                                        <tr>\r\n                                          <th className=\"text-center\">Size</th>\r\n                                          <th className=\"text-center\">Cut</th>\r\n                                          <th className=\"text-center\">Already Sewn</th>\r\n                                          <th className=\"text-center\">Available</th>\r\n                                        </tr>\r\n                                      </thead>\r\n                                      <tbody>\r\n                                        {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size) => {\r\n                                          const selectedOption = productColors.find(\r\n                                            (opt) => opt.value === variant.selectedColor\r\n                                          );\r\n                                          const sizeKey = size.toLowerCase();\r\n                                          const cutKey = `${sizeKey}_cut`;\r\n                                          const alreadySewnQty = variant.alreadySewn ? variant.alreadySewn[sizeKey] || 0 : 0;\r\n                                          const availableQty = selectedOption ?\r\n                                            Math.max(0, selectedOption[cutKey] - alreadySewnQty) : 0;\r\n\r\n                                          return (\r\n                                            <tr key={`avail-${variant.id}-${size}`}>\r\n                                              <td className=\"text-center\">\r\n                                                <Badge bg=\"secondary\" className=\"px-2 py-1\" style={{ fontSize: \"0.75rem\" }}>{size}</Badge>\r\n                                              </td>\r\n                                              <td className=\"text-center\">\r\n                                                <Badge bg=\"secondary\" pill className=\"px-2\" style={{ fontSize: \"0.75rem\" }}>\r\n                                                  {selectedOption ? selectedOption[cutKey] : 0}\r\n                                                </Badge>\r\n                                              </td>\r\n                                              <td className=\"text-center\">\r\n                                                <Badge bg=\"info\" pill className=\"px-2\" style={{ fontSize: \"0.75rem\" }}>\r\n                                                  {alreadySewnQty}\r\n                                                </Badge>\r\n                                              </td>\r\n                                              <td className=\"text-center\">\r\n                                                <Badge\r\n                                                  bg={availableQty > 0 ? 'success' : 'danger'}\r\n                                                  pill\r\n                                                  className=\"px-2\"\r\n                                                  style={{ fontSize: \"0.75rem\" }}\r\n                                                >\r\n                                                  {availableQty}\r\n                                                </Badge>\r\n                                              </td>\r\n                                            </tr>\r\n                                          );\r\n                                        })}\r\n\r\n                                        {/* Add a row for damage count */}\r\n                                        <tr className=\"table-light\">\r\n                                          <td className=\"text-center\">\r\n                                            <Badge bg=\"warning\" text=\"dark\" className=\"px-2 py-1\" style={{ fontSize: \"0.75rem\" }}>Damage</Badge>\r\n                                          </td>\r\n                                          <td className=\"text-center\">-</td>\r\n                                          <td className=\"text-center\">\r\n                                            <Badge bg=\"warning\" text=\"dark\" pill className=\"px-2\" style={{ fontSize: \"0.75rem\" }}>\r\n                                              {variant.alreadySewn ? variant.alreadySewn.damage_count || 0 : 0}\r\n                                            </Badge>\r\n                                          </td>\r\n                                          <td className=\"text-center\">-</td>\r\n                                        </tr>\r\n\r\n                                        {/* Add a row for total */}\r\n                                        <tr className=\"table-primary\">\r\n                                          <td className=\"text-center\">\r\n                                            <strong style={{ fontSize: \"0.8rem\" }}>TOTAL</strong>\r\n                                          </td>\r\n                                          <td className=\"text-center\">\r\n                                            <Badge bg=\"primary\" pill className=\"px-2\" style={{ fontSize: \"0.75rem\" }}>\r\n                                              {(() => {\r\n                                                const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\r\n                                                return selectedOption ? selectedOption.totalCut : 0;\r\n                                              })()}\r\n                                            </Badge>\r\n                                          </td>\r\n                                          <td className=\"text-center\">\r\n                                            <Badge bg=\"primary\" pill className=\"px-2\" style={{ fontSize: \"0.75rem\" }}>\r\n                                              {variant.alreadySewn ?\r\n                                                (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +\r\n                                                (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +\r\n                                                (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0) : 0}\r\n                                            </Badge>\r\n                                          </td>\r\n                                          <td className=\"text-center\">\r\n                                            {(() => {\r\n                                              const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);\r\n                                              const remainingQuantities = getRemainingQuantities(variant);\r\n                                              return selectedOption && variant.alreadySewn && (\r\n                                                <Badge\r\n                                                  bg={remainingQuantities && remainingQuantities.total >= 0 ? 'success' : 'danger'}\r\n                                                  pill\r\n                                                  className=\"px-2\"\r\n                                                  style={{ fontSize: \"0.75rem\" }}\r\n                                                >\r\n                                                  {Math.max(0, selectedOption.totalCut -\r\n                                                    ((variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +\r\n                                                    (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +\r\n                                                    (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0)))}\r\n                                                </Badge>\r\n                                              );\r\n                                            })()}\r\n                                          </td>\r\n                                        </tr>\r\n                                      </tbody>\r\n                                    </table>\r\n                                  </div>\r\n\r\n                                  <div className=\"mt-2 p-2 bg-light rounded border\">\r\n                                    <small className=\"text-muted d-block mb-1\" style={{ fontSize: \"0.7rem\" }}>\r\n                                      <FaInfoCircle className=\"me-1\" />\r\n                                      Available quantities for sewing in each size.\r\n                                    </small>\r\n                                    <small className=\"text-muted d-block\" style={{ fontSize: \"0.7rem\" }}>\r\n                                      <strong>Important:</strong> Total sewn + damage cannot exceed available quantity.\r\n                                    </small>\r\n                                  </div>\r\n                                </>\r\n                              )}\r\n                            </Card.Body>\r\n                          </Card>\r\n                        )}\r\n\r\n                        <h6 className=\"mt-3 mb-2\">Size Quantities</h6>\r\n                        <Row className=\"mb-3\">\r\n                          {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size) => {\r\n                            const sizeKey = size.toLowerCase();\r\n                            const remainingQuantities = getRemainingQuantities(variant);\r\n                            const isExceeded = remainingQuantities && remainingQuantities[sizeKey] < 0;\r\n\r\n                            return (\r\n                              <Col key={`${variant.id}-${size}`} xs={6} sm={4} md={2} className=\"mb-2\">\r\n                                <Form.Group>\r\n                                  <Form.Label className=\"text-center d-block\" style={{ fontSize: \"0.8rem\" }}>{size}</Form.Label>\r\n                                  <Form.Control\r\n                                    type=\"number\"\r\n                                    min=\"0\"\r\n                                    size=\"sm\"\r\n                                    value={variant[sizeKey]}\r\n                                    onChange={(e) => {\r\n                                      // Ensure value is not negative\r\n                                      const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                      updateVariant(variant.id, sizeKey, val);\r\n                                    }}\r\n                                    className={`text-center ${isExceeded ? 'border-danger' : ''}`}\r\n                                    disabled={!variant.selectedColor}\r\n                                  />\r\n                                  {isExceeded && (\r\n                                    <div className=\"text-danger small mt-1 text-center\" style={{ fontSize: \"0.7rem\" }}>\r\n                                      <FaExclamationTriangle className=\"me-1\" size={10} />\r\n                                      Exceeds limit\r\n                                    </div>\r\n                                  )}\r\n                                </Form.Group>\r\n                              </Col>\r\n                            );\r\n                          })}\r\n\r\n                          <Col xs={6} sm={4} md={2} className=\"mb-2\">\r\n                            <Form.Group>\r\n                              <Form.Label className=\"text-center d-block\" style={{ fontSize: \"0.8rem\" }}>Damage</Form.Label>\r\n                              <Form.Control\r\n                                type=\"number\"\r\n                                min=\"0\"\r\n                                size=\"sm\"\r\n                                value={variant.damageCount}\r\n                                onChange={(e) => {\r\n                                  // Ensure value is not negative\r\n                                  const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                  updateVariant(variant.id, 'damageCount', val);\r\n                                }}\r\n                                className={`text-center ${isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? 'border-danger' : ''}`}\r\n                                disabled={!variant.selectedColor}\r\n                              />\r\n                              {isVariantDamageExceeded(variant) && (\r\n                                <div className=\"text-danger small mt-1 text-center\" style={{ fontSize: \"0.7rem\" }}>\r\n                                  <FaExclamationTriangle className=\"me-1\" size={10} />\r\n                                  Exceeds total sewn\r\n                                </div>\r\n                              )}\r\n                              {!isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && (\r\n                                <div className=\"text-danger small mt-1 text-center\" style={{ fontSize: \"0.7rem\" }}>\r\n                                  <FaExclamationTriangle className=\"me-1\" size={10} />\r\n                                  Exceeds available\r\n                                </div>\r\n                              )}\r\n                            </Form.Group>\r\n                          </Col>\r\n                        </Row>\r\n\r\n                        {/* Variant Summary */}\r\n                        {getVariantTotalSewn(variant) > 0 && (\r\n                          <Card className=\"border-0 mb-3\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                            <Card.Body className=\"py-2\">\r\n                              <div className=\"d-flex flex-column\">\r\n                                <div className=\"d-flex align-items-center mb-2\">\r\n                                  <strong className=\"me-2\" style={{ fontSize: \"0.8rem\" }}>Total Quantities:</strong>\r\n                                  <Badge bg=\"primary\" className=\"me-1\" style={{ fontSize: \"0.7rem\" }}>XS: {variant.xs}</Badge>\r\n                                  <Badge bg=\"primary\" className=\"me-1\" style={{ fontSize: \"0.7rem\" }}>S: {variant.s}</Badge>\r\n                                  <Badge bg=\"primary\" className=\"me-1\" style={{ fontSize: \"0.7rem\" }}>M: {variant.m}</Badge>\r\n                                  <Badge bg=\"primary\" className=\"me-1\" style={{ fontSize: \"0.7rem\" }}>L: {variant.l}</Badge>\r\n                                  <Badge bg=\"primary\" className=\"me-1\" style={{ fontSize: \"0.7rem\" }}>XL: {variant.xl}</Badge>\r\n                                  <Badge bg=\"success\" className=\"ms-2\" style={{ fontSize: \"0.7rem\" }}>Total: {getVariantTotalSewn(variant)}</Badge>\r\n                                </div>\r\n                                {variant.damageCount > 0 && (\r\n                                  <div className=\"d-flex align-items-center\">\r\n                                    <strong className=\"me-2\" style={{ fontSize: \"0.8rem\" }}>Damage Count:</strong>\r\n                                    <Badge bg={isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? \"danger\" : \"warning\"}\r\n                                           text={isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? \"white\" : \"dark\"}\r\n                                           style={{ fontSize: \"0.7rem\" }}>\r\n                                      {variant.damageCount} {(isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant)) && <FaExclamationTriangle className=\"ms-1\" size={10} />}\r\n                                    </Badge>\r\n                                    <strong className=\"mx-2\" style={{ fontSize: \"0.8rem\" }}>Good Items:</strong>\r\n                                    <Badge bg=\"success\" style={{ fontSize: \"0.7rem\" }}>{Math.max(0, getVariantTotalSewn(variant) - variant.damageCount)}</Badge>\r\n                                    {isVariantDamageExceeded(variant) && (\r\n                                      <span className=\"text-danger ms-2 small\" style={{ fontSize: \"0.7rem\" }}>\r\n                                        <FaExclamationTriangle className=\"me-1\" />\r\n                                        Damage count cannot exceed total sewn items\r\n                                      </span>\r\n                                    )}\r\n                                    {!isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && (\r\n                                      <span className=\"text-danger ms-2 small\" style={{ fontSize: \"0.7rem\" }}>\r\n                                        <FaExclamationTriangle className=\"me-1\" />\r\n                                        Total sewn + damage exceeds available quantity\r\n                                      </span>\r\n                                    )}\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            </Card.Body>\r\n                          </Card>\r\n                        )}\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={!formValid || isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    `Submit Daily Sewing Records (${colorVariants.length} variant${colorVariants.length > 1 ? 's' : ''})`\r\n                  )}\r\n                </Button>\r\n              </div>\r\n\r\n              {!formValid && (\r\n                <div className=\"text-center mt-3\">\r\n                  <small className=\"text-muted\">\r\n                    <FaInfoCircle className=\"me-1\" />\r\n                    {!selectedProduct ? \"Please select a product\" :\r\n                     colorVariants.some(v => !v.selectedColor) ? \"Please select colors for all variants\" :\r\n                     colorVariants.every(v => getVariantTotalSewn(v) === 0) ? \"Please enter at least one size quantity in any variant\" :\r\n                     colorVariants.some(v => isVariantDamageExceeded(v)) ? \"Some variants have damage count exceeding total sewn items\" :\r\n                     colorVariants.some(v => isVariantTotalExceeded(v)) ? \"Some variants exceed available quantities\" :\r\n                     \"Please check your inputs\"}\r\n                  </small>\r\n                </div>\r\n              )}\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddDailySewingRecord;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AACrF,SAASC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3H,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAACoC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC;;EAE9E;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,CAAC;IAClDwC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;IACdC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,IAAI;IACjBC,EAAE,EAAE,CAAC;IACLC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,CAAC;IACdlB,OAAO,EAAE;EACX,CAAC,CAAC,CAAC;EAEH,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwD,YAAY,GAAGA,CAAA,KAAM;MACzBtB,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACsB,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMrB,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAENxD,SAAS,CAAC,MAAM;IACdC,KAAK,CACF0D,GAAG,CAAC,oDAAoD,CAAC,CACzDC,IAAI,CAAEC,GAAG,IAAKnC,WAAW,CAACmC,GAAG,CAACC,IAAI,CAAC,CAAC,CACpCC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;EAENhE,SAAS,CAAC,MAAM;IACd,IAAI2B,eAAe,EAAE;MACnB,MAAMwC,OAAO,GAAG1C,QAAQ,CAAC2C,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC9B,EAAE,KAAK+B,QAAQ,CAAC3C,eAAe,CAAC,CAAC;MACxE,IAAIwC,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEI,OAAO,EAAE;QACpB,MAAMC,OAAO,GAAGL,OAAO,CAACI,OAAO,CAACE,GAAG,CAAEC,MAAM,IAAK;UAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAC9C,MAAMC,QAAQ,GACZ,CAACN,MAAM,CAAC9B,EAAE,IAAI,CAAC,KACd8B,MAAM,CAAC7B,CAAC,IAAI,CAAC,CAAC,IACd6B,MAAM,CAAC5B,CAAC,IAAI,CAAC,CAAC,IACd4B,MAAM,CAAC3B,CAAC,IAAI,CAAC,CAAC,IACd2B,MAAM,CAAC1B,EAAE,IAAI,CAAC,CAAC;;UAElB;UACA,MAAMiC,UAAU,GAAG,EAAAN,qBAAA,GAAAD,MAAM,CAACQ,mBAAmB,cAAAP,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BQ,sBAAsB,cAAAP,sBAAA,uBAAlDA,sBAAA,CAAoDQ,WAAW,KAAI,gBAAgB;UACtG,MAAMC,SAAS,GAAG,EAAAR,sBAAA,GAAAH,MAAM,CAACQ,mBAAmB,cAAAL,sBAAA,uBAA1BA,sBAAA,CAA4BS,UAAU,OAAAR,sBAAA,GAAIJ,MAAM,CAACQ,mBAAmB,cAAAJ,sBAAA,uBAA1BA,sBAAA,CAA4BS,KAAK,KAAI,KAAK;UAEtG,OAAO;YACLC,KAAK,EAAEd,MAAM,CAACnC,EAAE;YAChBkD,KAAK,EAAE,GAAGR,UAAU,MAAMI,SAAS,EAAE;YACrCJ,UAAU,EAAEA,UAAU;YACtBI,SAAS,EAAEA,SAAS;YACpBE,KAAK,EAAE,EAAAR,sBAAA,GAAAL,MAAM,CAACQ,mBAAmB,cAAAH,sBAAA,uBAA1BA,sBAAA,CAA4BQ,KAAK,KAAI,SAAS;YACrDP,QAAQ;YACR;YACAU,MAAM,EAAEhB,MAAM,CAAC9B,EAAE,IAAI,CAAC;YACtB+C,KAAK,EAAEjB,MAAM,CAAC7B,CAAC,IAAI,CAAC;YACpB+C,KAAK,EAAElB,MAAM,CAAC5B,CAAC,IAAI,CAAC;YACpB+C,KAAK,EAAEnB,MAAM,CAAC3B,CAAC,IAAI,CAAC;YACpB+C,MAAM,EAAEpB,MAAM,CAAC1B,EAAE,IAAI;UACvB,CAAC;QACH,CAAC,CAAC;QACFlB,gBAAgB,CAAC0C,OAAO,CAAC;MAC3B,CAAC,MAAM;QACL1C,gBAAgB,CAAC,EAAE,CAAC;MACtB;MACA;MACAQ,gBAAgB,CAAC,CAAC;QAChBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,IAAI;QACjBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,CAAC;QACdlB,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACJ,eAAe,EAAEF,QAAQ,CAAC,CAAC;;EAE/B;EACA,MAAMsE,gBAAgB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,KAAK;IACrD;IACA3D,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACzB,GAAG,CAAC0B,OAAO,IACvCA,OAAO,CAAC5D,EAAE,KAAKyD,SAAS,GACpB;MAAE,GAAGG,OAAO;MAAEpE,OAAO,EAAE;IAAK,CAAC,GAC7BoE,OACN,CAAC,CAAC;IAEF,IAAI;MACF,MAAMtC,GAAG,GAAG,MAAM5D,KAAK,CAAC0D,GAAG,CAAC,iDAAiDsC,OAAO,GAAG,CAAC;MACxF3D,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACzB,GAAG,CAAC0B,OAAO,IACvCA,OAAO,CAAC5D,EAAE,KAAKyD,SAAS,GACpB;QAAE,GAAGG,OAAO;QAAExD,WAAW,EAAEkB,GAAG,CAACC,IAAI;QAAE/B,OAAO,EAAE;MAAM,CAAC,GACrDoE,OACN,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAC7D;MACA1B,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACzB,GAAG,CAAC0B,OAAO,IACvCA,OAAO,CAAC5D,EAAE,KAAKyD,SAAS,GACpB;QACE,GAAGG,OAAO;QACVxD,WAAW,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC/CjB,OAAO,EAAE;MACX,CAAC,GACDoE,OACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B9D,gBAAgB,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MACjC3D,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,IAAI;MACjBC,EAAE,EAAE,CAAC;MACLC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,CAAC;MACdlB,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMsE,kBAAkB,GAAIL,SAAS,IAAK;IACxC,IAAI3D,aAAa,CAACiE,MAAM,GAAG,CAAC,EAAE;MAC5BhE,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACJ,OAAO,IAAIA,OAAO,CAAC5D,EAAE,KAAKyD,SAAS,CAAC,CAAC;IAC5E;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAACR,SAAS,EAAES,KAAK,EAAEjB,KAAK,KAAK;IACjDlD,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,CAACzB,GAAG,CAAC0B,OAAO,IACvCA,OAAO,CAAC5D,EAAE,KAAKyD,SAAS,GACpB;MAAE,GAAGG,OAAO;MAAE,CAACM,KAAK,GAAGjB;IAAM,CAAC,GAC9BW,OACN,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMO,oBAAoB,GAAGA,CAACV,SAAS,EAAEW,UAAU,KAAK;IACtDH,aAAa,CAACR,SAAS,EAAE,eAAe,EAAEW,UAAU,CAAC;IACrD,IAAIA,UAAU,EAAE;MACdZ,gBAAgB,CAACC,SAAS,EAAEW,UAAU,CAAC;IACzC,CAAC,MAAM;MACLH,aAAa,CAACR,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC;IAC/C;EACF,CAAC;;EAED;EACAhG,SAAS,CAAC,MAAM;IACd,MAAM4G,UAAU,GAAG,CAAC,CAACjF,eAAe;;IAEpC;IACA,MAAMkF,gBAAgB,GAAGxE,aAAa,CAACyE,KAAK,CAACX,OAAO,IAAI;MACtD,MAAMY,QAAQ,GAAG,CAAC,CAACZ,OAAO,CAACzD,aAAa;MACxC,MAAMsE,QAAQ,GAAG1C,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAC9B0B,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAC5ByB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAC5BwB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAC5BuB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC;;MAE7C;MACA,MAAMiE,cAAc,GAAG3C,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC,GAAG0B,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC,GACrDyB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC,GAAGwB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC,GACnDuB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC;MAC/C,MAAMkE,aAAa,GAAG5C,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC,CAAC,IAAIgE,cAAc;;MAE1E;MACA,IAAIE,YAAY,GAAG,IAAI;MACvB,IAAIhB,OAAO,CAACzD,aAAa,IAAIyD,OAAO,CAACxD,WAAW,EAAE;QAChD,MAAMyE,MAAM,GAAGvF,aAAa,CAACuC,IAAI,CAACiD,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC;QAC7E,IAAI0E,MAAM,EAAE;UACV,MAAME,cAAc,GAAGF,MAAM,CAACpC,QAAQ,IACnCmB,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,CAAC,IAAIuD,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,IAC3DsD,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,IAAIqD,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,IAC1DoD,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC,IAAImD,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC,CAAC;UACzEJ,YAAY,GAAIF,cAAc,GAAG3C,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC,CAAC,IAAKqE,cAAc;QACxF;MACF;MAEA,OAAOP,QAAQ,IAAIC,QAAQ,IAAIE,aAAa,IAAIC,YAAY;IAC9D,CAAC,CAAC;IAEF5D,YAAY,CAACqD,UAAU,IAAIC,gBAAgB,IAAIxE,aAAa,CAACiE,MAAM,GAAG,CAAC,CAAC;EAC1E,CAAC,EAAE,CAAC3E,eAAe,EAAEU,aAAa,EAAER,aAAa,CAAC,CAAC;EAEnD,MAAM2F,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBvE,UAAU,CAAC,EAAE,CAAC;IACdE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI,CAAC1B,eAAe,EAAE;MACpBwB,UAAU,CAAC,0BAA0B,CAAC;MACtCE,eAAe,CAAC,KAAK,CAAC;MACtB;IACF;;IAEA;IACA,KAAK,IAAIsE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtF,aAAa,CAACiE,MAAM,EAAEqB,CAAC,EAAE,EAAE;MAC7C,MAAMxB,OAAO,GAAG9D,aAAa,CAACsF,CAAC,CAAC;MAEhC,IAAI,CAACxB,OAAO,CAACzD,aAAa,EAAE;QAC1BS,UAAU,CAAC,qCAAqCwE,CAAC,GAAG,CAAC,GAAG,CAAC;QACzDtE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,MAAMuE,cAAc,GAAG/F,aAAa,CAACuC,IAAI,CACtCiD,GAAG,IAAKA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aACjC,CAAC;MAED,IAAI,CAACkF,cAAc,EAAE;QACnBzE,UAAU,CAAC,gDAAgDwE,CAAC,GAAG,CAAC,GAAG,CAAC;QACpEtE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;;MAEA;MACA,MAAMwE,QAAQ,GAAGvD,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC;MAC1C,MAAMkF,OAAO,GAAGxD,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC;MACxC,MAAMkF,OAAO,GAAGzD,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC;MACxC,MAAMkF,OAAO,GAAG1D,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC;MACxC,MAAMkF,QAAQ,GAAG3D,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC;MAC1C,MAAMkF,YAAY,GAAG5D,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC,CAAC;;MAEvD;MACA,IAAI4E,QAAQ,GAAG,CAAC,IAAIC,OAAO,GAAG,CAAC,IAAIC,OAAO,GAAG,CAAC,IAAIC,OAAO,GAAG,CAAC,IAAIC,QAAQ,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;QACjG/E,UAAU,CAAC,0DAA0DwE,CAAC,GAAG,CAAC,GAAG,CAAC;QAC9EtE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;;MAEA;MACA,MAAM4D,cAAc,GAAGY,QAAQ,GAAGC,OAAO,GAAGC,OAAO,GAAGC,OAAO,GAAGC,QAAQ;MACxE,IAAIC,YAAY,GAAGjB,cAAc,EAAE;QACjC9D,UAAU,CAAC,iBAAiB+E,YAAY,mDAAmDjB,cAAc,iBAAiBU,CAAC,GAAG,CAAC,GAAG,CAAC;QACnItE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;;MAEA;MACA,IAAI,CAAC8C,OAAO,CAACxD,WAAW,EAAE;QACxBQ,UAAU,CAAC,6CAA6CwE,CAAC,GAAG,CAAC,qBAAqB,CAAC;QACnFtE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,MAAM8E,aAAa,GAAGhC,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC;MACjD,MAAMwF,YAAY,GAAGjC,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC;MAC/C,MAAMwF,YAAY,GAAGlC,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC;MAC/C,MAAMwF,YAAY,GAAGnC,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC;MAC/C,MAAMwF,aAAa,GAAGpC,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC;MAEjD,IAAI6E,QAAQ,GAAGM,aAAa,GAAGP,cAAc,CAAClC,MAAM,EAAE;QACpDvC,UAAU,CAAC,gBAAgB0E,QAAQ,qCAAqCD,cAAc,CAAClC,MAAM,GAAGyC,aAAa,iBAAiBR,CAAC,GAAG,CAAC,mBAAmBQ,aAAa,EAAE,CAAC;QACtK9E,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MACA,IAAIyE,OAAO,GAAGM,YAAY,GAAGR,cAAc,CAACjC,KAAK,EAAE;QACjDxC,UAAU,CAAC,eAAe2E,OAAO,qCAAqCF,cAAc,CAACjC,KAAK,GAAGyC,YAAY,iBAAiBT,CAAC,GAAG,CAAC,mBAAmBS,YAAY,EAAE,CAAC;QACjK/E,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MACA,IAAI0E,OAAO,GAAGM,YAAY,GAAGT,cAAc,CAAChC,KAAK,EAAE;QACjDzC,UAAU,CAAC,eAAe4E,OAAO,qCAAqCH,cAAc,CAAChC,KAAK,GAAGyC,YAAY,iBAAiBV,CAAC,GAAG,CAAC,mBAAmBU,YAAY,EAAE,CAAC;QACjKhF,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MACA,IAAI2E,OAAO,GAAGM,YAAY,GAAGV,cAAc,CAAC/B,KAAK,EAAE;QACjD1C,UAAU,CAAC,eAAe6E,OAAO,qCAAqCJ,cAAc,CAAC/B,KAAK,GAAGyC,YAAY,iBAAiBX,CAAC,GAAG,CAAC,mBAAmBW,YAAY,EAAE,CAAC;QACjKjF,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MACA,IAAI4E,QAAQ,GAAGM,aAAa,GAAGX,cAAc,CAAC9B,MAAM,EAAE;QACpD3C,UAAU,CAAC,gBAAgB8E,QAAQ,qCAAqCL,cAAc,CAAC9B,MAAM,GAAGyC,aAAa,iBAAiBZ,CAAC,GAAG,CAAC,mBAAmBY,aAAa,EAAE,CAAC;QACtKlF,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,MAAMmF,aAAa,GAAGX,QAAQ,GAAGC,OAAO,GAAGC,OAAO,GAAGC,OAAO,GAAGC,QAAQ;MACvE,MAAMQ,gBAAgB,GAAGN,aAAa,GAAGC,YAAY,GAAGC,YAAY,GAAGC,YAAY,GAAGC,aAAa;MAEnG,IAAIC,aAAa,GAAGC,gBAAgB,GAAGb,cAAc,CAAC5C,QAAQ,EAAE;QAC9D7B,UAAU,CAAC,2BAA2BqF,aAAa,qCAAqCZ,cAAc,CAAC5C,QAAQ,GAAGyD,gBAAgB,iBAAiBd,CAAC,GAAG,CAAC,mBAAmBc,gBAAgB,EAAE,CAAC;QAC9LpF,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;;MAEA;MACA,MAAMqF,kBAAkB,GAAGvC,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC;MAChE,MAAMD,cAAc,GAAGM,cAAc,CAAC5C,QAAQ,GAAGyD,gBAAgB,GAAGC,kBAAkB;;MAEtF;MACA,IAAIF,aAAa,GAAGN,YAAY,GAAGZ,cAAc,EAAE;QACjDnE,UAAU,CAAC,4BAA4BqF,aAAa,wBAAwBN,YAAY,qCAAqCZ,cAAc,iBAAiBK,CAAC,GAAG,CAAC,GAAG,CAAC;QACrKtE,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;IACF;;IAEA;IACA,IAAI;MACF,MAAMsF,QAAQ,GAAGtG,aAAa,CAACoC,GAAG,CAAC0B,OAAO,IAAI;QAC5C,MAAMyC,OAAO,GAAG;UACdC,qBAAqB,EAAE1C,OAAO,CAACzD,aAAa;UAC5CE,EAAE,EAAE0B,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC;UAC7BC,CAAC,EAAEyB,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC;UAC3BC,CAAC,EAAEwB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC;UAC3BC,CAAC,EAAEuB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC;UAC3BC,EAAE,EAAEsB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC;UAC7BuE,YAAY,EAAEjD,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC;QACjD,CAAC;QACD,OAAOhD,KAAK,CAAC6I,IAAI,CAAC,iDAAiD,EAAEF,OAAO,CAAC;MAC/E,CAAC,CAAC;MAEF,MAAMG,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAE3BxF,UAAU,CAAC,iDAAiDd,aAAa,CAACiE,MAAM,iBAAiBjE,aAAa,CAACiE,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC;MACxI1E,kBAAkB,CAAC,EAAE,CAAC;MACtBE,gBAAgB,CAAC,EAAE,CAAC;MACpBQ,gBAAgB,CAAC,CAAC;QAChBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,IAAI;QACjBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,WAAW,EAAE,CAAC;QACdlB,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;MACHsB,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOW,GAAG,EAAE;MAAA,IAAAiF,aAAA;MACZhF,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEF,GAAG,CAAC;MACxD,IAAIkF,YAAY,GAAG,oCAAoC;MACvD,KAAAD,aAAA,GAAIjF,GAAG,CAACmF,QAAQ,cAAAF,aAAA,eAAZA,aAAA,CAAcnF,IAAI,EAAE;QACtBoF,YAAY,GACV,OAAOlF,GAAG,CAACmF,QAAQ,CAACrF,IAAI,KAAK,QAAQ,GACjCsF,MAAM,CAACC,MAAM,CAACrF,GAAG,CAACmF,QAAQ,CAACrF,IAAI,CAAC,CAACwF,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAClDvF,GAAG,CAACmF,QAAQ,CAACrF,IAAI;MACzB;MACAX,UAAU,CAAC+F,YAAY,CAAC;MACxB7F,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMmG,YAAY,GAAGA,CAAC;IAAE1F,IAAI;IAAE2F,QAAQ;IAAEC;EAAW,CAAC,kBAClDtI,OAAA;IACEuI,GAAG,EAAEF,QAAS;IAAA,GACVC,UAAU;IACdE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAEjE5I,OAAA;MACEwI,KAAK,EAAE;QACLK,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAErG,IAAI,CAACyB,KAAK;QAC3B6E,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE;MAChB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFtJ,OAAA;MAAKwI,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEc,aAAa,EAAE;MAAS,CAAE;MAAAX,QAAA,gBACvD5I,OAAA;QAAMwI,KAAK,EAAE;UAAEgB,UAAU,EAAE,KAAK;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAb,QAAA,EAClDlG,IAAI,CAACmB;MAAU;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACPtJ,OAAA;QAAMwI,KAAK,EAAE;UAAEiB,QAAQ,EAAE,MAAM;UAAEtF,KAAK,EAAE;QAAO,CAAE;QAAAyE,QAAA,EAC9ClG,IAAI,CAACuB;MAAS;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMI,sBAAsB,GAAI3E,OAAO,IAAK;IAC1C,IAAI,CAACA,OAAO,CAACzD,aAAa,IAAI,CAACyD,OAAO,CAACxD,WAAW,EAAE,OAAO,IAAI;IAE/D,MAAMiF,cAAc,GAAG/F,aAAa,CAACuC,IAAI,CAACiD,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC;IACrF,IAAI,CAACkF,cAAc,EAAE,OAAO,IAAI;;IAEhC;IACA,MAAM5C,QAAQ,GAAG4C,cAAc,CAAC5C,QAAQ;IACxC,MAAM+F,gBAAgB,GAAG,CAAC5E,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,KAAKuD,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,IAC5DsD,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,IAAIqD,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,IAC1DoD,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC;IACrD,MAAMgI,mBAAmB,GAAG7E,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC;IACjE,MAAMD,cAAc,GAAGtC,QAAQ,GAAG+F,gBAAgB,GAAGC,mBAAmB;;IAExE;IACA,OAAO;MACLpI,EAAE,EAAEgF,cAAc,CAAClC,MAAM,IAAIS,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,CAAC,GAAG0B,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC;MACrFC,CAAC,EAAE+E,cAAc,CAACjC,KAAK,IAAIQ,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,GAAGyB,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC;MACjFC,CAAC,EAAE8E,cAAc,CAAChC,KAAK,IAAIO,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,GAAGwB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC;MACjFC,CAAC,EAAE6E,cAAc,CAAC/B,KAAK,IAAIM,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,GAAGuB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC;MACjFC,EAAE,EAAE4E,cAAc,CAAC9B,MAAM,IAAIK,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC,GAAGsB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC;MACrFiI,KAAK,EAAE3D,cAAc,GAAGhD,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC,GAAG0B,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC,GACrEyB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC,GAAGwB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC,GAAGuB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC,GAC/EsB,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC;IAC1C,CAAC;EACH,CAAC;;EAED;EACA,MAAMiI,mBAAmB,GAAI/E,OAAO,IAAK;IACvC,OAAO7B,QAAQ,CAAC6B,OAAO,CAACvD,EAAE,IAAI,CAAC,CAAC,GAAG0B,QAAQ,CAAC6B,OAAO,CAACtD,CAAC,IAAI,CAAC,CAAC,GAAGyB,QAAQ,CAAC6B,OAAO,CAACrD,CAAC,IAAI,CAAC,CAAC,GAC/EwB,QAAQ,CAAC6B,OAAO,CAACpD,CAAC,IAAI,CAAC,CAAC,GAAGuB,QAAQ,CAAC6B,OAAO,CAACnD,EAAE,IAAI,CAAC,CAAC;EAC7D,CAAC;EAED,MAAMmI,uBAAuB,GAAIhF,OAAO,IAAK;IAC3C,OAAO7B,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC,CAAC,GAAGiI,mBAAmB,CAAC/E,OAAO,CAAC;EAC1E,CAAC;EAED,MAAMiF,sBAAsB,GAAIjF,OAAO,IAAK;IAC1C,MAAMkF,mBAAmB,GAAGxJ,aAAa,CAACuC,IAAI,CAACiD,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC;IAC1F,OAAO2I,mBAAmB,IAAIlF,OAAO,CAACxD,WAAW,GAC9CuI,mBAAmB,CAAC/E,OAAO,CAAC,GAAG7B,QAAQ,CAAC6B,OAAO,CAAClD,WAAW,IAAI,CAAC,CAAC,GACjEoI,mBAAmB,CAACrG,QAAQ,IAAImB,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,CAAC,IAAIuD,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,IAC1FsD,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,IAAIqD,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,IAAIoD,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC,IAC1FmD,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC,CAAE,GACvC,KAAK;EACX,CAAC;EAED,oBACEnG,OAAA,CAAAE,SAAA;IAAA0I,QAAA,gBACE5I,OAAA,CAAClB,eAAe;MAAAqK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBtJ,OAAA;MACEwI,KAAK,EAAE;QACL0B,UAAU,EAAErJ,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CgI,KAAK,EAAE,eAAehI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzDsJ,UAAU,EAAE,eAAe;QAC3BxB,OAAO,EAAE;MACX,CAAE;MAAAC,QAAA,gBAEF5I,OAAA;QAAIoK,SAAS,EAAC,MAAM;QAAAxB,QAAA,gBAClB5I,OAAA,CAACP,QAAQ;UAAC2K,SAAS,EAAC;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJxH,OAAO,IAAIA,OAAO,CAACuI,UAAU,CAAC,GAAG,CAAC,iBACjCrK,OAAA,CAACX,KAAK;QACJ0F,OAAO,EAAC,SAAS;QACjBqF,SAAS,EAAC,2BAA2B;QAAAxB,QAAA,gBAErC5I,OAAA,CAACN,OAAO;UAAC0K,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCtJ,OAAA;UAAA4I,QAAA,EAAM9G;QAAO;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACR,EAEAxH,OAAO,IAAI,CAACA,OAAO,CAACuI,UAAU,CAAC,GAAG,CAAC,iBAClCrK,OAAA,CAACX,KAAK;QACJ0F,OAAO,EAAC,QAAQ;QAChBqF,SAAS,EAAC,2BAA2B;QAAAxB,QAAA,gBAErC5I,OAAA,CAACL,qBAAqB;UAACyK,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDtJ,OAAA;UAAA4I,QAAA,EAAM9G;QAAO;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACR,eAEDtJ,OAAA,CAACd,IAAI;QAACkL,SAAS,EAAC,gBAAgB;QAAC5B,KAAK,EAAE;UAAEO,eAAe,EAAE,SAAS;UAAEG,YAAY,EAAE;QAAO,CAAE;QAAAN,QAAA,eAC3F5I,OAAA,CAACd,IAAI,CAACqL,IAAI;UAAA3B,QAAA,eACR5I,OAAA,CAACb,IAAI;YAACqL,UAAU;YAACC,QAAQ,EAAErE,YAAa;YAAAwC,QAAA,gBACtC5I,OAAA,CAAChB,GAAG;cAAA4J,QAAA,eACF5I,OAAA,CAACf,GAAG;gBAACyL,EAAE,EAAE,EAAG;gBAAA9B,QAAA,eACV5I,OAAA,CAACb,IAAI,CAACwL,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAxB,QAAA,gBAC1B5I,OAAA,CAACb,IAAI,CAACyL,KAAK;oBAAAhC,QAAA,eAAC5I,OAAA;sBAAA4I,QAAA,EAAQ;oBAAwB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClEtJ,OAAA,CAACb,IAAI,CAACJ,MAAM;oBACVqF,KAAK,EAAE7D,eAAgB;oBACvBsK,QAAQ,EAAGxE,CAAC,IAAK7F,kBAAkB,CAAC6F,CAAC,CAACyE,MAAM,CAAC1G,KAAK,CAAE;oBACpDgG,SAAS,EAAC,wBAAwB;oBAClC5B,KAAK,EAAE;sBACLU,YAAY,EAAE,KAAK;sBACnBP,OAAO,EAAE,MAAM;sBACfwB,UAAU,EAAE;oBACd,CAAE;oBAAAvB,QAAA,gBAEF5I,OAAA;sBAAQoE,KAAK,EAAC,EAAE;sBAAAwE,QAAA,EAAC;oBAAc;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACvCjJ,QAAQ,CAACgD,GAAG,CAAE0H,IAAI;sBAAA,IAAAC,qBAAA;sBAAA,oBACjBhL,OAAA;wBAAsBoE,KAAK,EAAE2G,IAAI,CAAC5J,EAAG;wBAAAyH,QAAA,EAClCmC,IAAI,CAACE,YAAY,IAChB,IAAAD,qBAAA,GAAGD,IAAI,CAAChH,sBAAsB,cAAAiH,qBAAA,uBAA3BA,qBAAA,CAA6BhH,WAAW,WAAW+G,IAAI,CAACG,YAAY;sBAAE,GAFhEH,IAAI,CAAC5J,EAAE;wBAAAgI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGZ,CAAC;oBAAA,CACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eACdtJ,OAAA,CAACb,IAAI,CAACgM,IAAI;oBAACf,SAAS,EAAC,YAAY;oBAAAxB,QAAA,EAAC;kBAElC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL/I,eAAe,iBACdP,OAAA;cAAKoK,SAAS,EAAC,MAAM;cAAAxB,QAAA,gBACnB5I,OAAA;gBAAKoK,SAAS,EAAC,wDAAwD;gBAAAxB,QAAA,gBACrE5I,OAAA;kBAAIoK,SAAS,EAAC,MAAM;kBAAAxB,QAAA,EAAC;gBAAc;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCtJ,OAAA,CAACZ,MAAM;kBACL2F,OAAO,EAAC,iBAAiB;kBACzBuF,IAAI,EAAC,IAAI;kBACTc,OAAO,EAAEpG,eAAgB;kBACzBoF,SAAS,EAAC,2BAA2B;kBAAAxB,QAAA,gBAErC5I,OAAA,CAACH,MAAM;oBAACuK,SAAS,EAAC;kBAAM;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE7B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAELrI,aAAa,CAACoC,GAAG,CAAC,CAAC0B,OAAO,EAAEsG,KAAK,kBAChCrL,OAAA,CAACd,IAAI;gBAAkBkL,SAAS,EAAC,aAAa;gBAAC5B,KAAK,EAAE;kBAAEO,eAAe,EAAE;gBAAU,CAAE;gBAAAH,QAAA,gBACnF5I,OAAA,CAACd,IAAI,CAACoM,MAAM;kBAAClB,SAAS,EAAC,4DAA4D;kBAAAxB,QAAA,gBACjF5I,OAAA;oBAAIoK,SAAS,EAAC,MAAM;oBAAAxB,QAAA,GAAC,UAAQ,EAACyC,KAAK,GAAG,CAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EAC5CrI,aAAa,CAACiE,MAAM,GAAG,CAAC,iBACvBlF,OAAA,CAACZ,MAAM;oBACL2F,OAAO,EAAC,gBAAgB;oBACxBuF,IAAI,EAAC,IAAI;oBACTc,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAACF,OAAO,CAAC5D,EAAE,CAAE;oBAAAyH,QAAA,eAE9C5I,OAAA,CAACF,OAAO;sBAAAqJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU,CAAC,eACdtJ,OAAA,CAACd,IAAI,CAACqL,IAAI;kBAAA3B,QAAA,gBACR5I,OAAA,CAAChB,GAAG;oBAAA4J,QAAA,eACF5I,OAAA,CAACf,GAAG;sBAACyL,EAAE,EAAE,EAAG;sBAAA9B,QAAA,eACV5I,OAAA,CAACb,IAAI,CAACwL,KAAK;wBAACP,SAAS,EAAC,MAAM;wBAAAxB,QAAA,gBAC1B5I,OAAA,CAACb,IAAI,CAACyL,KAAK;0BAAAhC,QAAA,eAAC5I,OAAA;4BAAA4I,QAAA,EAAQ;0BAAK;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC/CtJ,OAAA,CAACjB,MAAM;0BACLqE,OAAO,EAAE3C,aAAc;0BACvB8K,UAAU,EAAE;4BAAEC,MAAM,EAAEpD;0BAAa,CAAE;0BACrChE,KAAK,EACH3D,aAAa,CAACuC,IAAI,CAAEiD,GAAG,IAAKA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC,IAAI,IACrE;0BACDuJ,QAAQ,EAAG5E,GAAG,IAAKX,oBAAoB,CAACP,OAAO,CAAC5D,EAAE,EAAE8E,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE7B,KAAK,CAAE;0BAChEqH,WAAW,EAAC,cAAc;0BAC1BC,UAAU,EAAE,CAACnL,eAAgB;0BAC7BoL,MAAM,EAAE;4BACNC,OAAO,EAAGC,QAAQ,KAAM;8BACtB,GAAGA,QAAQ;8BACXC,WAAW,EAAE,MAAM;8BACnBC,SAAS,EAAE,2BAA2B;8BACtC7C,YAAY,EAAE,KAAK;8BACnB,SAAS,EAAE;gCAAE4C,WAAW,EAAE;8BAAO,CAAC;8BAClCnD,OAAO,EAAE,KAAK;8BACdwB,UAAU,EAAE;4BACd,CAAC,CAAC;4BACFnE,MAAM,EAAEA,CAAC6F,QAAQ,EAAEG,KAAK,MAAM;8BAC5B,GAAGH,QAAQ;8BACX9C,eAAe,EAAEiD,KAAK,CAACC,UAAU,GAAG,SAAS,GAAGD,KAAK,CAACE,SAAS,GAAG,SAAS,GAAG,OAAO;8BACrF/H,KAAK,EAAE6H,KAAK,CAACC,UAAU,GAAG,OAAO,GAAG,MAAM;8BAC1CE,MAAM,EAAE;4BACV,CAAC;0BACH;wBAAE;0BAAAhD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFtJ,OAAA,CAACb,IAAI,CAACgM,IAAI;0BAACf,SAAS,EAAC,YAAY;0BAAAxB,QAAA,EAAC;wBAElC;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAW,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELvE,OAAO,CAACzD,aAAa,iBACpBtB,OAAA,CAACd,IAAI;oBAACkL,SAAS,EAAC,8BAA8B;oBAAAxB,QAAA,gBAC5C5I,OAAA,CAACd,IAAI,CAACoM,MAAM;sBAAClB,SAAS,EAAC,UAAU;sBAAAxB,QAAA,eAC/B5I,OAAA;wBAAKoK,SAAS,EAAC,2BAA2B;wBAAAxB,QAAA,gBACxC5I,OAAA,CAACJ,gBAAgB;0BAACwK,SAAS,EAAC;wBAAmB;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAClDtJ,OAAA;0BAAIoK,SAAS,EAAC,MAAM;0BAAAxB,QAAA,EAAC;wBAAoB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,eACdtJ,OAAA,CAACd,IAAI,CAACqL,IAAI;sBAAA3B,QAAA,EACP7D,OAAO,CAACpE,OAAO,gBACdX,OAAA;wBAAKoK,SAAS,EAAC,kBAAkB;wBAAAxB,QAAA,gBAC/B5I,OAAA,CAACV,OAAO;0BAAC8M,SAAS,EAAC,QAAQ;0BAACrH,OAAO,EAAC,SAAS;0BAACqF,SAAS,EAAC;wBAAM;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACjEtJ,OAAA;0BAAMoK,SAAS,EAAC,YAAY;0BAAAxB,QAAA,EAAC;wBAAqB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,gBAENtJ,OAAA,CAAAE,SAAA;wBAAA0I,QAAA,gBACE5I,OAAA;0BAAKoK,SAAS,EAAC,kBAAkB;0BAAAxB,QAAA,eAC/B5I,OAAA;4BAAOoK,SAAS,EAAC,4BAA4B;4BAAAxB,QAAA,gBAC3C5I,OAAA;8BAAOoK,SAAS,EAAC,aAAa;8BAAAxB,QAAA,eAC5B5I,OAAA;gCAAA4I,QAAA,gBACE5I,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAI;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACrCtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAG;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACpCtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAY;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eAC7CtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAS;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC,eACRtJ,OAAA;8BAAA4I,QAAA,GACG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAACvF,GAAG,CAAEiH,IAAI,IAAK;gCACzC,MAAM9D,cAAc,GAAG/F,aAAa,CAACuC,IAAI,CACtCiD,GAAG,IAAKA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aACjC,CAAC;gCACD,MAAM+K,OAAO,GAAG/B,IAAI,CAACgC,WAAW,CAAC,CAAC;gCAClC,MAAMC,MAAM,GAAG,GAAGF,OAAO,MAAM;gCAC/B,MAAMG,cAAc,GAAGzH,OAAO,CAACxD,WAAW,GAAGwD,OAAO,CAACxD,WAAW,CAAC8K,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gCAClF,MAAMI,YAAY,GAAGjG,cAAc,GACjCkG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEnG,cAAc,CAAC+F,MAAM,CAAC,GAAGC,cAAc,CAAC,GAAG,CAAC;gCAE1D,oBACExM,OAAA;kCAAA4I,QAAA,gBACE5I,OAAA;oCAAIoK,SAAS,EAAC,aAAa;oCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;sCAACqN,EAAE,EAAC,WAAW;sCAACxC,SAAS,EAAC,WAAW;sCAAC5B,KAAK,EAAE;wCAAEiB,QAAQ,EAAE;sCAAU,CAAE;sCAAAb,QAAA,EAAE0B;oCAAI;sCAAAnB,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxF,CAAC,eACLtJ,OAAA;oCAAIoK,SAAS,EAAC,aAAa;oCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;sCAACqN,EAAE,EAAC,WAAW;sCAACC,IAAI;sCAACzC,SAAS,EAAC,MAAM;sCAAC5B,KAAK,EAAE;wCAAEiB,QAAQ,EAAE;sCAAU,CAAE;sCAAAb,QAAA,EACxEpC,cAAc,GAAGA,cAAc,CAAC+F,MAAM,CAAC,GAAG;oCAAC;sCAAApD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACvC;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN,CAAC,eACLtJ,OAAA;oCAAIoK,SAAS,EAAC,aAAa;oCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;sCAACqN,EAAE,EAAC,MAAM;sCAACC,IAAI;sCAACzC,SAAS,EAAC,MAAM;sCAAC5B,KAAK,EAAE;wCAAEiB,QAAQ,EAAE;sCAAU,CAAE;sCAAAb,QAAA,EACnE4D;oCAAc;sCAAArD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACV;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN,CAAC,eACLtJ,OAAA;oCAAIoK,SAAS,EAAC,aAAa;oCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;sCACJqN,EAAE,EAAEH,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,QAAS;sCAC5CI,IAAI;sCACJzC,SAAS,EAAC,MAAM;sCAChB5B,KAAK,EAAE;wCAAEiB,QAAQ,EAAE;sCAAU,CAAE;sCAAAb,QAAA,EAE9B6D;oCAAY;sCAAAtD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACR;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN,CAAC;gCAAA,GAvBE,SAASvE,OAAO,CAAC5D,EAAE,IAAImJ,IAAI,EAAE;kCAAAnB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAwBlC,CAAC;8BAET,CAAC,CAAC,eAGFtJ,OAAA;gCAAIoK,SAAS,EAAC,aAAa;gCAAAxB,QAAA,gBACzB5I,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;oCAACqN,EAAE,EAAC,SAAS;oCAACE,IAAI,EAAC,MAAM;oCAAC1C,SAAS,EAAC,WAAW;oCAAC5B,KAAK,EAAE;sCAAEiB,QAAQ,EAAE;oCAAU,CAAE;oCAAAb,QAAA,EAAC;kCAAM;oCAAAO,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAClG,CAAC,eACLtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAC;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eAClCtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;oCAACqN,EAAE,EAAC,SAAS;oCAACE,IAAI,EAAC,MAAM;oCAACD,IAAI;oCAACzC,SAAS,EAAC,MAAM;oCAAC5B,KAAK,EAAE;sCAAEiB,QAAQ,EAAE;oCAAU,CAAE;oCAAAb,QAAA,EAClF7D,OAAO,CAACxD,WAAW,GAAGwD,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC,GAAG;kCAAC;oCAAAgD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC3D;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACLtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EAAC;gCAAC;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChC,CAAC,eAGLtJ,OAAA;gCAAIoK,SAAS,EAAC,eAAe;gCAAAxB,QAAA,gBAC3B5I,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,eACzB5I,OAAA;oCAAQwI,KAAK,EAAE;sCAAEiB,QAAQ,EAAE;oCAAS,CAAE;oCAAAb,QAAA,EAAC;kCAAK;oCAAAO,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnD,CAAC,eACLtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;oCAACqN,EAAE,EAAC,SAAS;oCAACC,IAAI;oCAACzC,SAAS,EAAC,MAAM;oCAAC5B,KAAK,EAAE;sCAAEiB,QAAQ,EAAE;oCAAU,CAAE;oCAAAb,QAAA,EACtE,CAAC,MAAM;sCACN,MAAMpC,cAAc,GAAG/F,aAAa,CAACuC,IAAI,CAACiD,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC;sCACrF,OAAOkF,cAAc,GAAGA,cAAc,CAAC5C,QAAQ,GAAG,CAAC;oCACrD,CAAC,EAAE;kCAAC;oCAAAuF,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACC;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACLtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,eACzB5I,OAAA,CAACT,KAAK;oCAACqN,EAAE,EAAC,SAAS;oCAACC,IAAI;oCAACzC,SAAS,EAAC,MAAM;oCAAC5B,KAAK,EAAE;sCAAEiB,QAAQ,EAAE;oCAAU,CAAE;oCAAAb,QAAA,EACtE7D,OAAO,CAACxD,WAAW,GAClB,CAACwD,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,KAAKuD,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,IAC3DsD,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,IAAIqD,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,IAC1DoD,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC,IAAImD,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC,CAAC,GAAG;kCAAC;oCAAAgD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACLtJ,OAAA;kCAAIoK,SAAS,EAAC,aAAa;kCAAAxB,QAAA,EACxB,CAAC,MAAM;oCACN,MAAMpC,cAAc,GAAG/F,aAAa,CAACuC,IAAI,CAACiD,GAAG,IAAIA,GAAG,CAAC7B,KAAK,KAAKW,OAAO,CAACzD,aAAa,CAAC;oCACrF,MAAMyL,mBAAmB,GAAGrD,sBAAsB,CAAC3E,OAAO,CAAC;oCAC3D,OAAOyB,cAAc,IAAIzB,OAAO,CAACxD,WAAW,iBAC1CvB,OAAA,CAACT,KAAK;sCACJqN,EAAE,EAAEG,mBAAmB,IAAIA,mBAAmB,CAAClD,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,QAAS;sCACjFgD,IAAI;sCACJzC,SAAS,EAAC,MAAM;sCAChB5B,KAAK,EAAE;wCAAEiB,QAAQ,EAAE;sCAAU,CAAE;sCAAAb,QAAA,EAE9B8D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEnG,cAAc,CAAC5C,QAAQ,IACjC,CAACmB,OAAO,CAACxD,WAAW,CAACC,EAAE,IAAI,CAAC,KAAKuD,OAAO,CAACxD,WAAW,CAACE,CAAC,IAAI,CAAC,CAAC,IAC5DsD,OAAO,CAACxD,WAAW,CAACG,CAAC,IAAI,CAAC,CAAC,IAAIqD,OAAO,CAACxD,WAAW,CAACI,CAAC,IAAI,CAAC,CAAC,IAC1DoD,OAAO,CAACxD,WAAW,CAACK,EAAE,IAAI,CAAC,CAAC,IAAImD,OAAO,CAACxD,WAAW,CAAC4E,YAAY,IAAI,CAAC,CAAC,CAAC;oCAAC;sCAAAgD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACtE,CACR;kCACH,CAAC,EAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACF,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENtJ,OAAA;0BAAKoK,SAAS,EAAC,kCAAkC;0BAAAxB,QAAA,gBAC/C5I,OAAA;4BAAOoK,SAAS,EAAC,yBAAyB;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,gBACvE5I,OAAA,CAACR,YAAY;8BAAC4K,SAAS,EAAC;4BAAM;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,iDAEnC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eACRtJ,OAAA;4BAAOoK,SAAS,EAAC,oBAAoB;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,gBAClE5I,OAAA;8BAAA4I,QAAA,EAAQ;4BAAU;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,0DAC7B;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,eACN;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACP,eAEDtJ,OAAA;oBAAIoK,SAAS,EAAC,WAAW;oBAAAxB,QAAA,EAAC;kBAAe;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9CtJ,OAAA,CAAChB,GAAG;oBAACoL,SAAS,EAAC,MAAM;oBAAAxB,QAAA,GAClB,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAACvF,GAAG,CAAEiH,IAAI,IAAK;sBACzC,MAAM+B,OAAO,GAAG/B,IAAI,CAACgC,WAAW,CAAC,CAAC;sBAClC,MAAMS,mBAAmB,GAAGrD,sBAAsB,CAAC3E,OAAO,CAAC;sBAC3D,MAAMiI,UAAU,GAAGD,mBAAmB,IAAIA,mBAAmB,CAACV,OAAO,CAAC,GAAG,CAAC;sBAE1E,oBACErM,OAAA,CAACf,GAAG;wBAA+BuC,EAAE,EAAE,CAAE;wBAACyL,EAAE,EAAE,CAAE;wBAACvC,EAAE,EAAE,CAAE;wBAACN,SAAS,EAAC,MAAM;wBAAAxB,QAAA,eACtE5I,OAAA,CAACb,IAAI,CAACwL,KAAK;0BAAA/B,QAAA,gBACT5I,OAAA,CAACb,IAAI,CAACyL,KAAK;4BAACR,SAAS,EAAC,qBAAqB;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,EAAE0B;0BAAI;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAa,CAAC,eAC9FtJ,OAAA,CAACb,IAAI,CAAC+N,OAAO;4BACXC,IAAI,EAAC,QAAQ;4BACbC,GAAG,EAAC,GAAG;4BACP9C,IAAI,EAAC,IAAI;4BACTlG,KAAK,EAAEW,OAAO,CAACsH,OAAO,CAAE;4BACxBxB,QAAQ,EAAGxE,CAAC,IAAK;8BACf;8BACA,MAAMgH,GAAG,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzJ,QAAQ,CAACmD,CAAC,CAACyE,MAAM,CAAC1G,KAAK,IAAI,CAAC,CAAC,CAAC;8BACtDgB,aAAa,CAACL,OAAO,CAAC5D,EAAE,EAAEkL,OAAO,EAAEgB,GAAG,CAAC;4BACzC,CAAE;4BACFjD,SAAS,EAAE,eAAe4C,UAAU,GAAG,eAAe,GAAG,EAAE,EAAG;4BAC9DM,QAAQ,EAAE,CAACvI,OAAO,CAACzD;0BAAc;4BAAA6H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC,CAAC,EACD0D,UAAU,iBACThN,OAAA;4BAAKoK,SAAS,EAAC,oCAAoC;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,gBAChF5I,OAAA,CAACL,qBAAqB;8BAACyK,SAAS,EAAC,MAAM;8BAACE,IAAI,EAAE;4BAAG;8BAAAnB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,iBAEtD;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACS;sBAAC,GAtBL,GAAGvE,OAAO,CAAC5D,EAAE,IAAImJ,IAAI,EAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAuB5B,CAAC;oBAEV,CAAC,CAAC,eAEFtJ,OAAA,CAACf,GAAG;sBAACuC,EAAE,EAAE,CAAE;sBAACyL,EAAE,EAAE,CAAE;sBAACvC,EAAE,EAAE,CAAE;sBAACN,SAAS,EAAC,MAAM;sBAAAxB,QAAA,eACxC5I,OAAA,CAACb,IAAI,CAACwL,KAAK;wBAAA/B,QAAA,gBACT5I,OAAA,CAACb,IAAI,CAACyL,KAAK;0BAACR,SAAS,EAAC,qBAAqB;0BAAC5B,KAAK,EAAE;4BAAEiB,QAAQ,EAAE;0BAAS,CAAE;0BAAAb,QAAA,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC9FtJ,OAAA,CAACb,IAAI,CAAC+N,OAAO;0BACXC,IAAI,EAAC,QAAQ;0BACbC,GAAG,EAAC,GAAG;0BACP9C,IAAI,EAAC,IAAI;0BACTlG,KAAK,EAAEW,OAAO,CAAClD,WAAY;0BAC3BgJ,QAAQ,EAAGxE,CAAC,IAAK;4BACf;4BACA,MAAMgH,GAAG,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzJ,QAAQ,CAACmD,CAAC,CAACyE,MAAM,CAAC1G,KAAK,IAAI,CAAC,CAAC,CAAC;4BACtDgB,aAAa,CAACL,OAAO,CAAC5D,EAAE,EAAE,aAAa,EAAEkM,GAAG,CAAC;0BAC/C,CAAE;0BACFjD,SAAS,EAAE,eAAeL,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,GAAG,eAAe,GAAG,EAAE,EAAG;0BACvHuI,QAAQ,EAAE,CAACvI,OAAO,CAACzD;wBAAc;0BAAA6H,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CAAC,EACDS,uBAAuB,CAAChF,OAAO,CAAC,iBAC/B/E,OAAA;0BAAKoK,SAAS,EAAC,oCAAoC;0BAAC5B,KAAK,EAAE;4BAAEiB,QAAQ,EAAE;0BAAS,CAAE;0BAAAb,QAAA,gBAChF5I,OAAA,CAACL,qBAAqB;4BAACyK,SAAS,EAAC,MAAM;4BAACE,IAAI,EAAE;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,sBAEtD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CACN,EACA,CAACS,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,iBACnE/E,OAAA;0BAAKoK,SAAS,EAAC,oCAAoC;0BAAC5B,KAAK,EAAE;4BAAEiB,QAAQ,EAAE;0BAAS,CAAE;0BAAAb,QAAA,gBAChF5I,OAAA,CAACL,qBAAqB;4BAACyK,SAAS,EAAC,MAAM;4BAACE,IAAI,EAAE;0BAAG;4BAAAnB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,qBAEtD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGLQ,mBAAmB,CAAC/E,OAAO,CAAC,GAAG,CAAC,iBAC/B/E,OAAA,CAACd,IAAI;oBAACkL,SAAS,EAAC,eAAe;oBAAC5B,KAAK,EAAE;sBAAEO,eAAe,EAAE;oBAAU,CAAE;oBAAAH,QAAA,eACpE5I,OAAA,CAACd,IAAI,CAACqL,IAAI;sBAACH,SAAS,EAAC,MAAM;sBAAAxB,QAAA,eACzB5I,OAAA;wBAAKoK,SAAS,EAAC,oBAAoB;wBAAAxB,QAAA,gBACjC5I,OAAA;0BAAKoK,SAAS,EAAC,gCAAgC;0BAAAxB,QAAA,gBAC7C5I,OAAA;4BAAQoK,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,EAAC;0BAAiB;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAClFtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,MAAI,EAAC7D,OAAO,CAACvD,EAAE;0BAAA;4BAAA2H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC5FtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,KAAG,EAAC7D,OAAO,CAACtD,CAAC;0BAAA;4BAAA0H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC1FtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,KAAG,EAAC7D,OAAO,CAACrD,CAAC;0BAAA;4BAAAyH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC1FtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,KAAG,EAAC7D,OAAO,CAACpD,CAAC;0BAAA;4BAAAwH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC1FtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,MAAI,EAAC7D,OAAO,CAACnD,EAAE;0BAAA;4BAAAuH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC5FtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACxC,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAAC,SAAO,EAACkB,mBAAmB,CAAC/E,OAAO,CAAC;0BAAA;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9G,CAAC,EACLvE,OAAO,CAAClD,WAAW,GAAG,CAAC,iBACtB7B,OAAA;0BAAKoK,SAAS,EAAC,2BAA2B;0BAAAxB,QAAA,gBACxC5I,OAAA;4BAAQoK,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,EAAC;0BAAa;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9EtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAE7C,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,GAAG,QAAQ,GAAG,SAAU;4BAC/F+H,IAAI,EAAE/C,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,GAAG,OAAO,GAAG,MAAO;4BAC7FyD,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,GAClC7D,OAAO,CAAClD,WAAW,EAAC,GAAC,EAAC,CAACkI,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,kBAAK/E,OAAA,CAACL,qBAAqB;8BAACyK,SAAS,EAAC,MAAM;8BAACE,IAAI,EAAE;4BAAG;8BAAAnB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/I,CAAC,eACRtJ,OAAA;4BAAQoK,SAAS,EAAC,MAAM;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,EAAC;0BAAW;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC5EtJ,OAAA,CAACT,KAAK;4BAACqN,EAAE,EAAC,SAAS;4BAACpE,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,EAAE8D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE7C,mBAAmB,CAAC/E,OAAO,CAAC,GAAGA,OAAO,CAAClD,WAAW;0BAAC;4BAAAsH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EAC3HS,uBAAuB,CAAChF,OAAO,CAAC,iBAC/B/E,OAAA;4BAAMoK,SAAS,EAAC,wBAAwB;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,gBACrE5I,OAAA,CAACL,qBAAqB;8BAACyK,SAAS,EAAC;4BAAM;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,+CAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP,EACA,CAACS,uBAAuB,CAAChF,OAAO,CAAC,IAAIiF,sBAAsB,CAACjF,OAAO,CAAC,iBACnE/E,OAAA;4BAAMoK,SAAS,EAAC,wBAAwB;4BAAC5B,KAAK,EAAE;8BAAEiB,QAAQ,EAAE;4BAAS,CAAE;4BAAAb,QAAA,gBACrE5I,OAAA,CAACL,qBAAqB;8BAACyK,SAAS,EAAC;4BAAM;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,kDAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA7SHvE,OAAO,CAAC5D,EAAE;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8Sf,CACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDtJ,OAAA;cAAKoK,SAAS,EAAC,oCAAoC;cAAAxB,QAAA,eACjD5I,OAAA,CAACZ,MAAM;gBACL+N,IAAI,EAAC,QAAQ;gBACbpI,OAAO,EAAC,SAAS;gBACjBuF,IAAI,EAAC,IAAI;gBACTgD,QAAQ,EAAE,CAACpL,SAAS,IAAIF,YAAa;gBACrCoI,SAAS,EAAC,MAAM;gBAAAxB,QAAA,EAEf5G,YAAY,gBACXhC,OAAA,CAAAE,SAAA;kBAAA0I,QAAA,gBACE5I,OAAA,CAACV,OAAO;oBAACiO,EAAE,EAAC,MAAM;oBAACnB,SAAS,EAAC,QAAQ;oBAAC9B,IAAI,EAAC,IAAI;oBAACkD,IAAI,EAAC,QAAQ;oBAAC,eAAY,MAAM;oBAACpD,SAAS,EAAC;kBAAM;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAEtG;gBAAA,eAAE,CAAC,GAEH,gCAAgCrI,aAAa,CAACiE,MAAM,WAAWjE,aAAa,CAACiE,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;cACnG;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEL,CAACpH,SAAS,iBACTlC,OAAA;cAAKoK,SAAS,EAAC,kBAAkB;cAAAxB,QAAA,eAC/B5I,OAAA;gBAAOoK,SAAS,EAAC,YAAY;gBAAAxB,QAAA,gBAC3B5I,OAAA,CAACR,YAAY;kBAAC4K,SAAS,EAAC;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChC,CAAC/I,eAAe,GAAG,yBAAyB,GAC5CU,aAAa,CAACwM,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,CAACpM,aAAa,CAAC,GAAG,uCAAuC,GACnFL,aAAa,CAACyE,KAAK,CAACgI,CAAC,IAAI5D,mBAAmB,CAAC4D,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,wDAAwD,GACjHzM,aAAa,CAACwM,IAAI,CAACC,CAAC,IAAI3D,uBAAuB,CAAC2D,CAAC,CAAC,CAAC,GAAG,4DAA4D,GAClHzM,aAAa,CAACwM,IAAI,CAACC,CAAC,IAAI1D,sBAAsB,CAAC0D,CAAC,CAAC,CAAC,GAAG,2CAA2C,GAChG,0BAA0B;cAAA;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAClJ,EAAA,CAt2BID,oBAAoB;AAAAwN,EAAA,GAApBxN,oBAAoB;AAw2B1B,eAAeA,oBAAoB;AAAC,IAAAwN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}