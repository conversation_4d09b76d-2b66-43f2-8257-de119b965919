{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\AddCutting.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\nimport { Card, Form, Button, Row, Col, Spinner, Alert, Badge, Modal } from 'react-bootstrap';\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\nimport jsPDF from 'jspdf';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddCuttingRecord = () => {\n  _s();\n  // Overall cutting record fields\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\n  const [cuttingDate, setCuttingDate] = useState('');\n  const [description, setDescription] = useState('');\n  const [productName, setProductName] = useState('');\n\n  // Fabric definition groups - each group has a fabric definition and its variants\n  const [fabricGroups, setFabricGroups] = useState([{\n    id: Date.now(),\n    fabric_definition: '',\n    variants: [{\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]\n  }]);\n\n  // Loading, error, success states\n  const [loadingVariants, setLoadingVariants] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [validated, setValidated] = useState(false);\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [submittedRecord, setSubmittedRecord] = useState(null);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Fetch fabric definitions and variants on mount\n  useEffect(() => {\n    setLoadingVariants(true);\n\n    // Fetch fabric definitions\n    const fetchDefinitions = axios.get(\"http://localhost:8000/api/fabric-definitions/\");\n    // Fetch all fabric variants\n    const fetchVariants = axios.get(\"http://localhost:8000/api/fabric-variants/\");\n    Promise.all([fetchDefinitions, fetchVariants]).then(([definitionsRes, variantsRes]) => {\n      setFabricDefinitions(definitionsRes.data);\n      setAllFabricVariants(variantsRes.data);\n      setLoadingVariants(false);\n    }).catch(err => {\n      console.error('Error fetching fabric data:', err);\n      setError('Failed to load fabric data. Please try again.');\n      setLoadingVariants(false);\n    });\n  }, []);\n\n  // Add a new fabric definition group\n  const addFabricGroup = () => {\n    setFabricGroups([...fabricGroups, {\n      id: Date.now(),\n      fabric_definition: '',\n      variants: [{\n        fabric_variant: '',\n        yard_usage: '',\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0\n      }]\n    }]);\n  };\n\n  // Remove a fabric definition group\n  const removeFabricGroup = groupIndex => {\n    if (fabricGroups.length > 1) {\n      const newGroups = fabricGroups.filter((_, i) => i !== groupIndex);\n      setFabricGroups(newGroups);\n    }\n  };\n\n  // Add a new variant row to a specific fabric group\n  const addVariantToGroup = groupIndex => {\n    const newGroups = [...fabricGroups];\n    newGroups[groupIndex].variants.push({\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    });\n    setFabricGroups(newGroups);\n  };\n\n  // Remove a variant from a specific fabric group\n  const removeVariantFromGroup = (groupIndex, variantIndex) => {\n    const newGroups = [...fabricGroups];\n    if (newGroups[groupIndex].variants.length > 1) {\n      newGroups[groupIndex].variants = newGroups[groupIndex].variants.filter((_, i) => i !== variantIndex);\n      setFabricGroups(newGroups);\n    }\n  };\n\n  // Handle fabric definition change for a group\n  const handleFabricDefinitionChange = (groupIndex, fabricDefinitionId) => {\n    const newGroups = [...fabricGroups];\n    newGroups[groupIndex].fabric_definition = fabricDefinitionId;\n    // Reset variants when fabric definition changes\n    newGroups[groupIndex].variants = [{\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }];\n    setFabricGroups(newGroups);\n  };\n\n  // Handle variant field change\n  const handleVariantChange = (groupIndex, variantIndex, field, value) => {\n    const newGroups = [...fabricGroups];\n\n    // If changing fabric variant, check for duplicates within the same group\n    if (field === 'fabric_variant') {\n      if (isDuplicateFabricVariant(groupIndex, value, variantIndex)) {\n        setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);\n        return;\n      } else {\n        setError('');\n      }\n    }\n    newGroups[groupIndex].variants[variantIndex][field] = value;\n    setFabricGroups(newGroups);\n  };\n\n  // Check if a fabric variant is already selected in the same group\n  const isDuplicateFabricVariant = (groupIndex, variantId, currentVariantIndex) => {\n    return fabricGroups[groupIndex].variants.some((variant, idx) => idx !== currentVariantIndex && variant.fabric_variant === variantId && variantId !== '');\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Form validation\n    const form = e.currentTarget;\n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n\n    // Check if any fabric group has valid data\n    const hasValidGroups = fabricGroups.some(group => group.fabric_definition && group.variants.some(variant => variant.fabric_variant));\n    if (!hasValidGroups) {\n      setError('Please select at least one fabric definition and one fabric variant.');\n      return;\n    }\n\n    // Validate each fabric group\n    let validationError = false;\n    for (let groupIndex = 0; groupIndex < fabricGroups.length; groupIndex++) {\n      const group = fabricGroups[groupIndex];\n      if (group.fabric_definition && group.variants.some(variant => variant.fabric_variant)) {\n        // Check for duplicates within the group\n        const selectedVariants = group.variants.map(variant => variant.fabric_variant).filter(Boolean);\n        const uniqueVariants = [...new Set(selectedVariants)];\n        if (selectedVariants.length !== uniqueVariants.length) {\n          setError(`Duplicate fabric variants detected in fabric group ${groupIndex + 1}. Please ensure each variant is selected only once per group.`);\n          validationError = true;\n          break;\n        }\n\n        // Validate yard availability for each variant\n        for (let variant of group.variants) {\n          if (variant.fabric_variant) {\n            const variantData = allFabricVariants.find(v => v.id === variant.fabric_variant);\n            if (variantData && parseFloat(variant.yard_usage) > (variantData.available_yard || variantData.total_yard)) {\n              setError(`Yard usage for ${variantData.color_name || variantData.color} exceeds available yards (${variantData.available_yard || variantData.total_yard} yards available).`);\n              validationError = true;\n              break;\n            }\n          }\n        }\n      }\n    }\n    if (validationError) {\n      setValidated(true);\n      return;\n    }\n    setValidated(true);\n    setIsSubmitting(true);\n    setError('');\n    setSuccess('');\n    try {\n      // Flatten fabric groups into details array\n      const details = [];\n      fabricGroups.forEach(group => {\n        if (group.fabric_definition) {\n          group.variants.forEach(variant => {\n            if (variant.fabric_variant) {\n              details.push(variant);\n            }\n          });\n        }\n      });\n      const payload = {\n        cutting_date: cuttingDate,\n        description: description,\n        product_name: productName,\n        details: details\n      };\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\n      setSuccess('Cutting record created successfully!');\n\n      // Store the submitted record for PDF generation\n      const fabricNames = new Set();\n      const recordData = {\n        ...response.data,\n        details: response.data.details.map(detail => {\n          var _variant$fabric_defin, _variant$fabric_defin2;\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n          if (variant !== null && variant !== void 0 && (_variant$fabric_defin = variant.fabric_definition_data) !== null && _variant$fabric_defin !== void 0 && _variant$fabric_defin.fabric_name) {\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\n          }\n          return {\n            ...detail,\n            color: (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            color_name: (variant === null || variant === void 0 ? void 0 : variant.color_name) || (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            fabric_name: (variant === null || variant === void 0 ? void 0 : (_variant$fabric_defin2 = variant.fabric_definition_data) === null || _variant$fabric_defin2 === void 0 ? void 0 : _variant$fabric_defin2.fabric_name) || 'Unknown'\n          };\n        }),\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\n        totalQuantities: totalQuantities\n      };\n      setSubmittedRecord(recordData);\n\n      // Show the PDF generation modal\n      setShowPdfModal(true);\n    } catch (err) {\n      console.error('Error creating cutting record:', err);\n      if (err.response && err.response.data) {\n        // Display more specific error message if available\n        const errorMessage = typeof err.response.data === 'string' ? err.response.data : 'Failed to create cutting record. Please check your inputs.';\n        setError(errorMessage);\n      } else {\n        setError('Failed to create cutting record. Please try again.');\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Function to generate PDF directly without using html2canvas\n  const generatePDF = () => {\n    if (!submittedRecord) return;\n    try {\n      // Create a new PDF document\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes and styles\n      const titleFontSize = 18;\n      const headingFontSize = 14;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add title\n      pdf.setFontSize(titleFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Cutting Record', 105, 20, {\n        align: 'center'\n      });\n\n      // Add general information section\n      pdf.setFontSize(headingFontSize);\n      pdf.text('General Information', 20, 35);\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'normal');\n\n      // Draw table for general info\n      pdf.line(20, 40, 190, 40); // Top horizontal line\n\n      const generalInfoData = [['Record ID', submittedRecord.id.toString()], ['Product Name', submittedRecord.product_name], ['Fabrics Used', submittedRecord.fabric_names], ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()], ['Description', submittedRecord.description || 'N/A']];\n      let yPos = 45;\n      generalInfoData.forEach(row => {\n        pdf.setFont('helvetica', 'bold');\n        pdf.text(row[0], 25, yPos);\n        pdf.setFont('helvetica', 'normal');\n        pdf.text(row[1], 80, yPos);\n        yPos += 8;\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\n      });\n\n      // Add fabric details section\n      pdf.setFontSize(headingFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Fabric Details', 20, yPos + 10);\n\n      // Table headers for fabric details\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\n\n      // Calculate starting positions for each column\n      const colPositions = [];\n      let currentPos = 20;\n      colWidths.forEach(width => {\n        colPositions.push(currentPos);\n        currentPos += width;\n      });\n\n      // Draw table header\n      yPos += 15;\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'bold');\n\n      // Draw header background\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n\n      // Draw header text\n      headers.forEach((header, index) => {\n        pdf.text(header, colPositions[index] + 2, yPos);\n      });\n\n      // Draw horizontal line after header\n      yPos += 3;\n      pdf.line(20, yPos, 190, yPos);\n\n      // Draw table rows\n      pdf.setFont('helvetica', 'normal');\n      submittedRecord.details.forEach(detail => {\n        var _detail$xs, _detail$s, _detail$m, _detail$l, _detail$xl;\n        yPos += 8;\n\n        // Calculate total for this row\n        const total = parseInt(detail.xs || 0) + parseInt(detail.s || 0) + parseInt(detail.m || 0) + parseInt(detail.l || 0) + parseInt(detail.xl || 0);\n\n        // Draw row data\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\n        pdf.text(((_detail$xs = detail.xs) === null || _detail$xs === void 0 ? void 0 : _detail$xs.toString()) || '0', colPositions[3] + 2, yPos);\n        pdf.text(((_detail$s = detail.s) === null || _detail$s === void 0 ? void 0 : _detail$s.toString()) || '0', colPositions[4] + 2, yPos);\n        pdf.text(((_detail$m = detail.m) === null || _detail$m === void 0 ? void 0 : _detail$m.toString()) || '0', colPositions[5] + 2, yPos);\n        pdf.text(((_detail$l = detail.l) === null || _detail$l === void 0 ? void 0 : _detail$l.toString()) || '0', colPositions[6] + 2, yPos);\n        pdf.text(((_detail$xl = detail.xl) === null || _detail$xl === void 0 ? void 0 : _detail$xl.toString()) || '0', colPositions[7] + 2, yPos);\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\n\n        // Draw horizontal line after row\n        yPos += 3;\n        pdf.line(20, yPos, 190, yPos);\n      });\n\n      // Draw totals row\n      yPos += 8;\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Total', colPositions[0] + 2, yPos);\n      pdf.text('', colPositions[1] + 2, yPos);\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\n\n      // Add footer\n      pdf.setFontSize(smallFontSize);\n      pdf.setFont('helvetica', 'italic');\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      pdf.text('Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\n\n      // Reset form after PDF generation\n      setShowPdfModal(false);\n      setCuttingDate('');\n      setDescription('');\n      setProductName('');\n      setFabricGroups([{\n        id: Date.now(),\n        fabric_definition: '',\n        variants: [{\n          fabric_variant: '',\n          yard_usage: '',\n          xs: 0,\n          s: 0,\n          m: 0,\n          l: 0,\n          xl: 0\n        }]\n      }]);\n      setValidated(false);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setError('Failed to generate PDF. Please try again.');\n      setShowPdfModal(false);\n    }\n  };\n\n  // Function to handle modal close without generating PDF\n  const handleCloseModal = () => {\n    setShowPdfModal(false);\n    // Reset form\n    setCuttingDate('');\n    setDescription('');\n    setProductName('');\n    setFabricGroups([{\n      id: Date.now(),\n      fabric_definition: '',\n      variants: [{\n        fabric_variant: '',\n        yard_usage: '',\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0\n      }]\n    }]);\n    setValidated(false);\n  };\n\n  // Calculate total quantities for all fabric groups\n  const totalQuantities = fabricGroups.reduce((acc, group) => {\n    group.variants.forEach(variant => {\n      if (variant.fabric_variant) {\n        acc.xs += parseInt(variant.xs) || 0;\n        acc.s += parseInt(variant.s) || 0;\n        acc.m += parseInt(variant.m) || 0;\n        acc.l += parseInt(variant.l) || 0;\n        acc.xl += parseInt(variant.xl) || 0;\n        acc.total += (parseInt(variant.xs) || 0) + (parseInt(variant.s) || 0) + (parseInt(variant.m) || 0) + (parseInt(variant.l) || 0) + (parseInt(variant.xl) || 0);\n        acc.yard_usage += parseFloat(variant.yard_usage) || 0;\n      }\n    });\n    return acc;\n  }, {\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0,\n    total: 0,\n    yard_usage: 0\n  });\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(BsScissors, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), \"Add Cutting Record\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsCheck2Circle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), success]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsExclamationTriangle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: \"#D9EDFB\",\n          borderRadius: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            noValidate: true,\n            validated: validated,\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Product Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: productName,\n                    onChange: e => setProductName(e.target.value),\n                    placeholder: \"Enter product name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please provide a product name.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cutting Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: cuttingDate,\n                    onChange: e => setCuttingDate(e.target.value),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please select a cutting date.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    as: \"textarea\",\n                    rows: 3,\n                    value: description,\n                    onChange: e => setDescription(e.target.value),\n                    placeholder: \"Enter details about this cutting record...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0\",\n                children: \"Fabric Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"sm\",\n                onClick: addFabricGroup,\n                disabled: isSubmitting,\n                children: [/*#__PURE__*/_jsxDEV(BsPlus, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 19\n                }, this), \" Add Fabric Definition\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), fabricGroups.map((group, groupIndex) => {\n              // Get fabric variants for the selected fabric definition\n              const groupVariants = group.fabric_definition ? allFabricVariants.filter(v => v.fabric_definition === parseInt(group.fabric_definition)) : [];\n              return /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-4 border\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"d-flex justify-content-between align-items-center bg-primary text-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: [\"Fabric Definition #\", groupIndex + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-light\",\n                    size: \"sm\",\n                    onClick: () => removeFabricGroup(groupIndex),\n                    disabled: fabricGroups.length === 1,\n                    children: [/*#__PURE__*/_jsxDEV(BsTrash, {\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 25\n                    }, this), \" Remove\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Row, {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(Col, {\n                      md: 12,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Select Fabric Definition\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 572,\n                            columnNumber: 41\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 29\n                        }, this), loadingVariants ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                            animation: \"border\",\n                            size: \"sm\",\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 575,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Loading fabric definitions...\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 576,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 574,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(Form.Select, {\n                          value: group.fabric_definition,\n                          onChange: e => handleFabricDefinitionChange(groupIndex, e.target.value),\n                          required: true,\n                          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                            value: \"\",\n                            children: \"Select Fabric Definition\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 584,\n                            columnNumber: 33\n                          }, this), fabricDefinitions.map(fd => /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: fd.id,\n                            children: [fd.fabric_name, \" - \", fd.supplier_name || 'Unknown Supplier']\n                          }, fd.id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 586,\n                            columnNumber: 35\n                          }, this))]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 579,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 23\n                  }, this), group.fabric_definition && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"mb-0\",\n                        children: \"Fabric Variants\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-success\",\n                        size: \"sm\",\n                        onClick: () => addVariantToGroup(groupIndex),\n                        disabled: isSubmitting,\n                        children: [/*#__PURE__*/_jsxDEV(BsPlus, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 607,\n                          columnNumber: 31\n                        }, this), \" Add Variant\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 27\n                    }, this), group.variants.map((variant, variantIndex) => {\n                      const currentVariant = allFabricVariants.find(v => v.id === variant.fabric_variant);\n                      return /*#__PURE__*/_jsxDEV(Card, {\n                        className: \"mb-3 border-light\",\n                        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex justify-content-between align-items-center mb-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex align-items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                                className: \"mb-0 me-2\",\n                                children: [\"Variant #\", variantIndex + 1]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 619,\n                                columnNumber: 39\n                              }, this), currentVariant && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"d-flex align-items-center\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  style: {\n                                    width: '16px',\n                                    height: '16px',\n                                    backgroundColor: currentVariant.color,\n                                    border: '1px solid #ccc',\n                                    borderRadius: '3px',\n                                    marginRight: '6px'\n                                  },\n                                  title: `Color: ${currentVariant.color}`\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 622,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                                  className: \"text-muted\",\n                                  children: currentVariant.color_name || currentVariant.color\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 633,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 621,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 618,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"outline-danger\",\n                              size: \"sm\",\n                              onClick: () => removeVariantFromGroup(groupIndex, variantIndex),\n                              disabled: group.variants.length === 1,\n                              children: [/*#__PURE__*/_jsxDEV(BsTrash, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 645,\n                                columnNumber: 39\n                              }, this), \" Remove\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 639,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 617,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Row, {\n                            children: [/*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    children: \"Fabric Variant (Color)\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 652,\n                                    columnNumber: 53\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 652,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(ColorVariantSelector, {\n                                  variants: groupVariants,\n                                  selectedValue: variant.fabric_variant,\n                                  onSelect: value => handleVariantChange(groupIndex, variantIndex, 'fabric_variant', value),\n                                  placeholder: \"Select Color Variant\",\n                                  isDuplicateFunction: isDuplicateFabricVariant,\n                                  groupIndex: groupIndex,\n                                  variantIndex: variantIndex\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 653,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 651,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 650,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                    className: \"mb-0\",\n                                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                      children: \"Yard Usage\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 667,\n                                      columnNumber: 72\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 667,\n                                    columnNumber: 43\n                                  }, this), currentVariant && /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"d-flex align-items-center\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      style: {\n                                        width: '16px',\n                                        height: '16px',\n                                        backgroundColor: currentVariant.color,\n                                        border: '1px solid #ccc',\n                                        borderRadius: '3px',\n                                        marginRight: '8px'\n                                      },\n                                      title: `Color: ${currentVariant.color_name || currentVariant.color}`\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 670,\n                                      columnNumber: 47\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\",\n                                      children: [currentVariant.color_name || currentVariant.color, \" - Available: \", currentVariant.available_yard || currentVariant.total_yard, \" yards\"]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 681,\n                                      columnNumber: 47\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 669,\n                                    columnNumber: 45\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 666,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                  type: \"number\",\n                                  step: \"0.01\",\n                                  min: \"0\",\n                                  value: variant.yard_usage,\n                                  onChange: e => handleVariantChange(groupIndex, variantIndex, 'yard_usage', e.target.value),\n                                  required: true,\n                                  placeholder: \"Enter yards used\",\n                                  isInvalid: currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard),\n                                  className: currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\",\n                                  style: {\n                                    height: '38px'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 687,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                                  type: \"invalid\",\n                                  children: currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)` : \"Please enter valid yard usage.\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 699,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 665,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 664,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Size Quantities\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 708,\n                              columnNumber: 64\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 708,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Row, {\n                            children: [[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\n                              const sizeKey = size.toLowerCase();\n                              return /*#__PURE__*/_jsxDEV(Col, {\n                                xs: 6,\n                                sm: 4,\n                                md: 2,\n                                className: \"mb-3\",\n                                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                    className: \"text-center d-block\",\n                                    children: size\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 715,\n                                    columnNumber: 45\n                                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                    type: \"number\",\n                                    min: \"0\",\n                                    value: variant[sizeKey],\n                                    onChange: e => {\n                                      const val = Math.max(0, parseInt(e.target.value || 0));\n                                      handleVariantChange(groupIndex, variantIndex, sizeKey, val);\n                                    },\n                                    className: \"text-center\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 716,\n                                    columnNumber: 45\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 714,\n                                  columnNumber: 43\n                                }, this)\n                              }, sizeIndex, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 713,\n                                columnNumber: 41\n                              }, this);\n                            }), /*#__PURE__*/_jsxDEV(Col, {\n                              xs: 6,\n                              sm: 4,\n                              md: 2,\n                              className: \"mb-3\",\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  className: \"text-center d-block\",\n                                  children: \"Total\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 732,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"form-control text-center bg-light\",\n                                  children: parseInt(variant.xs || 0) + parseInt(variant.s || 0) + parseInt(variant.m || 0) + parseInt(variant.l || 0) + parseInt(variant.xl || 0)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 733,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 731,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 730,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 709,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 33\n                        }, this)\n                      }, variantIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 31\n                      }, this);\n                    })]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)]\n              }, group.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"border-0\",\n                style: {\n                  backgroundColor: \"#e8f4fe\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex flex-column\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Quantities:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 759,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XS: \", totalQuantities.xs]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"S: \", totalQuantities.s]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 761,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"M: \", totalQuantities.m]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 762,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"L: \", totalQuantities.l]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XL: \", totalQuantities.xl]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"success\",\n                        className: \"ms-2\",\n                        children: [\"Total: \", totalQuantities.total]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 765,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Yard Usage:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 768,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"info\",\n                        children: [totalQuantities.yard_usage.toFixed(2), \" yards\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 769,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 767,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                disabled: isSubmitting,\n                className: \"px-5\",\n                children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 23\n                  }, this), \"Submitting...\"]\n                }, void 0, true) : 'Submit Cutting Record'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Generate Cutting Record PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Would you like to generate a PDF for this cutting record?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 11\n        }, this), submittedRecord && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"The PDF will include the following information:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.product_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Fabric:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.fabric_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cutting Date:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 21\n              }, this), \" \", new Date(submittedRecord.cutting_date).toLocaleDateString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Quantities:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 21\n              }, this), \" XS: \", submittedRecord.totalQuantities.xs, \", S: \", submittedRecord.totalQuantities.s, \", M: \", submittedRecord.totalQuantities.m, \", L: \", submittedRecord.totalQuantities.l, \", XL: \", submittedRecord.totalQuantities.xl]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Items:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.total]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Yard Usage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.yard_usage.toFixed(2), \" yards\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: \"No, Skip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generatePDF,\n          children: [/*#__PURE__*/_jsxDEV(BsFilePdf, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 13\n          }, this), \" Generate PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddCuttingRecord, \"2V6SDyYqz6cCxc0l3E1C2/XOyOE=\");\n_c = AddCuttingRecord;\nexport default AddCuttingRecord;\nvar _c;\n$RefreshReg$(_c, \"AddCuttingRecord\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "RoleBasedNavBar", "ColorVariantSelector", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Badge", "Modal", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsFilePdf", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddCuttingRecord", "_s", "fabricDefinitions", "setFabricDefinitions", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "fabricGroups", "setFabricGroups", "id", "Date", "now", "fabric_definition", "variants", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "loadingVariants", "setLoadingVariants", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "showPdfModal", "setShowPdfModal", "submittedRecord", "setSubmittedRecord", "handleResize", "addEventListener", "removeEventListener", "fetchDefinitions", "get", "fetchVariants", "Promise", "all", "then", "definitionsRes", "variantsRes", "data", "catch", "err", "console", "addFabricGroup", "removeFabricGroup", "groupIndex", "length", "newGroups", "filter", "_", "i", "addVariantToGroup", "push", "removeVariantFromGroup", "variantIndex", "handleFabricDefinitionChange", "fabricDefinitionId", "handleVariantChange", "field", "value", "isDuplicateFabricVariant", "variantId", "currentVariantIndex", "some", "variant", "idx", "handleSubmit", "e", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidGroups", "group", "validationError", "selectedVariants", "map", "Boolean", "uniqueVariants", "Set", "variantData", "find", "v", "parseFloat", "available_yard", "total_yard", "color_name", "color", "details", "for<PERSON>ach", "payload", "cutting_date", "product_name", "response", "post", "fabricNames", "recordData", "detail", "_variant$fabric_defin", "_variant$fabric_defin2", "fabric_definition_data", "fabric_name", "add", "fabric_names", "Array", "from", "join", "totalQuantities", "errorMessage", "generatePDF", "pdf", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "line", "generalInfoData", "toString", "toLocaleDateString", "yPos", "row", "headers", "col<PERSON><PERSON><PERSON>", "colPositions", "currentPos", "width", "setFillColor", "rect", "header", "index", "_detail$xs", "_detail$s", "_detail$m", "_detail$l", "_detail$xl", "total", "parseInt", "toFixed", "toLocaleString", "save", "handleCloseModal", "reduce", "acc", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "transition", "padding", "className", "size", "backgroundColor", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "onClick", "disabled", "groupVariants", "Header", "animation", "Select", "fd", "supplier_name", "currentV<PERSON>t", "height", "border", "marginRight", "title", "selected<PERSON><PERSON><PERSON>", "onSelect", "isDuplicateFunction", "step", "min", "isInvalid", "sizeIndex", "sizeKey", "toLowerCase", "sm", "val", "Math", "max", "bg", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/AddCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport ColorVariantSelector from \"../components/ColorVariantSelector\";\r\nimport { Card, Form, Button, Row, Col, <PERSON>, <PERSON><PERSON>, <PERSON>ge, Modal } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\r\nimport jsPDF from 'jspdf';\r\n\r\nconst AddCuttingRecord = () => {\r\n  // Overall cutting record fields\r\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n\r\n  // Fabric definition groups - each group has a fabric definition and its variants\r\n  const [fabricGroups, setFabricGroups] = useState([\r\n    {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }\r\n  ]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [submittedRecord, setSubmittedRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch fabric definitions and variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n\r\n    // Fetch fabric definitions\r\n    const fetchDefinitions = axios.get(\"http://localhost:8000/api/fabric-definitions/\");\r\n    // Fetch all fabric variants\r\n    const fetchVariants = axios.get(\"http://localhost:8000/api/fabric-variants/\");\r\n\r\n    Promise.all([fetchDefinitions, fetchVariants])\r\n      .then(([definitionsRes, variantsRes]) => {\r\n        setFabricDefinitions(definitionsRes.data);\r\n        setAllFabricVariants(variantsRes.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric data:', err);\r\n        setError('Failed to load fabric data. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add a new fabric definition group\r\n  const addFabricGroup = () => {\r\n    setFabricGroups([...fabricGroups, {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n  };\r\n\r\n  // Remove a fabric definition group\r\n  const removeFabricGroup = (groupIndex) => {\r\n    if (fabricGroups.length > 1) {\r\n      const newGroups = fabricGroups.filter((_, i) => i !== groupIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Add a new variant row to a specific fabric group\r\n  const addVariantToGroup = (groupIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].variants.push({ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 });\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Remove a variant from a specific fabric group\r\n  const removeVariantFromGroup = (groupIndex, variantIndex) => {\r\n    const newGroups = [...fabricGroups];\r\n    if (newGroups[groupIndex].variants.length > 1) {\r\n      newGroups[groupIndex].variants = newGroups[groupIndex].variants.filter((_, i) => i !== variantIndex);\r\n      setFabricGroups(newGroups);\r\n    }\r\n  };\r\n\r\n  // Handle fabric definition change for a group\r\n  const handleFabricDefinitionChange = (groupIndex, fabricDefinitionId) => {\r\n    const newGroups = [...fabricGroups];\r\n    newGroups[groupIndex].fabric_definition = fabricDefinitionId;\r\n    // Reset variants when fabric definition changes\r\n    newGroups[groupIndex].variants = [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }];\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Handle variant field change\r\n  const handleVariantChange = (groupIndex, variantIndex, field, value) => {\r\n    const newGroups = [...fabricGroups];\r\n\r\n    // If changing fabric variant, check for duplicates within the same group\r\n    if (field === 'fabric_variant') {\r\n      if (isDuplicateFabricVariant(groupIndex, value, variantIndex)) {\r\n        setError(`This fabric variant is already selected in this fabric group. Please select a different variant.`);\r\n        return;\r\n      } else {\r\n        setError('');\r\n      }\r\n    }\r\n\r\n    newGroups[groupIndex].variants[variantIndex][field] = value;\r\n    setFabricGroups(newGroups);\r\n  };\r\n\r\n  // Check if a fabric variant is already selected in the same group\r\n  const isDuplicateFabricVariant = (groupIndex, variantId, currentVariantIndex) => {\r\n    return fabricGroups[groupIndex].variants.some((variant, idx) =>\r\n      idx !== currentVariantIndex && variant.fabric_variant === variantId && variantId !== ''\r\n    );\r\n  };\r\n\r\n\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any fabric group has valid data\r\n    const hasValidGroups = fabricGroups.some(group =>\r\n      group.fabric_definition && group.variants.some(variant => variant.fabric_variant)\r\n    );\r\n    if (!hasValidGroups) {\r\n      setError('Please select at least one fabric definition and one fabric variant.');\r\n      return;\r\n    }\r\n\r\n    // Validate each fabric group\r\n    let validationError = false;\r\n    for (let groupIndex = 0; groupIndex < fabricGroups.length; groupIndex++) {\r\n      const group = fabricGroups[groupIndex];\r\n\r\n      if (group.fabric_definition && group.variants.some(variant => variant.fabric_variant)) {\r\n        // Check for duplicates within the group\r\n        const selectedVariants = group.variants.map(variant => variant.fabric_variant).filter(Boolean);\r\n        const uniqueVariants = [...new Set(selectedVariants)];\r\n        if (selectedVariants.length !== uniqueVariants.length) {\r\n          setError(`Duplicate fabric variants detected in fabric group ${groupIndex + 1}. Please ensure each variant is selected only once per group.`);\r\n          validationError = true;\r\n          break;\r\n        }\r\n\r\n        // Validate yard availability for each variant\r\n        for (let variant of group.variants) {\r\n          if (variant.fabric_variant) {\r\n            const variantData = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n            if (variantData && parseFloat(variant.yard_usage) > (variantData.available_yard || variantData.total_yard)) {\r\n              setError(`Yard usage for ${variantData.color_name || variantData.color} exceeds available yards (${variantData.available_yard || variantData.total_yard} yards available).`);\r\n              validationError = true;\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (validationError) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      // Flatten fabric groups into details array\r\n      const details = [];\r\n      fabricGroups.forEach(group => {\r\n        if (group.fabric_definition) {\r\n          group.variants.forEach(variant => {\r\n            if (variant.fabric_variant) {\r\n              details.push(variant);\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      const payload = {\r\n        cutting_date: cuttingDate,\r\n        description: description,\r\n        product_name: productName,\r\n        details: details\r\n      };\r\n\r\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\r\n      setSuccess('Cutting record created successfully!');\r\n\r\n      // Store the submitted record for PDF generation\r\n      const fabricNames = new Set();\r\n      const recordData = {\r\n        ...response.data,\r\n        details: response.data.details.map(detail => {\r\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n          if (variant?.fabric_definition_data?.fabric_name) {\r\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\r\n          }\r\n          return {\r\n            ...detail,\r\n            color: variant?.color || 'Unknown',\r\n            color_name: variant?.color_name || variant?.color || 'Unknown',\r\n            fabric_name: variant?.fabric_definition_data?.fabric_name || 'Unknown'\r\n          };\r\n        }),\r\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\r\n        totalQuantities: totalQuantities\r\n      };\r\n\r\n      setSubmittedRecord(recordData);\r\n\r\n      // Show the PDF generation modal\r\n      setShowPdfModal(true);\r\n    } catch (err) {\r\n      console.error('Error creating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to create cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to create cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to generate PDF directly without using html2canvas\r\n  const generatePDF = () => {\r\n    if (!submittedRecord) return;\r\n\r\n    try {\r\n      // Create a new PDF document\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add title\r\n      pdf.setFontSize(titleFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Cutting Record', 105, 20, { align: 'center' });\r\n\r\n      // Add general information section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.text('General Information', 20, 35);\r\n\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'normal');\r\n\r\n      // Draw table for general info\r\n      pdf.line(20, 40, 190, 40); // Top horizontal line\r\n\r\n      const generalInfoData = [\r\n        ['Record ID', submittedRecord.id.toString()],\r\n        ['Product Name', submittedRecord.product_name],\r\n        ['Fabrics Used', submittedRecord.fabric_names],\r\n        ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()],\r\n        ['Description', submittedRecord.description || 'N/A']\r\n      ];\r\n\r\n      let yPos = 45;\r\n      generalInfoData.forEach((row) => {\r\n        pdf.setFont('helvetica', 'bold');\r\n        pdf.text(row[0], 25, yPos);\r\n        pdf.setFont('helvetica', 'normal');\r\n        pdf.text(row[1], 80, yPos);\r\n        yPos += 8;\r\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\r\n      });\r\n\r\n      // Add fabric details section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Fabric Details', 20, yPos + 10);\r\n\r\n      // Table headers for fabric details\r\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\r\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\r\n\r\n      // Calculate starting positions for each column\r\n      const colPositions = [];\r\n      let currentPos = 20;\r\n      colWidths.forEach(width => {\r\n        colPositions.push(currentPos);\r\n        currentPos += width;\r\n      });\r\n\r\n      // Draw table header\r\n      yPos += 15;\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n\r\n      // Draw header background\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      // Draw header text\r\n      headers.forEach((header, index) => {\r\n        pdf.text(header, colPositions[index] + 2, yPos);\r\n      });\r\n\r\n      // Draw horizontal line after header\r\n      yPos += 3;\r\n      pdf.line(20, yPos, 190, yPos);\r\n\r\n      // Draw table rows\r\n      pdf.setFont('helvetica', 'normal');\r\n      submittedRecord.details.forEach((detail) => {\r\n        yPos += 8;\r\n\r\n        // Calculate total for this row\r\n        const total = parseInt(detail.xs || 0) +\r\n                      parseInt(detail.s || 0) +\r\n                      parseInt(detail.m || 0) +\r\n                      parseInt(detail.l || 0) +\r\n                      parseInt(detail.xl || 0);\r\n\r\n        // Draw row data\r\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\r\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\r\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\r\n        pdf.text(detail.xs?.toString() || '0', colPositions[3] + 2, yPos);\r\n        pdf.text(detail.s?.toString() || '0', colPositions[4] + 2, yPos);\r\n        pdf.text(detail.m?.toString() || '0', colPositions[5] + 2, yPos);\r\n        pdf.text(detail.l?.toString() || '0', colPositions[6] + 2, yPos);\r\n        pdf.text(detail.xl?.toString() || '0', colPositions[7] + 2, yPos);\r\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\r\n\r\n        // Draw horizontal line after row\r\n        yPos += 3;\r\n        pdf.line(20, yPos, 190, yPos);\r\n      });\r\n\r\n      // Draw totals row\r\n      yPos += 8;\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Total', colPositions[0] + 2, yPos);\r\n      pdf.text('', colPositions[1] + 2, yPos);\r\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\r\n\r\n      // Add footer\r\n      pdf.setFontSize(smallFontSize);\r\n      pdf.setFont('helvetica', 'italic');\r\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      pdf.text('Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\r\n\r\n      // Reset form after PDF generation\r\n      setShowPdfModal(false);\r\n      setCuttingDate('');\r\n      setDescription('');\r\n      setProductName('');\r\n      setFabricGroups([{\r\n        id: Date.now(),\r\n        fabric_definition: '',\r\n        variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n      }]);\r\n      setValidated(false);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError('Failed to generate PDF. Please try again.');\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle modal close without generating PDF\r\n  const handleCloseModal = () => {\r\n    setShowPdfModal(false);\r\n    // Reset form\r\n    setCuttingDate('');\r\n    setDescription('');\r\n    setProductName('');\r\n    setFabricGroups([{\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }]);\r\n    setValidated(false);\r\n  };\r\n\r\n\r\n\r\n  // Calculate total quantities for all fabric groups\r\n  const totalQuantities = fabricGroups.reduce((acc, group) => {\r\n    group.variants.forEach(variant => {\r\n      if (variant.fabric_variant) {\r\n        acc.xs += parseInt(variant.xs) || 0;\r\n        acc.s += parseInt(variant.s) || 0;\r\n        acc.m += parseInt(variant.m) || 0;\r\n        acc.l += parseInt(variant.l) || 0;\r\n        acc.xl += parseInt(variant.xl) || 0;\r\n        acc.total += (parseInt(variant.xs) || 0) +\r\n                    (parseInt(variant.s) || 0) +\r\n                    (parseInt(variant.m) || 0) +\r\n                    (parseInt(variant.l) || 0) +\r\n                    (parseInt(variant.xl) || 0);\r\n        acc.yard_usage += parseFloat(variant.yard_usage) || 0;\r\n      }\r\n    });\r\n    return acc;\r\n  }, { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 });\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <BsScissors className=\"me-2\" />\r\n          Add Cutting Record\r\n        </h2>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addFabricGroup}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Definition\r\n                </Button>\r\n              </div>\r\n\r\n              {fabricGroups.map((group, groupIndex) => {\r\n                // Get fabric variants for the selected fabric definition\r\n                const groupVariants = group.fabric_definition\r\n                  ? allFabricVariants.filter(v => v.fabric_definition === parseInt(group.fabric_definition))\r\n                  : [];\r\n\r\n                return (\r\n                  <Card key={group.id} className=\"mb-4 border\">\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-primary text-white\">\r\n                      <h5 className=\"mb-0\">Fabric Definition #{groupIndex + 1}</h5>\r\n                      <Button\r\n                        variant=\"outline-light\"\r\n                        size=\"sm\"\r\n                        onClick={() => removeFabricGroup(groupIndex)}\r\n                        disabled={fabricGroups.length === 1}\r\n                      >\r\n                        <BsTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      {/* Fabric Definition Selection */}\r\n                      <Row className=\"mb-3\">\r\n                        <Col md={12}>\r\n                          <Form.Group>\r\n                            <Form.Label><strong>Select Fabric Definition</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading fabric definitions...</span>\r\n                              </div>\r\n                            ) : (\r\n                              <Form.Select\r\n                                value={group.fabric_definition}\r\n                                onChange={(e) => handleFabricDefinitionChange(groupIndex, e.target.value)}\r\n                                required\r\n                              >\r\n                                <option value=\"\">Select Fabric Definition</option>\r\n                                {fabricDefinitions.map((fd) => (\r\n                                  <option key={fd.id} value={fd.id}>\r\n                                    {fd.fabric_name} - {fd.supplier_name || 'Unknown Supplier'}\r\n                                  </option>\r\n                                ))}\r\n                              </Form.Select>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n\r\n                      {/* Fabric Variants Section */}\r\n                      {group.fabric_definition && (\r\n                        <>\r\n                          <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                            <h6 className=\"mb-0\">Fabric Variants</h6>\r\n                            <Button\r\n                              variant=\"outline-success\"\r\n                              size=\"sm\"\r\n                              onClick={() => addVariantToGroup(groupIndex)}\r\n                              disabled={isSubmitting}\r\n                            >\r\n                              <BsPlus className=\"me-1\" /> Add Variant\r\n                            </Button>\r\n                          </div>\r\n\r\n                          {group.variants.map((variant, variantIndex) => {\r\n                            const currentVariant = allFabricVariants.find(v => v.id === variant.fabric_variant);\r\n\r\n                            return (\r\n                              <Card key={variantIndex} className=\"mb-3 border-light\">\r\n                                <Card.Body>\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <div className=\"d-flex align-items-center\">\r\n                                      <h6 className=\"mb-0 me-2\">Variant #{variantIndex + 1}</h6>\r\n                                      {currentVariant && (\r\n                                        <div className=\"d-flex align-items-center\">\r\n                                          <div\r\n                                            style={{\r\n                                              width: '16px',\r\n                                              height: '16px',\r\n                                              backgroundColor: currentVariant.color,\r\n                                              border: '1px solid #ccc',\r\n                                              borderRadius: '3px',\r\n                                              marginRight: '6px'\r\n                                            }}\r\n                                            title={`Color: ${currentVariant.color}`}\r\n                                          />\r\n                                          <small className=\"text-muted\">\r\n                                            {currentVariant.color_name || currentVariant.color}\r\n                                          </small>\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                    <Button\r\n                                      variant=\"outline-danger\"\r\n                                      size=\"sm\"\r\n                                      onClick={() => removeVariantFromGroup(groupIndex, variantIndex)}\r\n                                      disabled={group.variants.length === 1}\r\n                                    >\r\n                                      <BsTrash className=\"me-1\" /> Remove\r\n                                    </Button>\r\n                                  </div>\r\n\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                                        <ColorVariantSelector\r\n                                          variants={groupVariants}\r\n                                          selectedValue={variant.fabric_variant}\r\n                                          onSelect={(value) => handleVariantChange(groupIndex, variantIndex, 'fabric_variant', value)}\r\n                                          placeholder=\"Select Color Variant\"\r\n                                          isDuplicateFunction={isDuplicateFabricVariant}\r\n                                          groupIndex={groupIndex}\r\n                                          variantIndex={variantIndex}\r\n                                        />\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <div className=\"d-flex justify-content-between align-items-center mb-1\">\r\n                                          <Form.Label className=\"mb-0\"><strong>Yard Usage</strong></Form.Label>\r\n                                          {currentVariant && (\r\n                                            <div className=\"d-flex align-items-center\">\r\n                                              <div\r\n                                                style={{\r\n                                                  width: '16px',\r\n                                                  height: '16px',\r\n                                                  backgroundColor: currentVariant.color,\r\n                                                  border: '1px solid #ccc',\r\n                                                  borderRadius: '3px',\r\n                                                  marginRight: '8px'\r\n                                                }}\r\n                                                title={`Color: ${currentVariant.color_name || currentVariant.color}`}\r\n                                              />\r\n                                              <span className={parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\"}>\r\n                                                {currentVariant.color_name || currentVariant.color} - Available: {currentVariant.available_yard || currentVariant.total_yard} yards\r\n                                              </span>\r\n                                            </div>\r\n                                          )}\r\n                                        </div>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={variant.yard_usage}\r\n                                          onChange={(e) => handleVariantChange(groupIndex, variantIndex, 'yard_usage', e.target.value)}\r\n                                          required\r\n                                          placeholder=\"Enter yards used\"\r\n                                          isInvalid={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)}\r\n                                          className={currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\"}\r\n                                          style={{ height: '38px' }}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {currentVariant && parseFloat(variant.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)\r\n                                            ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)`\r\n                                            : \"Please enter valid yard usage.\"}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  <Form.Label className=\"mt-2\"><strong>Size Quantities</strong></Form.Label>\r\n                                  <Row>\r\n                                    {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\r\n                                      const sizeKey = size.toLowerCase();\r\n                                      return (\r\n                                        <Col key={sizeIndex} xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                          <Form.Group>\r\n                                            <Form.Label className=\"text-center d-block\">{size}</Form.Label>\r\n                                            <Form.Control\r\n                                              type=\"number\"\r\n                                              min=\"0\"\r\n                                              value={variant[sizeKey]}\r\n                                              onChange={(e) => {\r\n                                                const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                                handleVariantChange(groupIndex, variantIndex, sizeKey, val);\r\n                                              }}\r\n                                              className=\"text-center\"\r\n                                            />\r\n                                          </Form.Group>\r\n                                        </Col>\r\n                                      );\r\n                                    })}\r\n                                    <Col xs={6} sm={4} md={2} className=\"mb-3\">\r\n                                      <Form.Group>\r\n                                        <Form.Label className=\"text-center d-block\">Total</Form.Label>\r\n                                        <div className=\"form-control text-center bg-light\">\r\n                                          {parseInt(variant.xs || 0) +\r\n                                           parseInt(variant.s || 0) +\r\n                                           parseInt(variant.m || 0) +\r\n                                           parseInt(variant.l || 0) +\r\n                                           parseInt(variant.xl || 0)}\r\n                                        </div>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n                                </Card.Body>\r\n                              </Card>\r\n                            );\r\n                          })}\r\n                        </>\r\n                      )}\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-end mb-4\">\r\n                <Card className=\"border-0\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                  <Card.Body className=\"py-2\">\r\n                    <div className=\"d-flex flex-column\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <strong className=\"me-2\">Total Quantities:</strong>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XS: {totalQuantities.xs}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">S: {totalQuantities.s}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">M: {totalQuantities.m}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">L: {totalQuantities.l}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XL: {totalQuantities.xl}</Badge>\r\n                        <Badge bg=\"success\" className=\"ms-2\">Total: {totalQuantities.total}</Badge>\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <strong className=\"me-2\">Total Yard Usage:</strong>\r\n                        <Badge bg=\"info\">{totalQuantities.yard_usage.toFixed(2)} yards</Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* PDF Generation Modal */}\r\n      <Modal show={showPdfModal} onHide={handleCloseModal} size=\"lg\" centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Generate Cutting Record PDF</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Would you like to generate a PDF for this cutting record?</p>\r\n\r\n          {submittedRecord && (\r\n            <div className=\"mb-3\">\r\n              <p>The PDF will include the following information:</p>\r\n              <ul>\r\n                <li><strong>Product Name:</strong> {submittedRecord.product_name}</li>\r\n                <li><strong>Fabric:</strong> {submittedRecord.fabric_name}</li>\r\n                <li><strong>Cutting Date:</strong> {new Date(submittedRecord.cutting_date).toLocaleDateString()}</li>\r\n                <li><strong>Total Quantities:</strong> XS: {submittedRecord.totalQuantities.xs},\r\n                  S: {submittedRecord.totalQuantities.s},\r\n                  M: {submittedRecord.totalQuantities.m},\r\n                  L: {submittedRecord.totalQuantities.l},\r\n                  XL: {submittedRecord.totalQuantities.xl}</li>\r\n                <li><strong>Total Items:</strong> {submittedRecord.totalQuantities.total}</li>\r\n                <li><strong>Total Yard Usage:</strong> {submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards</li>\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            No, Skip\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={generatePDF}>\r\n            <BsFilePdf className=\"me-2\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddCuttingRecord;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9G,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAC/C;IACEuC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;IACdC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,CAAC;MAAEC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC;EACnF,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC6D,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoE,YAAY,GAAGA,CAAA,KAAM;MACzBT,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMR,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACdmD,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAMoB,gBAAgB,GAAGtE,KAAK,CAACuE,GAAG,CAAC,+CAA+C,CAAC;IACnF;IACA,MAAMC,aAAa,GAAGxE,KAAK,CAACuE,GAAG,CAAC,4CAA4C,CAAC;IAE7EE,OAAO,CAACC,GAAG,CAAC,CAACJ,gBAAgB,EAAEE,aAAa,CAAC,CAAC,CAC3CG,IAAI,CAAC,CAAC,CAACC,cAAc,EAAEC,WAAW,CAAC,KAAK;MACvCnD,oBAAoB,CAACkD,cAAc,CAACE,IAAI,CAAC;MACzClD,oBAAoB,CAACiD,WAAW,CAACC,IAAI,CAAC;MACtC5B,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACD6B,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;MACjD5B,QAAQ,CAAC,+CAA+C,CAAC;MACzDF,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAC3B9C,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE;MAChCE,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,CAAC;QAAEC,cAAc,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;IACnF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAIjD,YAAY,CAACkD,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,SAAS,GAAGnD,YAAY,CAACoD,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,UAAU,CAAC;MACjEhD,eAAe,CAACkD,SAAS,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAIN,UAAU,IAAK;IACxC,MAAME,SAAS,GAAG,CAAC,GAAGnD,YAAY,CAAC;IACnCmD,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,CAACkD,IAAI,CAAC;MAAEjD,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC;IAC3GZ,eAAe,CAACkD,SAAS,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMM,sBAAsB,GAAGA,CAACR,UAAU,EAAES,YAAY,KAAK;IAC3D,MAAMP,SAAS,GAAG,CAAC,GAAGnD,YAAY,CAAC;IACnC,IAAImD,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,CAAC4C,MAAM,GAAG,CAAC,EAAE;MAC7CC,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,GAAG6C,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,CAAC8C,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKI,YAAY,CAAC;MACpGzD,eAAe,CAACkD,SAAS,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMQ,4BAA4B,GAAGA,CAACV,UAAU,EAAEW,kBAAkB,KAAK;IACvE,MAAMT,SAAS,GAAG,CAAC,GAAGnD,YAAY,CAAC;IACnCmD,SAAS,CAACF,UAAU,CAAC,CAAC5C,iBAAiB,GAAGuD,kBAAkB;IAC5D;IACAT,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,GAAG,CAAC;MAAEC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC;IACzGZ,eAAe,CAACkD,SAAS,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAGA,CAACZ,UAAU,EAAES,YAAY,EAAEI,KAAK,EAAEC,KAAK,KAAK;IACtE,MAAMZ,SAAS,GAAG,CAAC,GAAGnD,YAAY,CAAC;;IAEnC;IACA,IAAI8D,KAAK,KAAK,gBAAgB,EAAE;MAC9B,IAAIE,wBAAwB,CAACf,UAAU,EAAEc,KAAK,EAAEL,YAAY,CAAC,EAAE;QAC7DzC,QAAQ,CAAC,kGAAkG,CAAC;QAC5G;MACF,CAAC,MAAM;QACLA,QAAQ,CAAC,EAAE,CAAC;MACd;IACF;IAEAkC,SAAS,CAACF,UAAU,CAAC,CAAC3C,QAAQ,CAACoD,YAAY,CAAC,CAACI,KAAK,CAAC,GAAGC,KAAK;IAC3D9D,eAAe,CAACkD,SAAS,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMa,wBAAwB,GAAGA,CAACf,UAAU,EAAEgB,SAAS,EAAEC,mBAAmB,KAAK;IAC/E,OAAOlE,YAAY,CAACiD,UAAU,CAAC,CAAC3C,QAAQ,CAAC6D,IAAI,CAAC,CAACC,OAAO,EAAEC,GAAG,KACzDA,GAAG,KAAKH,mBAAmB,IAAIE,OAAO,CAAC7D,cAAc,KAAK0D,SAAS,IAAIA,SAAS,KAAK,EACvF,CAAC;EACH,CAAC;;EAID;EACA,MAAMK,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGF,CAAC,CAACG,aAAa;IAC5B,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,KAAK,KAAK,EAAE;MAClCJ,CAAC,CAACK,eAAe,CAAC,CAAC;MACnBjD,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;;IAEA;IACA,MAAMkD,cAAc,GAAG7E,YAAY,CAACmE,IAAI,CAACW,KAAK,IAC5CA,KAAK,CAACzE,iBAAiB,IAAIyE,KAAK,CAACxE,QAAQ,CAAC6D,IAAI,CAACC,OAAO,IAAIA,OAAO,CAAC7D,cAAc,CAClF,CAAC;IACD,IAAI,CAACsE,cAAc,EAAE;MACnB5D,QAAQ,CAAC,sEAAsE,CAAC;MAChF;IACF;;IAEA;IACA,IAAI8D,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAI9B,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGjD,YAAY,CAACkD,MAAM,EAAED,UAAU,EAAE,EAAE;MACvE,MAAM6B,KAAK,GAAG9E,YAAY,CAACiD,UAAU,CAAC;MAEtC,IAAI6B,KAAK,CAACzE,iBAAiB,IAAIyE,KAAK,CAACxE,QAAQ,CAAC6D,IAAI,CAACC,OAAO,IAAIA,OAAO,CAAC7D,cAAc,CAAC,EAAE;QACrF;QACA,MAAMyE,gBAAgB,GAAGF,KAAK,CAACxE,QAAQ,CAAC2E,GAAG,CAACb,OAAO,IAAIA,OAAO,CAAC7D,cAAc,CAAC,CAAC6C,MAAM,CAAC8B,OAAO,CAAC;QAC9F,MAAMC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,gBAAgB,CAAC,CAAC;QACrD,IAAIA,gBAAgB,CAAC9B,MAAM,KAAKiC,cAAc,CAACjC,MAAM,EAAE;UACrDjC,QAAQ,CAAC,sDAAsDgC,UAAU,GAAG,CAAC,+DAA+D,CAAC;UAC7I8B,eAAe,GAAG,IAAI;UACtB;QACF;;QAEA;QACA,KAAK,IAAIX,OAAO,IAAIU,KAAK,CAACxE,QAAQ,EAAE;UAClC,IAAI8D,OAAO,CAAC7D,cAAc,EAAE;YAC1B,MAAM8E,WAAW,GAAG7F,iBAAiB,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKkE,OAAO,CAAC7D,cAAc,CAAC;YAChF,IAAI8E,WAAW,IAAIG,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAI6E,WAAW,CAACI,cAAc,IAAIJ,WAAW,CAACK,UAAU,CAAC,EAAE;cAC1GzE,QAAQ,CAAC,kBAAkBoE,WAAW,CAACM,UAAU,IAAIN,WAAW,CAACO,KAAK,6BAA6BP,WAAW,CAACI,cAAc,IAAIJ,WAAW,CAACK,UAAU,oBAAoB,CAAC;cAC5KX,eAAe,GAAG,IAAI;cACtB;YACF;UACF;QACF;MACF;IACF;IAEA,IAAIA,eAAe,EAAE;MACnBpD,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAA,YAAY,CAAC,IAAI,CAAC;IAClBN,eAAe,CAAC,IAAI,CAAC;IACrBJ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA,MAAM0E,OAAO,GAAG,EAAE;MAClB7F,YAAY,CAAC8F,OAAO,CAAChB,KAAK,IAAI;QAC5B,IAAIA,KAAK,CAACzE,iBAAiB,EAAE;UAC3ByE,KAAK,CAACxE,QAAQ,CAACwF,OAAO,CAAC1B,OAAO,IAAI;YAChC,IAAIA,OAAO,CAAC7D,cAAc,EAAE;cAC1BsF,OAAO,CAACrC,IAAI,CAACY,OAAO,CAAC;YACvB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAM2B,OAAO,GAAG;QACdC,YAAY,EAAEtG,WAAW;QACzBE,WAAW,EAAEA,WAAW;QACxBqG,YAAY,EAAEnG,WAAW;QACzB+F,OAAO,EAAEA;MACX,CAAC;MAED,MAAMK,QAAQ,GAAG,MAAMrI,KAAK,CAACsI,IAAI,CAAC,oDAAoD,EAAEJ,OAAO,CAAC;MAChG5E,UAAU,CAAC,sCAAsC,CAAC;;MAElD;MACA,MAAMiF,WAAW,GAAG,IAAIhB,GAAG,CAAC,CAAC;MAC7B,MAAMiB,UAAU,GAAG;QACjB,GAAGH,QAAQ,CAACvD,IAAI;QAChBkD,OAAO,EAAEK,QAAQ,CAACvD,IAAI,CAACkD,OAAO,CAACZ,GAAG,CAACqB,MAAM,IAAI;UAAA,IAAAC,qBAAA,EAAAC,sBAAA;UAC3C,MAAMpC,OAAO,GAAG5E,iBAAiB,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKoG,MAAM,CAAC/F,cAAc,CAAC;UAC3E,IAAI6D,OAAO,aAAPA,OAAO,gBAAAmC,qBAAA,GAAPnC,OAAO,CAAEqC,sBAAsB,cAAAF,qBAAA,eAA/BA,qBAAA,CAAiCG,WAAW,EAAE;YAChDN,WAAW,CAACO,GAAG,CAACvC,OAAO,CAACqC,sBAAsB,CAACC,WAAW,CAAC;UAC7D;UACA,OAAO;YACL,GAAGJ,MAAM;YACTV,KAAK,EAAE,CAAAxB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,KAAK,KAAI,SAAS;YAClCD,UAAU,EAAE,CAAAvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuB,UAAU,MAAIvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,KAAK,KAAI,SAAS;YAC9Dc,WAAW,EAAE,CAAAtC,OAAO,aAAPA,OAAO,wBAAAoC,sBAAA,GAAPpC,OAAO,CAAEqC,sBAAsB,cAAAD,sBAAA,uBAA/BA,sBAAA,CAAiCE,WAAW,KAAI;UAC/D,CAAC;QACH,CAAC,CAAC;QACFE,YAAY,EAAEC,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;QAC7DC,eAAe,EAAEA;MACnB,CAAC;MAEDjF,kBAAkB,CAACsE,UAAU,CAAC;;MAE9B;MACAxE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;MACpD,IAAIA,GAAG,CAACqD,QAAQ,IAAIrD,GAAG,CAACqD,QAAQ,CAACvD,IAAI,EAAE;QACrC;QACA,MAAMsE,YAAY,GAAG,OAAOpE,GAAG,CAACqD,QAAQ,CAACvD,IAAI,KAAK,QAAQ,GACtDE,GAAG,CAACqD,QAAQ,CAACvD,IAAI,GACjB,4DAA4D;QAChE1B,QAAQ,CAACgG,YAAY,CAAC;MACxB,CAAC,MAAM;QACLhG,QAAQ,CAAC,oDAAoD,CAAC;MAChE;IACF,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM6F,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACpF,eAAe,EAAE;IAEtB,IAAI;MACF;MACA,MAAMqF,GAAG,GAAG,IAAIpI,KAAK,CAAC;QACpBqI,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC;MAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAExD;MACAX,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,EAAE,CAAC;MAEvCV,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACAT,GAAG,CAACY,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;;MAE3B,MAAMC,eAAe,GAAG,CACtB,CAAC,WAAW,EAAElG,eAAe,CAAC5B,EAAE,CAAC+H,QAAQ,CAAC,CAAC,CAAC,EAC5C,CAAC,cAAc,EAAEnG,eAAe,CAACmE,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAEnE,eAAe,CAAC8E,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAE,IAAIzG,IAAI,CAAC2B,eAAe,CAACkE,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC,CAAC,EAC7E,CAAC,aAAa,EAAEpG,eAAe,CAAClC,WAAW,IAAI,KAAK,CAAC,CACtD;MAED,IAAIuI,IAAI,GAAG,EAAE;MACbH,eAAe,CAAClC,OAAO,CAAEsC,GAAG,IAAK;QAC/BjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BhB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BA,IAAI,IAAI,CAAC;QACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAEA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC;;MAEF;MACAhB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEM,IAAI,GAAG,EAAE,CAAC;;MAEzC;MACA,MAAME,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC;MACrF,MAAMC,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAEtD;MACA,MAAMC,YAAY,GAAG,EAAE;MACvB,IAAIC,UAAU,GAAG,EAAE;MACnBF,SAAS,CAACxC,OAAO,CAAC2C,KAAK,IAAI;QACzBF,YAAY,CAAC/E,IAAI,CAACgF,UAAU,CAAC;QAC7BA,UAAU,IAAIC,KAAK;MACrB,CAAC,CAAC;;MAEF;MACAN,IAAI,IAAI,EAAE;MACVhB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;;MAEhC;MACAT,GAAG,CAACuB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,EAAER,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;;MAEnC;MACAE,OAAO,CAACvC,OAAO,CAAC,CAAC8C,MAAM,EAAEC,KAAK,KAAK;QACjC1B,GAAG,CAACU,IAAI,CAACe,MAAM,EAAEL,YAAY,CAACM,KAAK,CAAC,GAAG,CAAC,EAAEV,IAAI,CAAC;MACjD,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;;MAE7B;MACAhB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClC9F,eAAe,CAAC+D,OAAO,CAACC,OAAO,CAAEQ,MAAM,IAAK;QAAA,IAAAwC,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA;QAC1Cf,IAAI,IAAI,CAAC;;QAET;QACA,MAAMgB,KAAK,GAAGC,QAAQ,CAAC9C,MAAM,CAAC7F,EAAE,IAAI,CAAC,CAAC,GACxB2I,QAAQ,CAAC9C,MAAM,CAAC5F,CAAC,IAAI,CAAC,CAAC,GACvB0I,QAAQ,CAAC9C,MAAM,CAAC3F,CAAC,IAAI,CAAC,CAAC,GACvByI,QAAQ,CAAC9C,MAAM,CAAC1F,CAAC,IAAI,CAAC,CAAC,GACvBwI,QAAQ,CAAC9C,MAAM,CAACzF,EAAE,IAAI,CAAC,CAAC;;QAEtC;QACAsG,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACI,WAAW,IAAI,SAAS,EAAE6B,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACpEhB,GAAG,CAACU,IAAI,CAACvB,MAAM,CAACX,UAAU,IAAIW,MAAM,CAACV,KAAK,EAAE2C,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACtEhB,GAAG,CAACU,IAAI,CAAC,GAAGvB,MAAM,CAAC9F,UAAU,QAAQ,EAAE+H,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAiB,UAAA,GAAAxC,MAAM,CAAC7F,EAAE,cAAAqI,UAAA,uBAATA,UAAA,CAAWb,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAkB,SAAA,GAAAzC,MAAM,CAAC5F,CAAC,cAAAqI,SAAA,uBAARA,SAAA,CAAUd,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAmB,SAAA,GAAA1C,MAAM,CAAC3F,CAAC,cAAAqI,SAAA,uBAARA,SAAA,CAAUf,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAoB,SAAA,GAAA3C,MAAM,CAAC1F,CAAC,cAAAqI,SAAA,uBAARA,SAAA,CAAUhB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAqB,UAAA,GAAA5C,MAAM,CAACzF,EAAE,cAAAqI,UAAA,uBAATA,UAAA,CAAWjB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAACsB,KAAK,CAAClB,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;QAErD;QACAA,IAAI,IAAI,CAAC;QACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACThB,GAAG,CAACuB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BvB,GAAG,CAACwB,IAAI,CAAC,EAAE,EAAER,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;MAEnChB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,EAAEU,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAC5ChB,GAAG,CAACU,IAAI,CAAC,EAAE,EAAEU,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACvChB,GAAG,CAACU,IAAI,CAAC,GAAG/F,eAAe,CAACkF,eAAe,CAACxG,UAAU,CAAC6I,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAEd,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACrGhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACvG,EAAE,CAACwH,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACtG,CAAC,CAACuH,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACrG,CAAC,CAACsH,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACpG,CAAC,CAACqH,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACnG,EAAE,CAACoH,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFhB,GAAG,CAACU,IAAI,CAAC/F,eAAe,CAACkF,eAAe,CAACmC,KAAK,CAAClB,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;MAErF;MACAhB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC;MAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,IAAI1H,IAAI,CAAC,CAAC,CAACmJ,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAExB,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFX,GAAG,CAACU,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE5E;MACAX,GAAG,CAACoC,IAAI,CAAC,kBAAkBzH,eAAe,CAAC5B,EAAE,IAAI4B,eAAe,CAACmE,YAAY,MAAM,CAAC;;MAEpF;MACApE,eAAe,CAAC,KAAK,CAAC;MACtBlC,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBE,eAAe,CAAC,CAAC;QACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,CAAC;UAAEC,cAAc,EAAE,EAAE;UAAEC,UAAU,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;MACnF,CAAC,CAAC,CAAC;MACHc,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd8B,OAAO,CAAC9B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2CAA2C,CAAC;MACrDY,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM2H,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3H,eAAe,CAAC,KAAK,CAAC;IACtB;IACAlC,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClBE,eAAe,CAAC,CAAC;MACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,iBAAiB,EAAE,EAAE;MACrBC,QAAQ,EAAE,CAAC;QAAEC,cAAc,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;IACnF,CAAC,CAAC,CAAC;IACHc,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAID;EACA,MAAMqF,eAAe,GAAGhH,YAAY,CAACyJ,MAAM,CAAC,CAACC,GAAG,EAAE5E,KAAK,KAAK;IAC1DA,KAAK,CAACxE,QAAQ,CAACwF,OAAO,CAAC1B,OAAO,IAAI;MAChC,IAAIA,OAAO,CAAC7D,cAAc,EAAE;QAC1BmJ,GAAG,CAACjJ,EAAE,IAAI2I,QAAQ,CAAChF,OAAO,CAAC3D,EAAE,CAAC,IAAI,CAAC;QACnCiJ,GAAG,CAAChJ,CAAC,IAAI0I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,CAAC,IAAI,CAAC;QACjCgJ,GAAG,CAAC/I,CAAC,IAAIyI,QAAQ,CAAChF,OAAO,CAACzD,CAAC,CAAC,IAAI,CAAC;QACjC+I,GAAG,CAAC9I,CAAC,IAAIwI,QAAQ,CAAChF,OAAO,CAACxD,CAAC,CAAC,IAAI,CAAC;QACjC8I,GAAG,CAAC7I,EAAE,IAAIuI,QAAQ,CAAChF,OAAO,CAACvD,EAAE,CAAC,IAAI,CAAC;QACnC6I,GAAG,CAACP,KAAK,IAAI,CAACC,QAAQ,CAAChF,OAAO,CAAC3D,EAAE,CAAC,IAAI,CAAC,KAC1B2I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,CAAC,IAAI,CAAC,CAAC,IACzB0I,QAAQ,CAAChF,OAAO,CAACzD,CAAC,CAAC,IAAI,CAAC,CAAC,IACzByI,QAAQ,CAAChF,OAAO,CAACxD,CAAC,CAAC,IAAI,CAAC,CAAC,IACzBwI,QAAQ,CAAChF,OAAO,CAACvD,EAAE,CAAC,IAAI,CAAC,CAAC;QACvC6I,GAAG,CAAClJ,UAAU,IAAIgF,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAI,CAAC;MACvD;IACF,CAAC,CAAC;IACF,OAAOkJ,GAAG;EACZ,CAAC,EAAE;IAAEjJ,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAEsI,KAAK,EAAE,CAAC;IAAE3I,UAAU,EAAE;EAAE,CAAC,CAAC;EAE/D,oBACEvB,OAAA,CAAAE,SAAA;IAAAwK,QAAA,gBACE1K,OAAA,CAACnB,eAAe;MAAA8L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnB9K,OAAA;MACE+K,KAAK,EAAE;QACLC,UAAU,EAAE3I,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CmH,KAAK,EAAE,eAAenH,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzD4I,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE;MACX,CAAE;MAAAR,QAAA,gBAEF1K,OAAA;QAAImL,SAAS,EAAC,MAAM;QAAAT,QAAA,gBAClB1K,OAAA,CAACR,UAAU;UAAC2L,SAAS,EAAC;QAAM;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJ7I,OAAO,iBACNjC,OAAA,CAACX,KAAK;QAAC8F,OAAO,EAAC,SAAS;QAACgG,SAAS,EAAC,2BAA2B;QAAAT,QAAA,gBAC5D1K,OAAA,CAACL,cAAc;UAACwL,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5C7I,OAAO;MAAA;QAAA0I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAEA/I,KAAK,iBACJ/B,OAAA,CAACX,KAAK;QAAC8F,OAAO,EAAC,QAAQ;QAACgG,SAAS,EAAC,2BAA2B;QAAAT,QAAA,gBAC3D1K,OAAA,CAACJ,qBAAqB;UAACuL,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnD/I,KAAK;MAAA;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED9K,OAAA,CAACjB,IAAI;QAACoM,SAAS,EAAC,gBAAgB;QAACJ,KAAK,EAAE;UAAEM,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAC3F1K,OAAA,CAACjB,IAAI,CAACwM,IAAI;UAAAb,QAAA,eACR1K,OAAA,CAAChB,IAAI;YAACwM,UAAU;YAAC/I,SAAS,EAAEA,SAAU;YAACgJ,QAAQ,EAAEpG,YAAa;YAAAqF,QAAA,gBAC5D1K,OAAA,CAACd,GAAG;cAAAwL,QAAA,eACF1K,OAAA,CAACb,GAAG;gBAACuM,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;kBAACR,SAAS,EAAC,MAAM;kBAAAT,QAAA,gBAC1B1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;oBAAAlB,QAAA,eAAC1K,OAAA;sBAAA0K,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXhH,KAAK,EAAEjE,WAAY;oBACnBkL,QAAQ,EAAGzG,CAAC,IAAKxE,cAAc,CAACwE,CAAC,CAAC0G,MAAM,CAAClH,KAAK,CAAE;oBAChDmH,WAAW,EAAC,oBAAoB;oBAChCC,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACF9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAApB,QAAA,EAAC;kBAEtC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9K,OAAA,CAACd,GAAG;cAAAwL,QAAA,gBACF1K,OAAA,CAACb,GAAG;gBAACuM,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;kBAACR,SAAS,EAAC,MAAM;kBAAAT,QAAA,gBAC1B1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;oBAAAlB,QAAA,eAAC1K,OAAA;sBAAA0K,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXhH,KAAK,EAAErE,WAAY;oBACnBsL,QAAQ,EAAGzG,CAAC,IAAK5E,cAAc,CAAC4E,CAAC,CAAC0G,MAAM,CAAClH,KAAK,CAAE;oBAChDoH,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACF9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAApB,QAAA,EAAC;kBAEtC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN9K,OAAA,CAACb,GAAG;gBAACuM,EAAE,EAAE,CAAE;gBAAAhB,QAAA,eACT1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;kBAACR,SAAS,EAAC,MAAM;kBAAAT,QAAA,gBAC1B1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;oBAAAlB,QAAA,eAAC1K,OAAA;sBAAA0K,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrD9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO;oBACXO,EAAE,EAAC,UAAU;oBACbC,IAAI,EAAE,CAAE;oBACRvH,KAAK,EAAEnE,WAAY;oBACnBoL,QAAQ,EAAGzG,CAAC,IAAK1E,cAAc,CAAC0E,CAAC,CAAC0G,MAAM,CAAClH,KAAK,CAAE;oBAChDmH,WAAW,EAAC;kBAA4C;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAIN9K,OAAA;cAAKmL,SAAS,EAAC,gFAAgF;cAAAT,QAAA,gBAC7F1K,OAAA;gBAAImL,SAAS,EAAC,MAAM;gBAAAT,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxC9K,OAAA,CAACf,MAAM;gBACLkG,OAAO,EAAC,SAAS;gBACjBiG,IAAI,EAAC,IAAI;gBACTkB,OAAO,EAAExI,cAAe;gBACxByI,QAAQ,EAAEpK,YAAa;gBAAAuI,QAAA,gBAEvB1K,OAAA,CAACP,MAAM;kBAAC0L,SAAS,EAAC;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,0BAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEL/J,YAAY,CAACiF,GAAG,CAAC,CAACH,KAAK,EAAE7B,UAAU,KAAK;cACvC;cACA,MAAMwI,aAAa,GAAG3G,KAAK,CAACzE,iBAAiB,GACzCb,iBAAiB,CAAC4D,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAAClF,iBAAiB,KAAK+I,QAAQ,CAACtE,KAAK,CAACzE,iBAAiB,CAAC,CAAC,GACxF,EAAE;cAEN,oBACEpB,OAAA,CAACjB,IAAI;gBAAgBoM,SAAS,EAAC,aAAa;gBAAAT,QAAA,gBAC1C1K,OAAA,CAACjB,IAAI,CAAC0N,MAAM;kBAACtB,SAAS,EAAC,yEAAyE;kBAAAT,QAAA,gBAC9F1K,OAAA;oBAAImL,SAAS,EAAC,MAAM;oBAAAT,QAAA,GAAC,qBAAmB,EAAC1G,UAAU,GAAG,CAAC;kBAAA;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7D9K,OAAA,CAACf,MAAM;oBACLkG,OAAO,EAAC,eAAe;oBACvBiG,IAAI,EAAC,IAAI;oBACTkB,OAAO,EAAEA,CAAA,KAAMvI,iBAAiB,CAACC,UAAU,CAAE;oBAC7CuI,QAAQ,EAAExL,YAAY,CAACkD,MAAM,KAAK,CAAE;oBAAAyG,QAAA,gBAEpC1K,OAAA,CAACN,OAAO;sBAACyL,SAAS,EAAC;oBAAM;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAC9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACd9K,OAAA,CAACjB,IAAI,CAACwM,IAAI;kBAAAb,QAAA,gBAER1K,OAAA,CAACd,GAAG;oBAACiM,SAAS,EAAC,MAAM;oBAAAT,QAAA,eACnB1K,OAAA,CAACb,GAAG;sBAACuM,EAAE,EAAE,EAAG;sBAAAhB,QAAA,eACV1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;wBAAAjB,QAAA,gBACT1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;0BAAAlB,QAAA,eAAC1K,OAAA;4BAAA0K,QAAA,EAAQ;0BAAwB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,EACjEjJ,eAAe,gBACd7B,OAAA;0BAAKmL,SAAS,EAAC,2BAA2B;0BAAAT,QAAA,gBACxC1K,OAAA,CAACZ,OAAO;4BAACsN,SAAS,EAAC,QAAQ;4BAACtB,IAAI,EAAC,IAAI;4BAACD,SAAS,EAAC;0BAAM;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACzD9K,OAAA;4BAAA0K,QAAA,EAAM;0BAA6B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC,gBAEN9K,OAAA,CAAChB,IAAI,CAAC2N,MAAM;0BACV7H,KAAK,EAAEe,KAAK,CAACzE,iBAAkB;0BAC/B2K,QAAQ,EAAGzG,CAAC,IAAKZ,4BAA4B,CAACV,UAAU,EAAEsB,CAAC,CAAC0G,MAAM,CAAClH,KAAK,CAAE;0BAC1EoH,QAAQ;0BAAAxB,QAAA,gBAER1K,OAAA;4BAAQ8E,KAAK,EAAC,EAAE;4BAAA4F,QAAA,EAAC;0BAAwB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,EACjDzK,iBAAiB,CAAC2F,GAAG,CAAE4G,EAAE,iBACxB5M,OAAA;4BAAoB8E,KAAK,EAAE8H,EAAE,CAAC3L,EAAG;4BAAAyJ,QAAA,GAC9BkC,EAAE,CAACnF,WAAW,EAAC,KAAG,EAACmF,EAAE,CAACC,aAAa,IAAI,kBAAkB;0BAAA,GAD/CD,EAAE,CAAC3L,EAAE;4BAAA0J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEV,CACT,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACS,CACd;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGLjF,KAAK,CAACzE,iBAAiB,iBACtBpB,OAAA,CAAAE,SAAA;oBAAAwK,QAAA,gBACE1K,OAAA;sBAAKmL,SAAS,EAAC,wDAAwD;sBAAAT,QAAA,gBACrE1K,OAAA;wBAAImL,SAAS,EAAC,MAAM;wBAAAT,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzC9K,OAAA,CAACf,MAAM;wBACLkG,OAAO,EAAC,iBAAiB;wBACzBiG,IAAI,EAAC,IAAI;wBACTkB,OAAO,EAAEA,CAAA,KAAMhI,iBAAiB,CAACN,UAAU,CAAE;wBAC7CuI,QAAQ,EAAEpK,YAAa;wBAAAuI,QAAA,gBAEvB1K,OAAA,CAACP,MAAM;0BAAC0L,SAAS,EAAC;wBAAM;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAC7B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EAELjF,KAAK,CAACxE,QAAQ,CAAC2E,GAAG,CAAC,CAACb,OAAO,EAAEV,YAAY,KAAK;sBAC7C,MAAMqI,cAAc,GAAGvM,iBAAiB,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKkE,OAAO,CAAC7D,cAAc,CAAC;sBAEnF,oBACEtB,OAAA,CAACjB,IAAI;wBAAoBoM,SAAS,EAAC,mBAAmB;wBAAAT,QAAA,eACpD1K,OAAA,CAACjB,IAAI,CAACwM,IAAI;0BAAAb,QAAA,gBACR1K,OAAA;4BAAKmL,SAAS,EAAC,wDAAwD;4BAAAT,QAAA,gBACrE1K,OAAA;8BAAKmL,SAAS,EAAC,2BAA2B;8BAAAT,QAAA,gBACxC1K,OAAA;gCAAImL,SAAS,EAAC,WAAW;gCAAAT,QAAA,GAAC,WAAS,EAACjG,YAAY,GAAG,CAAC;8BAAA;gCAAAkG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC,EACzDgC,cAAc,iBACb9M,OAAA;gCAAKmL,SAAS,EAAC,2BAA2B;gCAAAT,QAAA,gBACxC1K,OAAA;kCACE+K,KAAK,EAAE;oCACLvB,KAAK,EAAE,MAAM;oCACbuD,MAAM,EAAE,MAAM;oCACd1B,eAAe,EAAEyB,cAAc,CAACnG,KAAK;oCACrCqG,MAAM,EAAE,gBAAgB;oCACxB1B,YAAY,EAAE,KAAK;oCACnB2B,WAAW,EAAE;kCACf,CAAE;kCACFC,KAAK,EAAE,UAAUJ,cAAc,CAACnG,KAAK;gCAAG;kCAAAgE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACzC,CAAC,eACF9K,OAAA;kCAAOmL,SAAS,EAAC,YAAY;kCAAAT,QAAA,EAC1BoC,cAAc,CAACpG,UAAU,IAAIoG,cAAc,CAACnG;gCAAK;kCAAAgE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7C,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC,eACN9K,OAAA,CAACf,MAAM;8BACLkG,OAAO,EAAC,gBAAgB;8BACxBiG,IAAI,EAAC,IAAI;8BACTkB,OAAO,EAAEA,CAAA,KAAM9H,sBAAsB,CAACR,UAAU,EAAES,YAAY,CAAE;8BAChE8H,QAAQ,EAAE1G,KAAK,CAACxE,QAAQ,CAAC4C,MAAM,KAAK,CAAE;8BAAAyG,QAAA,gBAEtC1K,OAAA,CAACN,OAAO;gCAACyL,SAAS,EAAC;8BAAM;gCAAAR,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,WAC9B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eAEN9K,OAAA,CAACd,GAAG;4BAAAwL,QAAA,gBACF1K,OAAA,CAACb,GAAG;8BAACuM,EAAE,EAAE,CAAE;8BAAAhB,QAAA,eACT1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;gCAACR,SAAS,EAAC,MAAM;gCAAAT,QAAA,gBAC1B1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;kCAAAlB,QAAA,eAAC1K,OAAA;oCAAA0K,QAAA,EAAQ;kCAAsB;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAChE9K,OAAA,CAAClB,oBAAoB;kCACnBuC,QAAQ,EAAEmL,aAAc;kCACxBW,aAAa,EAAEhI,OAAO,CAAC7D,cAAe;kCACtC8L,QAAQ,EAAGtI,KAAK,IAAKF,mBAAmB,CAACZ,UAAU,EAAES,YAAY,EAAE,gBAAgB,EAAEK,KAAK,CAAE;kCAC5FmH,WAAW,EAAC,sBAAsB;kCAClCoB,mBAAmB,EAAEtI,wBAAyB;kCAC9Cf,UAAU,EAAEA,UAAW;kCACvBS,YAAY,EAAEA;gCAAa;kCAAAkG,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC5B,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACN9K,OAAA,CAACb,GAAG;8BAACuM,EAAE,EAAE,CAAE;8BAAAhB,QAAA,eACT1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;gCAACR,SAAS,EAAC,MAAM;gCAAAT,QAAA,gBAC1B1K,OAAA;kCAAKmL,SAAS,EAAC,wDAAwD;kCAAAT,QAAA,gBACrE1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;oCAACT,SAAS,EAAC,MAAM;oCAAAT,QAAA,eAAC1K,OAAA;sCAAA0K,QAAA,EAAQ;oCAAU;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAQ;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAY,CAAC,EACpEgC,cAAc,iBACb9M,OAAA;oCAAKmL,SAAS,EAAC,2BAA2B;oCAAAT,QAAA,gBACxC1K,OAAA;sCACE+K,KAAK,EAAE;wCACLvB,KAAK,EAAE,MAAM;wCACbuD,MAAM,EAAE,MAAM;wCACd1B,eAAe,EAAEyB,cAAc,CAACnG,KAAK;wCACrCqG,MAAM,EAAE,gBAAgB;wCACxB1B,YAAY,EAAE,KAAK;wCACnB2B,WAAW,EAAE;sCACf,CAAE;sCACFC,KAAK,EAAE,UAAUJ,cAAc,CAACpG,UAAU,IAAIoG,cAAc,CAACnG,KAAK;oCAAG;sCAAAgE,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACtE,CAAC,eACF9K,OAAA;sCAAMmL,SAAS,EAAE5E,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAIuL,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,CAAC,GAAG,mBAAmB,GAAG,oBAAqB;sCAAAiE,QAAA,GACzJoC,cAAc,CAACpG,UAAU,IAAIoG,cAAc,CAACnG,KAAK,EAAC,gBAAc,EAACmG,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,EAAC,QAC/H;oCAAA;sCAAAkE,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAM,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACJ,CACN;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACE,CAAC,eACN9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO;kCACXC,IAAI,EAAC,QAAQ;kCACbwB,IAAI,EAAC,MAAM;kCACXC,GAAG,EAAC,GAAG;kCACPzI,KAAK,EAAEK,OAAO,CAAC5D,UAAW;kCAC1BwK,QAAQ,EAAGzG,CAAC,IAAKV,mBAAmB,CAACZ,UAAU,EAAES,YAAY,EAAE,YAAY,EAAEa,CAAC,CAAC0G,MAAM,CAAClH,KAAK,CAAE;kCAC7FoH,QAAQ;kCACRD,WAAW,EAAC,kBAAkB;kCAC9BuB,SAAS,EAAEV,cAAc,IAAIvG,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAIuL,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,CAAE;kCAC3H0E,SAAS,EAAE2B,cAAc,IAAIvG,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAIuL,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,CAAC,GAAG,eAAe,GAAG,EAAG;kCAClJsE,KAAK,EAAE;oCAAEgC,MAAM,EAAE;kCAAO;gCAAE;kCAAApC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC3B,CAAC,eACF9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO,CAACM,QAAQ;kCAACL,IAAI,EAAC,SAAS;kCAAApB,QAAA,EAClCoC,cAAc,IAAIvG,UAAU,CAACpB,OAAO,CAAC5D,UAAU,CAAC,IAAIuL,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,CAAC,GAC5G,4BAA4BqG,cAAc,CAACtG,cAAc,IAAIsG,cAAc,CAACrG,UAAU,mBAAmB,GACzG;gCAAgC;kCAAAkE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACf,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eAEN9K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;4BAACT,SAAS,EAAC,MAAM;4BAAAT,QAAA,eAAC1K,OAAA;8BAAA0K,QAAA,EAAQ;4BAAe;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eAC1E9K,OAAA,CAACd,GAAG;4BAAAwL,QAAA,GACD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC1E,GAAG,CAAC,CAACoF,IAAI,EAAEqC,SAAS,KAAK;8BACpD,MAAMC,OAAO,GAAGtC,IAAI,CAACuC,WAAW,CAAC,CAAC;8BAClC,oBACE3N,OAAA,CAACb,GAAG;gCAAiBqC,EAAE,EAAE,CAAE;gCAACoM,EAAE,EAAE,CAAE;gCAAClC,EAAE,EAAE,CAAE;gCAACP,SAAS,EAAC,MAAM;gCAAAT,QAAA,eACxD1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;kCAAAjB,QAAA,gBACT1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;oCAACT,SAAS,EAAC,qBAAqB;oCAAAT,QAAA,EAAEU;kCAAI;oCAAAT,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAa,CAAC,eAC/D9K,OAAA,CAAChB,IAAI,CAAC6M,OAAO;oCACXC,IAAI,EAAC,QAAQ;oCACbyB,GAAG,EAAC,GAAG;oCACPzI,KAAK,EAAEK,OAAO,CAACuI,OAAO,CAAE;oCACxB3B,QAAQ,EAAGzG,CAAC,IAAK;sCACf,MAAMuI,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE5D,QAAQ,CAAC7E,CAAC,CAAC0G,MAAM,CAAClH,KAAK,IAAI,CAAC,CAAC,CAAC;sCACtDF,mBAAmB,CAACZ,UAAU,EAAES,YAAY,EAAEiJ,OAAO,EAAEG,GAAG,CAAC;oCAC7D,CAAE;oCACF1C,SAAS,EAAC;kCAAa;oCAAAR,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxB,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACQ;8BAAC,GAbL2C,SAAS;gCAAA9C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAcd,CAAC;4BAEV,CAAC,CAAC,eACF9K,OAAA,CAACb,GAAG;8BAACqC,EAAE,EAAE,CAAE;8BAACoM,EAAE,EAAE,CAAE;8BAAClC,EAAE,EAAE,CAAE;8BAACP,SAAS,EAAC,MAAM;8BAAAT,QAAA,eACxC1K,OAAA,CAAChB,IAAI,CAAC2M,KAAK;gCAAAjB,QAAA,gBACT1K,OAAA,CAAChB,IAAI,CAAC4M,KAAK;kCAACT,SAAS,EAAC,qBAAqB;kCAAAT,QAAA,EAAC;gCAAK;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAC9D9K,OAAA;kCAAKmL,SAAS,EAAC,mCAAmC;kCAAAT,QAAA,EAC/CP,QAAQ,CAAChF,OAAO,CAAC3D,EAAE,IAAI,CAAC,CAAC,GACzB2I,QAAQ,CAAChF,OAAO,CAAC1D,CAAC,IAAI,CAAC,CAAC,GACxB0I,QAAQ,CAAChF,OAAO,CAACzD,CAAC,IAAI,CAAC,CAAC,GACxByI,QAAQ,CAAChF,OAAO,CAACxD,CAAC,IAAI,CAAC,CAAC,GACxBwI,QAAQ,CAAChF,OAAO,CAACvD,EAAE,IAAI,CAAC;gCAAC;kCAAA+I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACvB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACI;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG;sBAAC,GAhIHrG,YAAY;wBAAAkG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiIjB,CAAC;oBAEX,CAAC,CAAC;kBAAA,eACF,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GAlMHjF,KAAK,CAAC5E,EAAE;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmMb,CAAC;YAEX,CAAC,CAAC,eAEF9K,OAAA;cAAKmL,SAAS,EAAC,iCAAiC;cAAAT,QAAA,eAC9C1K,OAAA,CAACjB,IAAI;gBAACoM,SAAS,EAAC,UAAU;gBAACJ,KAAK,EAAE;kBAAEM,eAAe,EAAE;gBAAU,CAAE;gBAAAX,QAAA,eAC/D1K,OAAA,CAACjB,IAAI,CAACwM,IAAI;kBAACJ,SAAS,EAAC,MAAM;kBAAAT,QAAA,eACzB1K,OAAA;oBAAKmL,SAAS,EAAC,oBAAoB;oBAAAT,QAAA,gBACjC1K,OAAA;sBAAKmL,SAAS,EAAC,gCAAgC;sBAAAT,QAAA,gBAC7C1K,OAAA;wBAAQmL,SAAS,EAAC,MAAM;wBAAAT,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnD9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,MAAI,EAAC3C,eAAe,CAACvG,EAAE;sBAAA;wBAAAmJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrE9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,KAAG,EAAC3C,eAAe,CAACtG,CAAC;sBAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,KAAG,EAAC3C,eAAe,CAACrG,CAAC;sBAAA;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,KAAG,EAAC3C,eAAe,CAACpG,CAAC;sBAAA;wBAAAgJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,MAAI,EAAC3C,eAAe,CAACnG,EAAE;sBAAA;wBAAA+I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrE9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,SAAS;wBAAC7C,SAAS,EAAC,MAAM;wBAAAT,QAAA,GAAC,SAAO,EAAC3C,eAAe,CAACmC,KAAK;sBAAA;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACN9K,OAAA;sBAAKmL,SAAS,EAAC,2BAA2B;sBAAAT,QAAA,gBACxC1K,OAAA;wBAAQmL,SAAS,EAAC,MAAM;wBAAAT,QAAA,EAAC;sBAAiB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnD9K,OAAA,CAACV,KAAK;wBAAC0O,EAAE,EAAC,MAAM;wBAAAtD,QAAA,GAAE3C,eAAe,CAACxG,UAAU,CAAC6I,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;sBAAA;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN9K,OAAA;cAAKmL,SAAS,EAAC,oCAAoC;cAAAT,QAAA,eACjD1K,OAAA,CAACf,MAAM;gBACL6M,IAAI,EAAC,QAAQ;gBACb3G,OAAO,EAAC,SAAS;gBACjBiG,IAAI,EAAC,IAAI;gBACTmB,QAAQ,EAAEpK,YAAa;gBACvBgJ,SAAS,EAAC,MAAM;gBAAAT,QAAA,EAEfvI,YAAY,gBACXnC,OAAA,CAAAE,SAAA;kBAAAwK,QAAA,gBACE1K,OAAA,CAACZ,OAAO;oBAACgN,EAAE,EAAC,MAAM;oBAACM,SAAS,EAAC,QAAQ;oBAACtB,IAAI,EAAC,IAAI;oBAAC6C,IAAI,EAAC,QAAQ;oBAAC,eAAY,MAAM;oBAAC9C,SAAS,EAAC;kBAAM;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAEtG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN9K,OAAA,CAACT,KAAK;MAAC2O,IAAI,EAAEvL,YAAa;MAACwL,MAAM,EAAE5D,gBAAiB;MAACa,IAAI,EAAC,IAAI;MAACgD,QAAQ;MAAA1D,QAAA,gBACrE1K,OAAA,CAACT,KAAK,CAACkN,MAAM;QAAC4B,WAAW;QAAA3D,QAAA,eACvB1K,OAAA,CAACT,KAAK,CAAC+O,KAAK;UAAA5D,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACf9K,OAAA,CAACT,KAAK,CAACgM,IAAI;QAAAb,QAAA,gBACT1K,OAAA;UAAA0K,QAAA,EAAG;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAE/DjI,eAAe,iBACd7C,OAAA;UAAKmL,SAAS,EAAC,MAAM;UAAAT,QAAA,gBACnB1K,OAAA;YAAA0K,QAAA,EAAG;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtD9K,OAAA;YAAA0K,QAAA,gBACE1K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjI,eAAe,CAACmE,YAAY;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtE9K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjI,eAAe,CAAC4E,WAAW;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/D9K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAI5J,IAAI,CAAC2B,eAAe,CAACkE,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrG9K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,SAAK,EAACjI,eAAe,CAACkF,eAAe,CAACvG,EAAE,EAAC,OAC1E,EAACqB,eAAe,CAACkF,eAAe,CAACtG,CAAC,EAAC,OACnC,EAACoB,eAAe,CAACkF,eAAe,CAACrG,CAAC,EAAC,OACnC,EAACmB,eAAe,CAACkF,eAAe,CAACpG,CAAC,EAAC,QAClC,EAACkB,eAAe,CAACkF,eAAe,CAACnG,EAAE;YAAA;cAAA+I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/C9K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjI,eAAe,CAACkF,eAAe,CAACmC,KAAK;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9E9K,OAAA;cAAA0K,QAAA,gBAAI1K,OAAA;gBAAA0K,QAAA,EAAQ;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjI,eAAe,CAACkF,eAAe,CAACxG,UAAU,CAAC6I,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb9K,OAAA,CAACT,KAAK,CAACgP,MAAM;QAAA7D,QAAA,gBACX1K,OAAA,CAACf,MAAM;UAACkG,OAAO,EAAC,WAAW;UAACmH,OAAO,EAAE/B,gBAAiB;UAAAG,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAACf,MAAM;UAACkG,OAAO,EAAC,SAAS;UAACmH,OAAO,EAAErE,WAAY;UAAAyC,QAAA,gBAC7C1K,OAAA,CAACH,SAAS;YAACsL,SAAS,EAAC;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAChC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC1K,EAAA,CA3zBID,gBAAgB;AAAAqO,EAAA,GAAhBrO,gBAAgB;AA6zBtB,eAAeA,gBAAgB;AAAC,IAAAqO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}