{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\ApproveFinishedProduct.js\",\n  _s = $RefreshSig$();\n// src/pages/ApproveFinishedProduct.jsx\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport axios from 'axios';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Form, Button, Alert, Row, Col, Spinner, Image, Modal, ProgressBar, Badge, Tabs, Tab, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';\nimport { FaCheck, FaUpload, FaImage, FaTags, FaInfoCircle, FaMoneyBillWave, FaArrowRight, FaPercentage, FaBoxOpen, FaClipboardList, FaTrash, FaUndo, FaExclamationTriangle, FaFilePdf, FaDownload } from 'react-icons/fa';\nimport { useDropzone } from 'react-dropzone';\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\nimport './ApproveFinishedProduct.css';\nimport { jsPDF } from 'jspdf';\n// No need to import uploadMultipleImages as we're using FormData directly\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ApproveFinishedProduct = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const fileInputRef = useRef(null);\n\n  // Basic form state\n  const [manufacturePrice, setManufacturePrice] = useState('');\n  const [sellingPrice, setSellingPrice] = useState('');\n  const [productNotes, setProductNotes] = useState('');\n  const [error, setError] = useState('');\n  const [successMsg, setSuccessMsg] = useState('');\n  const [isApproved, setIsApproved] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Image handling state\n  const [productImages, setProductImages] = useState([]);\n  const [imagePreviewUrls, setImagePreviewUrls] = useState([]);\n  const [existingImageUrls, setExistingImageUrls] = useState([]);\n  const [isDragging, setIsDragging] = useState(false);\n  const [activeImageIndex, setActiveImageIndex] = useState(0);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Product details state\n  const [productDetails, setProductDetails] = useState(null);\n  const [fabricDetails, setFabricDetails] = useState([]);\n  const [sizeQuantities, setSizeQuantities] = useState({\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0\n  });\n\n  // UI state\n  const [activeTab, setActiveTab] = useState('details');\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [pdfLoading, setPdfLoading] = useState(false);\n  const [validationErrors, setValidationErrors] = useState({});\n  const [profitMargin, setProfitMargin] = useState(0);\n\n  // Product name editing state\n  const [productName, setProductName] = useState('');\n  const [isEditingName, setIsEditingName] = useState(false);\n  const [productNameError, setProductNameError] = useState('');\n\n  // Price editing state for approved products\n  const [isEditingPrices, setIsEditingPrices] = useState(false);\n  const [editManufacturePrice, setEditManufacturePrice] = useState('');\n  const [editSellingPrice, setEditSellingPrice] = useState('');\n  const [priceEditErrors, setPriceEditErrors] = useState({});\n  const [finishedProductId, setFinishedProductId] = useState(null);\n\n  // Calculate profit margin whenever prices change\n  useEffect(() => {\n    if (manufacturePrice && sellingPrice) {\n      const mPrice = parseFloat(manufacturePrice);\n      const sPrice = parseFloat(sellingPrice);\n      if (mPrice > 0 && sPrice > 0) {\n        const margin = (sPrice - mPrice) / sPrice * 100;\n        setProfitMargin(margin.toFixed(2));\n      }\n    }\n  }, [manufacturePrice, sellingPrice]);\n\n  // State for retry mechanism\n  const [retryCount, setRetryCount] = useState(0);\n  const maxRetries = 3;\n\n  // Function to fetch product data with retry mechanism\n  const fetchProductData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Fetch approval status\n      const approvalRes = await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);\n\n      // Fetch cutting record details\n      const cuttingRes = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n      if (approvalRes.data && approvalRes.data.is_approved) {\n        setIsApproved(true);\n        setManufacturePrice(approvalRes.data.manufacture_price);\n        setSellingPrice(approvalRes.data.selling_price);\n        setFinishedProductId(approvalRes.data.finished_product_id);\n\n        // Set existing image URLs if available\n        if (approvalRes.data.product_images && Array.isArray(approvalRes.data.product_images)) {\n          setExistingImageUrls(approvalRes.data.product_images);\n        } else if (approvalRes.data.product_image) {\n          // For backward compatibility with single image\n          setExistingImageUrls([approvalRes.data.product_image]);\n        }\n        if (approvalRes.data.notes) {\n          setProductNotes(approvalRes.data.notes);\n        }\n      }\n      if (cuttingRes.data) {\n        setProductDetails(cuttingRes.data);\n\n        // Set product name from API response\n        if (cuttingRes.data.product_name) {\n          setProductName(cuttingRes.data.product_name);\n        } else if (cuttingRes.data.fabric_definition_data) {\n          // If no product name, use fabric name as default\n          setProductName(cuttingRes.data.fabric_definition_data.fabric_name);\n        }\n\n        // Extract fabric details\n        if (cuttingRes.data.details && cuttingRes.data.details.length > 0) {\n          // Debug: Log the fabric details to see what color data we're getting\n          console.log('Fabric details from API:', cuttingRes.data.details);\n          setFabricDetails(cuttingRes.data.details);\n\n          // Calculate size quantities\n          const sizes = {\n            xs: 0,\n            s: 0,\n            m: 0,\n            l: 0,\n            xl: 0\n          };\n          cuttingRes.data.details.forEach(detail => {\n            sizes.xs += detail.xs || 0;\n            sizes.s += detail.s || 0;\n            sizes.m += detail.m || 0;\n            sizes.l += detail.l || 0;\n            sizes.xl += detail.xl || 0;\n          });\n          setSizeQuantities(sizes);\n        }\n      }\n\n      // Reset retry count on success\n      setRetryCount(0);\n    } catch (err) {\n      console.error(\"Failed to fetch product data:\", err);\n\n      // Provide more detailed error message\n      const errorMessage = err.response ? `Error: ${err.response.status} - ${err.response.statusText}` : err.request ? \"No response received from server. Check if the backend is running.\" : \"Failed to make request. Check your network connection.\";\n      setError(`Unable to fetch product data. ${errorMessage}`);\n\n      // Implement retry mechanism\n      if (retryCount < maxRetries) {\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => {\n          fetchProductData();\n        }, 2000); // Wait 2 seconds before retrying\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [id, retryCount]);\n\n  // Fetch product details and approval status\n  useEffect(() => {\n    fetchProductData();\n  }, [fetchProductData]);\n\n  // Handle image selection from file input\n  const handleImageChange = e => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      processImageFiles(files);\n    }\n  };\n\n  // Process the selected image files\n  const processImageFiles = useCallback(files => {\n    // Check if adding these files would exceed the limit\n    if (productImages.length + files.length > 10) {\n      setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);\n      return;\n    }\n    const newImages = [];\n    const newPreviewUrls = [...imagePreviewUrls];\n    files.forEach(file => {\n      // Validate file type\n      if (!file.type.match('image.*')) {\n        setError('Please select image files only (JPEG, PNG, etc.)');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('Each image size should be less than 5MB');\n        return;\n      }\n      newImages.push(file);\n\n      // Create a preview URL\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        newPreviewUrls.push(reader.result);\n        setImagePreviewUrls([...newPreviewUrls]);\n      };\n      reader.readAsDataURL(file);\n    });\n    setProductImages([...productImages, ...newImages]);\n  }, [productImages, imagePreviewUrls]);\n\n  // Trigger file input click\n  const triggerFileInput = () => {\n    fileInputRef.current.click();\n  };\n\n  // Handle drag and drop functionality\n  const onDrop = useCallback(acceptedFiles => {\n    if (acceptedFiles && acceptedFiles.length > 0) {\n      processImageFiles(acceptedFiles);\n    }\n  }, [processImageFiles]);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif']\n    },\n    maxFiles: 10\n  });\n\n  // Remove an uploaded image\n  const removeImage = index => {\n    const newImages = [...productImages];\n    const newPreviewUrls = [...imagePreviewUrls];\n    newImages.splice(index, 1);\n    newPreviewUrls.splice(index, 1);\n    setProductImages(newImages);\n    setImagePreviewUrls(newPreviewUrls);\n\n    // Adjust active index if needed\n    if (index === activeImageIndex) {\n      setActiveImageIndex(Math.max(0, index - 1));\n    } else if (index < activeImageIndex) {\n      setActiveImageIndex(activeImageIndex - 1);\n    }\n  };\n\n  // Set active image\n  const setActiveImage = index => {\n    setActiveImageIndex(index);\n  };\n\n  // Validate form inputs\n  const validateForm = () => {\n    const errors = {};\n\n    // Validate manufacture price\n    if (!manufacturePrice || manufacturePrice.trim() === '') {\n      errors.manufacturePrice = \"Manufacture price is required\";\n    } else if (parseFloat(manufacturePrice) <= 0) {\n      errors.manufacturePrice = \"Manufacture price must be greater than zero\";\n    } else if (isNaN(parseFloat(manufacturePrice))) {\n      errors.manufacturePrice = \"Manufacture price must be a valid number\";\n    }\n\n    // Validate selling price\n    if (!sellingPrice || sellingPrice.trim() === '') {\n      errors.sellingPrice = \"Selling price is required\";\n    } else if (parseFloat(sellingPrice) <= 0) {\n      errors.sellingPrice = \"Selling price must be greater than zero\";\n    } else if (isNaN(parseFloat(sellingPrice))) {\n      errors.sellingPrice = \"Selling price must be a valid number\";\n    } else if (parseFloat(sellingPrice) < parseFloat(manufacturePrice)) {\n      errors.sellingPrice = \"Selling price should be greater than or equal to manufacture price\";\n    }\n\n    // Validate product notes (optional)\n    if (productNotes && productNotes.length > 500) {\n      errors.productNotes = \"Notes should be less than 500 characters\";\n    }\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Show confirmation modal\n  const handleFormSubmit = e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Show confirmation modal\n    setShowConfirmModal(true);\n  };\n\n  // Handle actual submission\n  const handleSubmit = async () => {\n    setError('');\n    setSuccessMsg('');\n    setShowConfirmModal(false);\n    try {\n      // Show loading state\n      setLoading(true);\n      setIsUploading(true);\n\n      // Create FormData object for API request\n      const formData = new FormData();\n      formData.append('cutting_record', id);\n      formData.append('manufacture_price', parseFloat(manufacturePrice));\n      formData.append('selling_price', parseFloat(sellingPrice));\n      if (productNotes) {\n        formData.append('notes', productNotes);\n      }\n\n      // Add images directly to the FormData if there are any\n      if (productImages && productImages.length > 0) {\n        setUploadProgress(0);\n\n        // Append each image to the FormData\n        productImages.forEach(image => {\n          formData.append('product_images', image);\n        });\n      }\n\n      // Make the API request with progress tracking\n      const response = await axios.post('http://localhost:8000/api/finished_product/approve/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          setUploadProgress(percentCompleted);\n        }\n      });\n      setSuccessMsg(response.data.message || 'Product approved successfully!');\n      setIsUploading(false);\n\n      // Redirect after a delay\n      setTimeout(() => {\n        navigate('/approveproduct-list');\n      }, 2000);\n    } catch (err) {\n      console.error(\"Error approving finished product:\", err);\n      const errMsg = err.response && err.response.data ? typeof err.response.data === 'object' ? JSON.stringify(err.response.data) : err.response.data : \"Failed to approve finished product. Please try again.\";\n      setError(errMsg);\n      setIsUploading(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Cancel confirmation\n  const handleCancelConfirmation = () => {\n    setShowConfirmModal(false);\n  };\n\n  // Open PDF modal\n  const openPdfModal = () => {\n    setShowPdfModal(true);\n  };\n\n  // Close PDF modal\n  const closePdfModal = () => {\n    setShowPdfModal(false);\n  };\n\n  // Start editing product name\n  const startEditingName = () => {\n    setIsEditingName(true);\n  };\n\n  // Cancel editing product name\n  const cancelEditingName = () => {\n    setIsEditingName(false);\n    setProductNameError('');\n    // Reset to original name from product details\n    if (productDetails && productDetails.product_name) {\n      setProductName(productDetails.product_name);\n    } else if (productDetails && productDetails.fabric_definition_data) {\n      setProductName(productDetails.fabric_definition_data.fabric_name);\n    }\n  };\n\n  // Save updated product name\n  const saveProductName = async () => {\n    // Validate product name\n    if (!productName.trim()) {\n      setProductNameError('Product name cannot be empty');\n      return;\n    }\n    setProductNameError('');\n    setLoading(true);\n    try {\n      // First, get the current cutting record data\n      const currentRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n\n      // Create a payload with all the required fields, including details\n      const payload = {\n        fabric_definition: currentRecord.data.fabric_definition,\n        cutting_date: currentRecord.data.cutting_date,\n        product_name: productName,\n        details: currentRecord.data.details // Include existing details without modification\n      };\n\n      // Update the product name in the backend\n      await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\n\n      // Update local state\n      setProductDetails({\n        ...productDetails,\n        product_name: productName\n      });\n      setIsEditingName(false);\n      setSuccessMsg('Product name updated successfully');\n\n      // Clear success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMsg('');\n      }, 3000);\n    } catch (err) {\n      console.error('Error updating product name:', err);\n\n      // Check if the product name was actually updated despite the error\n      try {\n        const updatedRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\n        if (updatedRecord.data.product_name === productName) {\n          // If the name was updated successfully despite the error, show success message\n          setProductDetails({\n            ...productDetails,\n            product_name: productName\n          });\n          setIsEditingName(false);\n          setSuccessMsg('Product name updated successfully');\n\n          // Clear success message after 3 seconds\n          setTimeout(() => {\n            setSuccessMsg('');\n          }, 3000);\n          return;\n        }\n      } catch (checkErr) {\n        // If we can't check, just show the original error\n        console.error('Error checking product name update:', checkErr);\n      }\n\n      // Show error message if the name wasn't updated\n      setError('Failed to update product name. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Start editing prices\n  const startEditingPrices = () => {\n    setEditManufacturePrice(manufacturePrice);\n    setEditSellingPrice(sellingPrice);\n    setIsEditingPrices(true);\n    setPriceEditErrors({});\n  };\n\n  // Cancel editing prices\n  const cancelEditingPrices = () => {\n    setIsEditingPrices(false);\n    setEditManufacturePrice('');\n    setEditSellingPrice('');\n    setPriceEditErrors({});\n  };\n\n  // Validate price editing form\n  const validatePriceEdit = () => {\n    const errors = {};\n\n    // Validate manufacture price\n    const manufactureStr = String(editManufacturePrice || '').trim();\n    if (!editManufacturePrice || manufactureStr === '') {\n      errors.editManufacturePrice = \"Manufacture price is required\";\n    } else if (parseFloat(editManufacturePrice) <= 0) {\n      errors.editManufacturePrice = \"Manufacture price must be greater than zero\";\n    } else if (isNaN(parseFloat(editManufacturePrice))) {\n      errors.editManufacturePrice = \"Manufacture price must be a valid number\";\n    }\n\n    // Validate selling price\n    const sellingStr = String(editSellingPrice || '').trim();\n    if (!editSellingPrice || sellingStr === '') {\n      errors.editSellingPrice = \"Selling price is required\";\n    } else if (parseFloat(editSellingPrice) <= 0) {\n      errors.editSellingPrice = \"Selling price must be greater than zero\";\n    } else if (isNaN(parseFloat(editSellingPrice))) {\n      errors.editSellingPrice = \"Selling price must be a valid number\";\n    } else if (parseFloat(editSellingPrice) < parseFloat(editManufacturePrice)) {\n      errors.editSellingPrice = \"Selling price should be greater than or equal to manufacture price\";\n    }\n    setPriceEditErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Save updated prices\n  const savePriceChanges = async () => {\n    console.log('=== savePriceChanges called ===');\n    console.log('editManufacturePrice:', editManufacturePrice);\n    console.log('editSellingPrice:', editSellingPrice);\n    console.log('finishedProductId:', finishedProductId);\n    console.log('loading state:', loading);\n    const validationResult = validatePriceEdit();\n    console.log('Validation result:', validationResult);\n    if (!validationResult) {\n      console.log('Validation failed - stopping execution');\n      return;\n    }\n    if (!finishedProductId) {\n      console.log('No finished product ID - stopping execution');\n      setError('Could not find finished product record');\n      return;\n    }\n    console.log('Validation passed, proceeding with API call...');\n    setLoading(true);\n    setError('');\n    try {\n      // Update the prices using the PATCH endpoint\n      const updateData = {\n        manufacture_price: parseFloat(editManufacturePrice),\n        selling_price: parseFloat(editSellingPrice)\n      };\n      console.log('Sending update data:', updateData);\n      await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`, updateData);\n\n      // Update local state\n      setManufacturePrice(editManufacturePrice);\n      setSellingPrice(editSellingPrice);\n      setIsEditingPrices(false);\n      setSuccessMsg('Prices updated successfully');\n\n      // Clear success message after 3 seconds\n      setTimeout(() => {\n        setSuccessMsg('');\n      }, 3000);\n    } catch (err) {\n      console.error('Error updating prices:', err);\n      const errMsg = err.response && err.response.data ? typeof err.response.data === 'object' ? JSON.stringify(err.response.data) : err.response.data : \"Failed to update prices. Please try again.\";\n      setError(errMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Generate PDF report for the product\n  const generateProductReport = () => {\n    setPdfLoading(true);\n    try {\n      // Create PDF document\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes\n      const titleFontSize = 16;\n      const headingFontSize = 12;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add header\n      doc.setFontSize(titleFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Product Report', 105, 20, {\n        align: 'center'\n      });\n\n      // Add product name\n      const pdfProductName = productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`);\n      doc.setFontSize(headingFontSize);\n      doc.text(pdfProductName, 105, 30, {\n        align: 'center'\n      });\n\n      // Add approval date\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`Approval Date: ${new Date().toLocaleDateString()}`, 105, 40, {\n        align: 'center'\n      });\n\n      // Add horizontal line\n      doc.setDrawColor(200, 200, 200);\n      doc.line(20, 45, 190, 45);\n\n      // Start Y position for content\n      let yPos = 55;\n\n      // Add pricing information\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Pricing Information', 20, yPos);\n      yPos += 10;\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`Manufacture Price: LKR ${manufacturePrice}`, 25, yPos);\n      yPos += 7;\n      doc.text(`Selling Price: LKR ${sellingPrice}`, 25, yPos);\n      yPos += 7;\n      doc.text(`Profit Margin: ${profitMargin}%`, 25, yPos);\n      yPos += 15;\n\n      // Add size distribution\n      doc.setFontSize(headingFontSize);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Size Distribution', 20, yPos);\n      yPos += 10;\n      doc.setFontSize(normalFontSize);\n      doc.setFont('helvetica', 'normal');\n\n      // Create a table for size distribution\n      const sizes = Object.entries(sizeQuantities);\n      const sizeHeaders = ['Size', 'Quantity', 'Percentage'];\n      const totalQuantity = sizes.reduce((sum, [_, qty]) => sum + qty, 0);\n\n      // Draw table headers\n      doc.setFont('helvetica', 'bold');\n      doc.text(sizeHeaders[0], 25, yPos);\n      doc.text(sizeHeaders[1], 60, yPos);\n      doc.text(sizeHeaders[2], 95, yPos);\n      yPos += 7;\n\n      // Draw table rows\n      doc.setFont('helvetica', 'normal');\n      sizes.forEach(([size, quantity]) => {\n        const percentage = totalQuantity > 0 ? (quantity / totalQuantity * 100).toFixed(1) : '0.0';\n        doc.text(size.toUpperCase(), 25, yPos);\n        doc.text(quantity.toString(), 60, yPos);\n        doc.text(`${percentage}%`, 95, yPos);\n        yPos += 7;\n      });\n\n      // Add total row\n      doc.setFont('helvetica', 'bold');\n      doc.text('Total', 25, yPos);\n      doc.text(totalQuantity.toString(), 60, yPos);\n      doc.text('100.0%', 95, yPos);\n      yPos += 15;\n\n      // Add color information\n      if (fabricDetails.length > 0) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Color Information', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n        fabricDetails.forEach((detail, index) => {\n          var _detail$fabric_varian;\n          const colorName = ((_detail$fabric_varian = detail.fabric_variant_data) === null || _detail$fabric_varian === void 0 ? void 0 : _detail$fabric_varian.color_name) || detail.color || 'N/A';\n          doc.text(`Color ${index + 1}: ${colorName}`, 25, yPos);\n          yPos += 7;\n        });\n        yPos += 8;\n      }\n\n      // Add product notes if available\n      if (productNotes) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Product Notes', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n\n        // Split notes into multiple lines if needed\n        const splitNotes = doc.splitTextToSize(productNotes, 160);\n        doc.text(splitNotes, 25, yPos);\n        yPos += splitNotes.length * 7 + 8;\n      }\n\n      // Add image information\n      if (existingImageUrls && existingImageUrls.length > 0) {\n        doc.setFontSize(headingFontSize);\n        doc.setFont('helvetica', 'bold');\n        doc.text('Product Images', 20, yPos);\n        yPos += 10;\n        doc.setFontSize(normalFontSize);\n        doc.setFont('helvetica', 'normal');\n        doc.text(`Number of Images: ${existingImageUrls.length}`, 25, yPos);\n        yPos += 7;\n        doc.text('Note: Images can be viewed in the system', 25, yPos);\n        yPos += 15;\n      }\n\n      // Add footer\n      doc.setFontSize(smallFontSize);\n      doc.setFont('helvetica', 'italic');\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      doc.text('Pri Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\n      doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0, 10)}.pdf`);\n      setPdfLoading(false);\n      setShowPdfModal(false);\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      setError(`Failed to generate PDF: ${error.message}`);\n      setPdfLoading(false);\n      setShowPdfModal(false);\n    }\n  };\n\n  // Loading spinner\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex justify-content-center align-items-center\",\n    style: {\n      height: \"100vh\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Spinner, {\n      animation: \"border\",\n      role: \"status\",\n      variant: \"primary\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"visually-hidden\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 758,\n    columnNumber: 5\n  }, this);\n\n  // Render color swatch\n  const renderColorSwatch = color => {\n    if (!color) return null;\n\n    // For debugging - log the color value we're receiving\n    console.log('Color value received:', color);\n\n    // SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\n    let bgColor;\n\n    // If it starts with #, use it directly\n    if (color.startsWith('#')) {\n      bgColor = color;\n    }\n    // If it looks like a hex code without #, add the #\n    else if (/^[0-9A-Fa-f]{6}$/.test(color) || /^[0-9A-Fa-f]{3}$/.test(color)) {\n      bgColor = `#${color}`;\n    }\n    // For named colors like \"Black\", \"Red\", etc.\n    else {\n      // Common color names mapping\n      const colorMap = {\n        'red': '#dc3545',\n        'blue': '#0d6efd',\n        'green': '#198754',\n        'yellow': '#ffc107',\n        'black': '#212529',\n        'white': '#f8f9fa',\n        'purple': '#6f42c1',\n        'orange': '#fd7e14',\n        'pink': '#d63384',\n        'brown': '#8B4513',\n        'gray': '#6c757d'\n      };\n\n      // Try to get from color map or use the name directly\n      bgColor = colorMap[color.toLowerCase()] || color;\n    }\n    return /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n      placement: \"top\",\n      overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n        className: \"custom-tooltip\",\n        children: color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 18\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"color-swatch\",\n        style: {\n          backgroundColor: bgColor\n        },\n        \"data-color\": color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 809,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render size quantity bars\n  const renderSizeQuantityBars = () => {\n    const sizes = Object.entries(sizeQuantities);\n    const maxQuantity = Math.max(...sizes.map(([_, qty]) => qty));\n    return sizes.map(([size, quantity]) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: size.toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [quantity, \" pcs\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n        now: maxQuantity ? quantity / maxQuantity * 100 : 0,\n        variant: quantity > 0 ? \"info\" : \"light\",\n        className: \"size-quantity-bar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this)]\n    }, size, true, {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 7\n    }, this));\n  };\n\n  // Confirmation Modal\n  const ConfirmationModal = () => /*#__PURE__*/_jsxDEV(Modal, {\n    show: showConfirmModal,\n    onHide: handleCancelConfirmation,\n    centered: true,\n    className: \"confirmation-modal\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: \"Confirm Product Approval\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 847,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 846,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to approve this product with the following details?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        bordered: true,\n        hover: true,\n        size: \"sm\",\n        className: \"mt-3\",\n        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Manufacture Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [\"LKR \", manufacturePrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Selling Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [\"LKR \", sellingPrice]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Profit Margin:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [profitMargin, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), productNotes && /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Notes:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: productNotes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Product Image:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Image, {\n          src: imagePreviewUrls[0],\n          alt: \"Product\",\n          thumbnail: true,\n          style: {\n            maxHeight: \"100px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: handleCancelConfirmation,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 892,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"success\",\n        onClick: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this), \"Confirm Approval\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 840,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 10,\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"shadow product-card slide-in\",\n            style: {\n              backgroundColor: \"#D9EDFB\",\n              borderRadius: \"10px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-center mb-3\",\n                children: \"Approve Finished Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mb-4\",\n                children: isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-0 me-2 flex-grow-1\",\n                    style: {\n                      maxWidth: '300px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      value: productName,\n                      onChange: e => setProductName(e.target.value),\n                      isInvalid: !!productNameError,\n                      placeholder: \"Enter product name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: productNameError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"success\",\n                    size: \"sm\",\n                    className: \"me-1\",\n                    onClick: saveProductName,\n                    children: /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"secondary\",\n                    size: \"sm\",\n                    onClick: cancelEditingName,\n                    children: /*#__PURE__*/_jsxDEV(FaUndo, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 916,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-center align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-center text-muted mb-0 me-2\",\n                    children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    onClick: startEditingName,\n                    children: \"Edit Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 17\n              }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-4 fade-in\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 25\n                    }, this), error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: fetchProductData,\n                    children: \"Retry\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 19\n              }, this), successMsg && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"success\",\n                className: \"mb-4 fade-in\",\n                children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 21\n                }, this), successMsg]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 19\n              }, this), isApproved ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 bg-white rounded mb-3 slide-in\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-center mb-4 text-success\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this), \"Product Already Approved\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-center align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                      className: \"mb-0 me-2 flex-grow-1\",\n                      style: {\n                        maxWidth: '300px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        value: productName,\n                        onChange: e => setProductName(e.target.value),\n                        isInvalid: !!productNameError,\n                        placeholder: \"Enter product name\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 999,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                        type: \"invalid\",\n                        children: productNameError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1006,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: saveProductName,\n                      children: /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1016,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"secondary\",\n                      size: \"sm\",\n                      onClick: cancelEditingName,\n                      children: /*#__PURE__*/_jsxDEV(FaUndo, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1023,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-center align-items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"mb-0 me-2\",\n                      children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: startEditingName,\n                      children: \"Edit Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1033,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4 bg-white rounded mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Row, {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-0\",\n                          children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1048,\n                            columnNumber: 52\n                          }, this), \"Pricing Information\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1048,\n                          columnNumber: 31\n                        }, this), !isEditingPrices && /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          size: \"sm\",\n                          onClick: startEditingPrices,\n                          children: \"Edit Prices\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1050,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1047,\n                        columnNumber: 29\n                      }, this), isEditingPrices ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-3 bg-light rounded\",\n                        children: /*#__PURE__*/_jsxDEV(Form, {\n                          onSubmit: e => e.preventDefault(),\n                          children: [/*#__PURE__*/_jsxDEV(Row, {\n                            children: [/*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    children: \"Manufacture Price (LKR):\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1066,\n                                    columnNumber: 53\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1066,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                  type: \"number\",\n                                  step: \"0.01\",\n                                  min: \"0\",\n                                  value: editManufacturePrice,\n                                  onChange: e => setEditManufacturePrice(e.target.value),\n                                  isInvalid: !!priceEditErrors.editManufacturePrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1067,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                                  type: \"invalid\",\n                                  children: priceEditErrors.editManufacturePrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1075,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1065,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1064,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Col, {\n                              md: 6,\n                              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                                className: \"mb-3\",\n                                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                    children: \"Selling Price (LKR):\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1082,\n                                    columnNumber: 53\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1082,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                                  type: \"number\",\n                                  step: \"0.01\",\n                                  min: \"0\",\n                                  value: editSellingPrice,\n                                  onChange: e => setEditSellingPrice(e.target.value),\n                                  isInvalid: !!priceEditErrors.editSellingPrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1083,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                                  type: \"invalid\",\n                                  children: priceEditErrors.editSellingPrice\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1091,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1081,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1080,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1063,\n                            columnNumber: 35\n                          }, this), editManufacturePrice && editSellingPrice && parseFloat(editManufacturePrice) > 0 && parseFloat(editSellingPrice) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-3 p-2 bg-info bg-opacity-10 rounded\",\n                            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"New Profit Margin: \"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1100,\n                              columnNumber: 39\n                            }, this), ((parseFloat(editSellingPrice) - parseFloat(editManufacturePrice)) / parseFloat(editSellingPrice) * 100).toFixed(2), \"%\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1099,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex gap-2\",\n                            children: [/*#__PURE__*/_jsxDEV(Button, {\n                              type: \"button\",\n                              variant: \"success\",\n                              size: \"sm\",\n                              onClick: savePriceChanges,\n                              disabled: loading,\n                              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1113,\n                                columnNumber: 39\n                              }, this), \"Save Changes\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1106,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              type: \"button\",\n                              variant: \"secondary\",\n                              size: \"sm\",\n                              onClick: cancelEditingPrices,\n                              children: [/*#__PURE__*/_jsxDEV(FaUndo, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1122,\n                                columnNumber: 39\n                              }, this), \"Cancel\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1116,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1105,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1062,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1061,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(Table, {\n                        bordered: true,\n                        hover: true,\n                        children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Manufacture Price:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1132,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1132,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [\"LKR \", manufacturePrice]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1133,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1131,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Selling Price:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1136,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1136,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [\"LKR \", sellingPrice]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1137,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1135,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Profit Margin:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1140,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1140,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: [profitMargin, \"%\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1141,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1139,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1130,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 31\n                      }, this), productNotes && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1149,\n                            columnNumber: 54\n                          }, this), \"Notes\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1149,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-3 bg-light rounded\",\n                          children: productNotes\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1150,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1148,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3 mt-4\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTags, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1156,\n                          columnNumber: 55\n                        }, this), \"Colors\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1156,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-4\",\n                        children: fabricDetails.length > 0 ? fabricDetails.map((detail, index) => {\n                          var _detail$fabric_varian2, _detail$fabric_varian3;\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-2\",\n                            children: [renderColorSwatch(((_detail$fabric_varian2 = detail.fabric_variant_data) === null || _detail$fabric_varian2 === void 0 ? void 0 : _detail$fabric_varian2.color) || detail.color || 'gray'), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"ms-2\",\n                              children: ((_detail$fabric_varian3 = detail.fabric_variant_data) === null || _detail$fabric_varian3 === void 0 ? void 0 : _detail$fabric_varian3.color_name) || detail.color\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1162,\n                              columnNumber: 37\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1160,\n                            columnNumber: 35\n                          }, this);\n                        }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted\",\n                          children: \"No color information available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1166,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1157,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(FaBoxOpen, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1170,\n                          columnNumber: 50\n                        }, this), \"Size Distribution\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1170,\n                        columnNumber: 29\n                      }, this), renderSizeQuantityBars(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outline-primary\",\n                          className: \"w-100\",\n                          onClick: openPdfModal,\n                          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1179,\n                            columnNumber: 33\n                          }, this), \"Generate Product Report\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1174,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1173,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1186,\n                          columnNumber: 50\n                        }, this), \"Product Images\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1186,\n                        columnNumber: 29\n                      }, this), existingImageUrls && existingImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"product-images-container\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"product-images-grid\",\n                          children: existingImageUrls.map((imageUrl, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `product-image-item ${index === activeImageIndex ? 'active' : ''}`,\n                            onClick: () => setActiveImageIndex(index),\n                            children: [/*#__PURE__*/_jsxDEV(Image, {\n                              src: imageUrl,\n                              alt: `Product ${index + 1}`,\n                              thumbnail: true,\n                              className: \"image-preview\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1196,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"image-number\",\n                              children: index + 1\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1202,\n                              columnNumber: 39\n                            }, this)]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1191,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1189,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"main-image-container mt-3\",\n                          children: /*#__PURE__*/_jsxDEV(Image, {\n                            src: existingImageUrls[activeImageIndex],\n                            alt: \"Product\",\n                            thumbnail: true,\n                            className: \"main-image-preview\",\n                            style: {\n                              maxHeight: \"250px\"\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1207,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1206,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1188,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"p-5 bg-light rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                          size: 60,\n                          className: \"text-secondary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1218,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"mt-3 text-muted\",\n                          children: \"No images available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1219,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1217,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1185,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1045,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 989,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"slide-in\",\n                children: /*#__PURE__*/_jsxDEV(Tabs, {\n                  id: \"product-approval-tabs\",\n                  activeKey: activeTab,\n                  onSelect: k => setActiveTab(k),\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Tab, {\n                    eventKey: \"details\",\n                    title: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1234,\n                        columnNumber: 60\n                      }, this), \"Product Details\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1234,\n                      columnNumber: 54\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(Row, {\n                      className: \"mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1237,\n                            columnNumber: 50\n                          }, this), \"Product Name\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1237,\n                          columnNumber: 29\n                        }, this), isEditingName ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-4\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"text\",\n                              value: productName,\n                              onChange: e => setProductName(e.target.value),\n                              isInvalid: !!productNameError,\n                              placeholder: \"Enter product name\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1241,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: productNameError\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1248,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1240,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mt-2\",\n                            children: [/*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"success\",\n                              size: \"sm\",\n                              className: \"me-1\",\n                              onClick: saveProductName,\n                              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1259,\n                                columnNumber: 37\n                              }, this), \" Save\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1253,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Button, {\n                              variant: \"secondary\",\n                              size: \"sm\",\n                              onClick: cancelEditingName,\n                              children: [/*#__PURE__*/_jsxDEV(FaUndo, {\n                                className: \"me-1\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1266,\n                                columnNumber: 37\n                              }, this), \" Cancel\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1261,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1252,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1239,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1272,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Button, {\n                            variant: \"outline-primary\",\n                            size: \"sm\",\n                            onClick: startEditingName,\n                            children: \"Edit Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1277,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1271,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTags, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1287,\n                            columnNumber: 50\n                          }, this), \"Colors\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1287,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-4\",\n                          children: fabricDetails.length > 0 ? fabricDetails.map((detail, index) => {\n                            var _detail$fabric_varian4, _detail$fabric_varian5;\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"mb-2\",\n                              children: [renderColorSwatch(((_detail$fabric_varian4 = detail.fabric_variant_data) === null || _detail$fabric_varian4 === void 0 ? void 0 : _detail$fabric_varian4.color) || detail.color || 'gray'), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-2\",\n                                children: ((_detail$fabric_varian5 = detail.fabric_variant_data) === null || _detail$fabric_varian5 === void 0 ? void 0 : _detail$fabric_varian5.color_name) || detail.color\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1293,\n                                columnNumber: 37\n                              }, this)]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1291,\n                              columnNumber: 35\n                            }, this);\n                          }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-muted\",\n                            children: \"No color information available\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1297,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1288,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(FaBoxOpen, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1301,\n                            columnNumber: 50\n                          }, this), \"Size Distribution\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1301,\n                          columnNumber: 29\n                        }, this), renderSizeQuantityBars()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1236,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Col, {\n                        md: 6,\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mb-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(FaImage, {\n                              className: \"me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1307,\n                              columnNumber: 52\n                            }, this), \"Product Images\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1307,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-muted mb-3\",\n                            children: [\"Upload up to 10 images of the product (Current: \", productImages.length, \"/10)\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1308,\n                            columnNumber: 31\n                          }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"product-images-container mb-4\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"product-images-grid\",\n                              children: imagePreviewUrls.map((previewUrl, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `product-image-item ${index === activeImageIndex ? 'active' : ''}`,\n                                onClick: () => setActiveImageIndex(index),\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"image-actions\",\n                                  children: /*#__PURE__*/_jsxDEV(Button, {\n                                    variant: \"danger\",\n                                    size: \"sm\",\n                                    className: \"btn-remove-image\",\n                                    onClick: e => {\n                                      e.stopPropagation();\n                                      removeImage(index);\n                                    },\n                                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1330,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1321,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1320,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Image, {\n                                  src: previewUrl,\n                                  alt: `Preview ${index + 1}`,\n                                  thumbnail: true,\n                                  className: \"image-preview\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1333,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"image-number\",\n                                  children: index + 1\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1339,\n                                  columnNumber: 41\n                                }, this)]\n                              }, index, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1315,\n                                columnNumber: 39\n                              }, this))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1313,\n                              columnNumber: 35\n                            }, this), imagePreviewUrls.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"main-image-container mt-3\",\n                              children: /*#__PURE__*/_jsxDEV(Image, {\n                                src: imagePreviewUrls[activeImageIndex],\n                                alt: \"Product Preview\",\n                                thumbnail: true,\n                                className: \"main-image-preview\",\n                                style: {\n                                  maxHeight: \"250px\"\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1346,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1345,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1312,\n                            columnNumber: 33\n                          }, this), isUploading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"p-4 bg-light rounded text-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                              className: \"mb-3\",\n                              children: \"Uploading Images\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1361,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                              now: uploadProgress,\n                              label: `${Math.round(uploadProgress)}%`,\n                              variant: \"info\",\n                              animated: true,\n                              className: \"mb-3\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1362,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-muted\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                                className: \"me-2 text-primary\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1370,\n                                columnNumber: 37\n                              }, this), \"Uploading \", productImages.length, \" images...\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1369,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1360,\n                            columnNumber: 33\n                          }, this) : productImages.length < 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            ...getRootProps(),\n                            className: `image-upload-container ${isDragActive ? 'active' : ''}`,\n                            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                              ...getInputProps(),\n                              multiple: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1377,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-center\",\n                              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                                size: 40,\n                                className: \"mb-3 text-primary\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1379,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                children: \"Drag & drop product images here, or click to select\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1380,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"text-muted small\",\n                                children: \"Supported formats: JPEG, PNG, GIF (Max: 5MB each)\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1381,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                                className: \"text-muted small\",\n                                children: \"You can select multiple images at once (Max: 10)\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1382,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1378,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1376,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"file\",\n                            ref: fileInputRef,\n                            onChange: handleImageChange,\n                            accept: \"image/*\",\n                            multiple: true,\n                            style: {\n                              display: 'none'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1388,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1306,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1305,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1235,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1234,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                    eventKey: \"pricing\",\n                    title: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                        className: \"me-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1401,\n                        columnNumber: 60\n                      }, this), \"Pricing\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1401,\n                      columnNumber: 54\n                    }, this),\n                    children: /*#__PURE__*/_jsxDEV(Form, {\n                      onSubmit: handleFormSubmit,\n                      className: \"mt-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Row, {\n                        children: [/*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Manufacture Price (LKR):\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1407,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"The cost to manufacture this product\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1410,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1412,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1408,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1406,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"number\",\n                              step: \"0.01\",\n                              min: \"0\",\n                              value: manufacturePrice,\n                              onChange: e => setManufacturePrice(e.target.value),\n                              isInvalid: !!validationErrors.manufacturePrice,\n                              required: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1415,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.manufacturePrice\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1424,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1405,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Selling Price (LKR):\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1431,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"The price at which this product will be sold\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1434,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1436,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1432,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1430,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              type: \"number\",\n                              step: \"0.01\",\n                              min: \"0\",\n                              value: sellingPrice,\n                              onChange: e => setSellingPrice(e.target.value),\n                              isInvalid: !!validationErrors.sellingPrice,\n                              required: true\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1439,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.sellingPrice\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1448,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1429,\n                            columnNumber: 31\n                          }, this), manufacturePrice && sellingPrice && parseFloat(manufacturePrice) > 0 && parseFloat(sellingPrice) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-4 p-3 bg-light rounded\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex justify-content-between align-items-center mb-2\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                                  children: \"Profit Margin:\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1456,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1456,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                                bg: profitMargin < 10 ? \"danger\" : profitMargin < 20 ? \"warning\" : \"success\",\n                                children: [/*#__PURE__*/_jsxDEV(FaPercentage, {\n                                  className: \"me-1\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1462,\n                                  columnNumber: 39\n                                }, this), profitMargin, \"%\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1457,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1455,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"profit-margin-indicator\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1466,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex justify-content-between mt-1\",\n                              children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                                children: \"Low\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1468,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                                children: \"High\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1469,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1467,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1454,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1404,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Col, {\n                          md: 6,\n                          children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                            className: \"mb-3\",\n                            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                                children: \"Product Notes:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1478,\n                                columnNumber: 35\n                              }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n                                placement: \"top\",\n                                overlay: /*#__PURE__*/_jsxDEV(Tooltip, {\n                                  children: \"Additional information about this product\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1481,\n                                  columnNumber: 46\n                                }, this),\n                                children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                                  className: \"ms-2 text-muted\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1483,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1479,\n                                columnNumber: 35\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1477,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                              as: \"textarea\",\n                              rows: 5,\n                              value: productNotes,\n                              onChange: e => setProductNotes(e.target.value),\n                              isInvalid: !!validationErrors.productNotes,\n                              placeholder: \"Enter any additional notes about this product...\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1486,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                              type: \"invalid\",\n                              children: validationErrors.productNotes\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1494,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                              className: \"text-muted\",\n                              children: [productNotes ? 500 - productNotes.length : 500, \" characters remaining\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1497,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1476,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1475,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1403,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-grid gap-2 mt-4\",\n                        children: /*#__PURE__*/_jsxDEV(Button, {\n                          type: \"submit\",\n                          className: \"btn-approve\",\n                          size: \"lg\",\n                          children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1506,\n                            columnNumber: 31\n                          }, this), \"Approve Product\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1505,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1504,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1402,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1227,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1522,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: closePdfModal,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"text-danger me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1528,\n            columnNumber: 13\n          }, this), \"Generate Product Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1527,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1526,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Are you sure you want to generate a PDF report for this product?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1533,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-light p-3 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Product:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1535,\n              columnNumber: 33\n            }, this), \" \", productName || (productDetails && productDetails.fabric_definition_data ? productDetails.fabric_definition_data.fabric_name : `Batch ID: ${id}`)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Manufacture Price:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 33\n            }, this), \" LKR \", manufacturePrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Selling Price:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1539,\n              columnNumber: 33\n            }, this), \" LKR \", sellingPrice]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Profit Margin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1540,\n              columnNumber: 33\n            }, this), \" \", profitMargin, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1540,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: closePdfModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generateProductReport,\n          disabled: pdfLoading,\n          children: pdfLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Spinner, {\n              as: \"span\",\n              animation: \"border\",\n              size: \"sm\",\n              role: \"status\",\n              \"aria-hidden\": \"true\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 17\n            }, this), \"Generating...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1566,\n              columnNumber: 17\n            }, this), \"Generate PDF\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1547,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1543,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1525,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ApproveFinishedProduct, \"5KckY46D3LkO17wQKFjKYOi3e9Y=\", false, function () {\n  return [useParams, useNavigate, useDropzone];\n});\n_c = ApproveFinishedProduct;\nexport default ApproveFinishedProduct;\nvar _c;\n$RefreshReg$(_c, \"ApproveFinishedProduct\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "axios", "useParams", "useNavigate", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Row", "Col", "Spinner", "Image", "Modal", "ProgressBar", "Badge", "Tabs", "Tab", "Table", "OverlayTrigger", "<PERSON><PERSON><PERSON>", "FaCheck", "FaUpload", "FaImage", "FaTags", "FaInfoCircle", "FaMoneyBillWave", "FaArrowRight", "FaPercentage", "FaBoxOpen", "FaClipboardList", "FaTrash", "FaUndo", "FaExclamationTriangle", "FaFilePdf", "FaDownload", "useDropzone", "RoleBasedNavBar", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ApproveFinishedProduct", "_s", "id", "navigate", "fileInputRef", "manufacturePrice", "setManufacturePrice", "sellingPrice", "setSellingPrice", "productNotes", "setProductNotes", "error", "setError", "successMsg", "setSuccessMsg", "isApproved", "setIsApproved", "loading", "setLoading", "productImages", "setProductImages", "imagePreviewUrls", "setImagePreviewUrls", "existingImageUrls", "setExistingImageUrls", "isDragging", "setIsDragging", "activeImageIndex", "setActiveImageIndex", "uploadProgress", "setUploadProgress", "isUploading", "setIsUploading", "productDetails", "setProductDetails", "fabricDetails", "setFabricDetails", "sizeQuantities", "setSizeQuantities", "xs", "s", "m", "l", "xl", "activeTab", "setActiveTab", "showConfirmModal", "setShowConfirmModal", "showPdfModal", "setShowPdfModal", "pdfLoading", "setPdfLoading", "validationErrors", "setValidationErrors", "profitMargin", "setProfitMargin", "productName", "setProductName", "isEditingName", "setIsEditingName", "productNameError", "setProductNameError", "isEditingPrices", "setIsEditingPrices", "editManufacturePrice", "setEditManufacturePrice", "editSellingPrice", "setEditSellingPrice", "priceEditErrors", "setPriceEditErrors", "finishedProductId", "setFinishedProductId", "mPrice", "parseFloat", "sPrice", "margin", "toFixed", "retryCount", "setRetryCount", "maxRetries", "fetchProductData", "approvalRes", "get", "cuttingRes", "data", "is_approved", "manufacture_price", "selling_price", "finished_product_id", "product_images", "Array", "isArray", "product_image", "notes", "product_name", "fabric_definition_data", "fabric_name", "details", "length", "console", "log", "sizes", "for<PERSON>ach", "detail", "err", "errorMessage", "response", "status", "statusText", "request", "prev", "setTimeout", "handleImageChange", "e", "files", "from", "target", "processImageFiles", "newImages", "newPreviewUrls", "file", "type", "match", "size", "push", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "triggerFileInput", "current", "click", "onDrop", "acceptedFiles", "getRootProps", "getInputProps", "isDragActive", "accept", "maxFiles", "removeImage", "index", "splice", "Math", "max", "setActiveImage", "validateForm", "errors", "trim", "isNaN", "Object", "keys", "handleFormSubmit", "preventDefault", "handleSubmit", "formData", "FormData", "append", "image", "post", "headers", "onUploadProgress", "progressEvent", "percentCompleted", "round", "loaded", "total", "message", "errMsg", "JSON", "stringify", "handleCancelConfirmation", "openPdfModal", "closePdfModal", "startEditingName", "cancelEditingName", "saveProductName", "currentRecord", "payload", "fabric_definition", "cutting_date", "put", "updatedRecord", "checkErr", "startEditingPrices", "cancelEditingPrices", "validatePriceEdit", "manufactureStr", "String", "sellingStr", "savePriceChanges", "validationResult", "updateData", "patch", "generateProductReport", "doc", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "pdfProductName", "Date", "toLocaleDateString", "setDrawColor", "line", "yPos", "entries", "sizeHeaders", "totalQuantity", "reduce", "sum", "_", "qty", "quantity", "percentage", "toUpperCase", "toString", "_detail$fabric_varian", "colorName", "fabric_variant_data", "color_name", "color", "splitNotes", "splitTextToSize", "toLocaleString", "cleanProductName", "replace", "save", "toISOString", "slice", "className", "style", "height", "children", "animation", "role", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderColorSwatch", "bgColor", "startsWith", "test", "colorMap", "toLowerCase", "placement", "overlay", "backgroundColor", "renderSizeQuantityBars", "maxQuantity", "map", "now", "ConfirmationModal", "show", "onHide", "centered", "Header", "closeButton", "Title", "Body", "bordered", "hover", "src", "alt", "thumbnail", "maxHeight", "Footer", "onClick", "md", "lg", "borderRadius", "Group", "max<PERSON><PERSON><PERSON>", "Control", "value", "onChange", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "onSubmit", "Label", "step", "min", "disabled", "_detail$fabric_varian2", "_detail$fabric_varian3", "imageUrl", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "_detail$fabric_varian4", "_detail$fabric_varian5", "previewUrl", "stopPropagation", "label", "animated", "multiple", "ref", "display", "required", "bg", "as", "rows", "Text", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/ApproveFinishedProduct.js"], "sourcesContent": ["// src/pages/ApproveFinishedProduct.jsx\r\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { Card, Form, Button, Alert, Row, Col, Spinner, Image, Modal, ProgressBar, Badge, Tabs, Tab, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';\r\nimport {\r\n  FaCheck, FaUpload, FaImage, FaTags, FaInfoCircle, FaMoneyBillWave,\r\n  FaArrowRight, FaPercentage, FaBoxOpen, FaClipboardList,\r\n  FaTrash, FaUndo, FaExclamationTriangle, FaFilePdf, FaDownload\r\n} from 'react-icons/fa';\r\nimport { useDropzone } from 'react-dropzone';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport './ApproveFinishedProduct.css';\r\nimport { jsPDF } from 'jspdf';\r\n// No need to import uploadMultipleImages as we're using FormData directly\r\n\r\nconst ApproveFinishedProduct = () => {\r\n  const { id } = useParams();\r\n  const navigate = useNavigate();\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Basic form state\r\n  const [manufacturePrice, setManufacturePrice] = useState('');\r\n  const [sellingPrice, setSellingPrice] = useState('');\r\n  const [productNotes, setProductNotes] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [successMsg, setSuccessMsg] = useState('');\r\n  const [isApproved, setIsApproved] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Image handling state\r\n  const [productImages, setProductImages] = useState([]);\r\n  const [imagePreviewUrls, setImagePreviewUrls] = useState([]);\r\n  const [existingImageUrls, setExistingImageUrls] = useState([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [activeImageIndex, setActiveImageIndex] = useState(0);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n\r\n  // Product details state\r\n  const [productDetails, setProductDetails] = useState(null);\r\n  const [fabricDetails, setFabricDetails] = useState([]);\r\n  const [sizeQuantities, setSizeQuantities] = useState({\r\n    xs: 0, s: 0, m: 0, l: 0, xl: 0\r\n  });\r\n\r\n  // UI state\r\n  const [activeTab, setActiveTab] = useState('details');\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [pdfLoading, setPdfLoading] = useState(false);\r\n  const [validationErrors, setValidationErrors] = useState({});\r\n  const [profitMargin, setProfitMargin] = useState(0);\r\n\r\n  // Product name editing state\r\n  const [productName, setProductName] = useState('');\r\n  const [isEditingName, setIsEditingName] = useState(false);\r\n  const [productNameError, setProductNameError] = useState('');\r\n\r\n  // Price editing state for approved products\r\n  const [isEditingPrices, setIsEditingPrices] = useState(false);\r\n  const [editManufacturePrice, setEditManufacturePrice] = useState('');\r\n  const [editSellingPrice, setEditSellingPrice] = useState('');\r\n  const [priceEditErrors, setPriceEditErrors] = useState({});\r\n  const [finishedProductId, setFinishedProductId] = useState(null);\r\n\r\n  // Calculate profit margin whenever prices change\r\n  useEffect(() => {\r\n    if (manufacturePrice && sellingPrice) {\r\n      const mPrice = parseFloat(manufacturePrice);\r\n      const sPrice = parseFloat(sellingPrice);\r\n\r\n      if (mPrice > 0 && sPrice > 0) {\r\n        const margin = ((sPrice - mPrice) / sPrice) * 100;\r\n        setProfitMargin(margin.toFixed(2));\r\n      }\r\n    }\r\n  }, [manufacturePrice, sellingPrice]);\r\n\r\n  // State for retry mechanism\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const maxRetries = 3;\r\n\r\n  // Function to fetch product data with retry mechanism\r\n  const fetchProductData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n\r\n      // Fetch approval status\r\n      const approvalRes = await axios.get(`http://localhost:8000/api/finished_product/status/${id}/`);\r\n\r\n      // Fetch cutting record details\r\n      const cuttingRes = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      if (approvalRes.data && approvalRes.data.is_approved) {\r\n        setIsApproved(true);\r\n        setManufacturePrice(approvalRes.data.manufacture_price);\r\n        setSellingPrice(approvalRes.data.selling_price);\r\n        setFinishedProductId(approvalRes.data.finished_product_id);\r\n\r\n        // Set existing image URLs if available\r\n        if (approvalRes.data.product_images && Array.isArray(approvalRes.data.product_images)) {\r\n          setExistingImageUrls(approvalRes.data.product_images);\r\n        } else if (approvalRes.data.product_image) {\r\n          // For backward compatibility with single image\r\n          setExistingImageUrls([approvalRes.data.product_image]);\r\n        }\r\n\r\n        if (approvalRes.data.notes) {\r\n          setProductNotes(approvalRes.data.notes);\r\n        }\r\n      }\r\n\r\n      if (cuttingRes.data) {\r\n        setProductDetails(cuttingRes.data);\r\n\r\n        // Set product name from API response\r\n        if (cuttingRes.data.product_name) {\r\n          setProductName(cuttingRes.data.product_name);\r\n        } else if (cuttingRes.data.fabric_definition_data) {\r\n          // If no product name, use fabric name as default\r\n          setProductName(cuttingRes.data.fabric_definition_data.fabric_name);\r\n        }\r\n\r\n        // Extract fabric details\r\n        if (cuttingRes.data.details && cuttingRes.data.details.length > 0) {\r\n          // Debug: Log the fabric details to see what color data we're getting\r\n          console.log('Fabric details from API:', cuttingRes.data.details);\r\n\r\n          setFabricDetails(cuttingRes.data.details);\r\n\r\n          // Calculate size quantities\r\n          const sizes = {xs: 0, s: 0, m: 0, l: 0, xl: 0};\r\n          cuttingRes.data.details.forEach(detail => {\r\n            sizes.xs += detail.xs || 0;\r\n            sizes.s += detail.s || 0;\r\n            sizes.m += detail.m || 0;\r\n            sizes.l += detail.l || 0;\r\n            sizes.xl += detail.xl || 0;\r\n          });\r\n          setSizeQuantities(sizes);\r\n        }\r\n      }\r\n\r\n      // Reset retry count on success\r\n      setRetryCount(0);\r\n    } catch (err) {\r\n      console.error(\"Failed to fetch product data:\", err);\r\n\r\n      // Provide more detailed error message\r\n      const errorMessage = err.response\r\n        ? `Error: ${err.response.status} - ${err.response.statusText}`\r\n        : err.request\r\n          ? \"No response received from server. Check if the backend is running.\"\r\n          : \"Failed to make request. Check your network connection.\";\r\n\r\n      setError(`Unable to fetch product data. ${errorMessage}`);\r\n\r\n      // Implement retry mechanism\r\n      if (retryCount < maxRetries) {\r\n        setRetryCount(prev => prev + 1);\r\n        setTimeout(() => {\r\n          fetchProductData();\r\n        }, 2000); // Wait 2 seconds before retrying\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [id, retryCount]);\r\n\r\n  // Fetch product details and approval status\r\n  useEffect(() => {\r\n    fetchProductData();\r\n  }, [fetchProductData]);\r\n\r\n  // Handle image selection from file input\r\n  const handleImageChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length > 0) {\r\n      processImageFiles(files);\r\n    }\r\n  };\r\n\r\n  // Process the selected image files\r\n  const processImageFiles = useCallback((files) => {\r\n    // Check if adding these files would exceed the limit\r\n    if (productImages.length + files.length > 10) {\r\n      setError(`You can only upload up to 10 images. You already have ${productImages.length} images.`);\r\n      return;\r\n    }\r\n\r\n    const newImages = [];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    files.forEach(file => {\r\n      // Validate file type\r\n      if (!file.type.match('image.*')) {\r\n        setError('Please select image files only (JPEG, PNG, etc.)');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        setError('Each image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      newImages.push(file);\r\n\r\n      // Create a preview URL\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        newPreviewUrls.push(reader.result);\r\n        setImagePreviewUrls([...newPreviewUrls]);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    });\r\n\r\n    setProductImages([...productImages, ...newImages]);\r\n  }, [productImages, imagePreviewUrls]);\r\n\r\n  // Trigger file input click\r\n  const triggerFileInput = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  // Handle drag and drop functionality\r\n  const onDrop = useCallback((acceptedFiles) => {\r\n    if (acceptedFiles && acceptedFiles.length > 0) {\r\n      processImageFiles(acceptedFiles);\r\n    }\r\n  }, [processImageFiles]);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif']\r\n    },\r\n    maxFiles: 10\r\n  });\r\n\r\n  // Remove an uploaded image\r\n  const removeImage = (index) => {\r\n    const newImages = [...productImages];\r\n    const newPreviewUrls = [...imagePreviewUrls];\r\n\r\n    newImages.splice(index, 1);\r\n    newPreviewUrls.splice(index, 1);\r\n\r\n    setProductImages(newImages);\r\n    setImagePreviewUrls(newPreviewUrls);\r\n\r\n    // Adjust active index if needed\r\n    if (index === activeImageIndex) {\r\n      setActiveImageIndex(Math.max(0, index - 1));\r\n    } else if (index < activeImageIndex) {\r\n      setActiveImageIndex(activeImageIndex - 1);\r\n    }\r\n  };\r\n\r\n  // Set active image\r\n  const setActiveImage = (index) => {\r\n    setActiveImageIndex(index);\r\n  };\r\n\r\n  // Validate form inputs\r\n  const validateForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    if (!manufacturePrice || manufacturePrice.trim() === '') {\r\n      errors.manufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(manufacturePrice) <= 0) {\r\n      errors.manufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(manufacturePrice))) {\r\n      errors.manufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    if (!sellingPrice || sellingPrice.trim() === '') {\r\n      errors.sellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(sellingPrice) <= 0) {\r\n      errors.sellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(sellingPrice))) {\r\n      errors.sellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(sellingPrice) < parseFloat(manufacturePrice)) {\r\n      errors.sellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    // Validate product notes (optional)\r\n    if (productNotes && productNotes.length > 500) {\r\n      errors.productNotes = \"Notes should be less than 500 characters\";\r\n    }\r\n\r\n    setValidationErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Show confirmation modal\r\n  const handleFormSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    // Show confirmation modal\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  // Handle actual submission\r\n  const handleSubmit = async () => {\r\n    setError('');\r\n    setSuccessMsg('');\r\n    setShowConfirmModal(false);\r\n\r\n    try {\r\n      // Show loading state\r\n      setLoading(true);\r\n      setIsUploading(true);\r\n\r\n      // Create FormData object for API request\r\n      const formData = new FormData();\r\n      formData.append('cutting_record', id);\r\n      formData.append('manufacture_price', parseFloat(manufacturePrice));\r\n      formData.append('selling_price', parseFloat(sellingPrice));\r\n\r\n      if (productNotes) {\r\n        formData.append('notes', productNotes);\r\n      }\r\n\r\n      // Add images directly to the FormData if there are any\r\n      if (productImages && productImages.length > 0) {\r\n        setUploadProgress(0);\r\n\r\n        // Append each image to the FormData\r\n        productImages.forEach(image => {\r\n          formData.append('product_images', image);\r\n        });\r\n      }\r\n\r\n      // Make the API request with progress tracking\r\n      const response = await axios.post(\r\n        'http://localhost:8000/api/finished_product/approve/',\r\n        formData,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n          onUploadProgress: (progressEvent) => {\r\n            const percentCompleted = Math.round(\r\n              (progressEvent.loaded * 100) / progressEvent.total\r\n            );\r\n            setUploadProgress(percentCompleted);\r\n          }\r\n        }\r\n      );\r\n\r\n      setSuccessMsg(response.data.message || 'Product approved successfully!');\r\n      setIsUploading(false);\r\n\r\n      // Redirect after a delay\r\n      setTimeout(() => {\r\n        navigate('/approveproduct-list');\r\n      }, 2000);\r\n    } catch (err) {\r\n      console.error(\"Error approving finished product:\", err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to approve finished product. Please try again.\";\r\n      setError(errMsg);\r\n      setIsUploading(false);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Cancel confirmation\r\n  const handleCancelConfirmation = () => {\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  // Open PDF modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Close PDF modal\r\n  const closePdfModal = () => {\r\n    setShowPdfModal(false);\r\n  };\r\n\r\n  // Start editing product name\r\n  const startEditingName = () => {\r\n    setIsEditingName(true);\r\n  };\r\n\r\n  // Cancel editing product name\r\n  const cancelEditingName = () => {\r\n    setIsEditingName(false);\r\n    setProductNameError('');\r\n    // Reset to original name from product details\r\n    if (productDetails && productDetails.product_name) {\r\n      setProductName(productDetails.product_name);\r\n    } else if (productDetails && productDetails.fabric_definition_data) {\r\n      setProductName(productDetails.fabric_definition_data.fabric_name);\r\n    }\r\n  };\r\n\r\n  // Save updated product name\r\n  const saveProductName = async () => {\r\n    // Validate product name\r\n    if (!productName.trim()) {\r\n      setProductNameError('Product name cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setProductNameError('');\r\n    setLoading(true);\r\n\r\n    try {\r\n      // First, get the current cutting record data\r\n      const currentRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n\r\n      // Create a payload with all the required fields, including details\r\n      const payload = {\r\n        fabric_definition: currentRecord.data.fabric_definition,\r\n        cutting_date: currentRecord.data.cutting_date,\r\n        product_name: productName,\r\n        details: currentRecord.data.details  // Include existing details without modification\r\n      };\r\n\r\n      // Update the product name in the backend\r\n      await axios.put(`http://localhost:8000/api/cutting/cutting-records/${id}/`, payload);\r\n\r\n      // Update local state\r\n      setProductDetails({\r\n        ...productDetails,\r\n        product_name: productName\r\n      });\r\n\r\n      setIsEditingName(false);\r\n      setSuccessMsg('Product name updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating product name:', err);\r\n\r\n      // Check if the product name was actually updated despite the error\r\n      try {\r\n        const updatedRecord = await axios.get(`http://localhost:8000/api/cutting/records/${id}/`);\r\n        if (updatedRecord.data.product_name === productName) {\r\n          // If the name was updated successfully despite the error, show success message\r\n          setProductDetails({\r\n            ...productDetails,\r\n            product_name: productName\r\n          });\r\n          setIsEditingName(false);\r\n          setSuccessMsg('Product name updated successfully');\r\n\r\n          // Clear success message after 3 seconds\r\n          setTimeout(() => {\r\n            setSuccessMsg('');\r\n          }, 3000);\r\n          return;\r\n        }\r\n      } catch (checkErr) {\r\n        // If we can't check, just show the original error\r\n        console.error('Error checking product name update:', checkErr);\r\n      }\r\n\r\n      // Show error message if the name wasn't updated\r\n      setError('Failed to update product name. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Start editing prices\r\n  const startEditingPrices = () => {\r\n    setEditManufacturePrice(manufacturePrice);\r\n    setEditSellingPrice(sellingPrice);\r\n    setIsEditingPrices(true);\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Cancel editing prices\r\n  const cancelEditingPrices = () => {\r\n    setIsEditingPrices(false);\r\n    setEditManufacturePrice('');\r\n    setEditSellingPrice('');\r\n    setPriceEditErrors({});\r\n  };\r\n\r\n  // Validate price editing form\r\n  const validatePriceEdit = () => {\r\n    const errors = {};\r\n\r\n    // Validate manufacture price\r\n    const manufactureStr = String(editManufacturePrice || '').trim();\r\n    if (!editManufacturePrice || manufactureStr === '') {\r\n      errors.editManufacturePrice = \"Manufacture price is required\";\r\n    } else if (parseFloat(editManufacturePrice) <= 0) {\r\n      errors.editManufacturePrice = \"Manufacture price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editManufacturePrice))) {\r\n      errors.editManufacturePrice = \"Manufacture price must be a valid number\";\r\n    }\r\n\r\n    // Validate selling price\r\n    const sellingStr = String(editSellingPrice || '').trim();\r\n    if (!editSellingPrice || sellingStr === '') {\r\n      errors.editSellingPrice = \"Selling price is required\";\r\n    } else if (parseFloat(editSellingPrice) <= 0) {\r\n      errors.editSellingPrice = \"Selling price must be greater than zero\";\r\n    } else if (isNaN(parseFloat(editSellingPrice))) {\r\n      errors.editSellingPrice = \"Selling price must be a valid number\";\r\n    } else if (parseFloat(editSellingPrice) < parseFloat(editManufacturePrice)) {\r\n      errors.editSellingPrice = \"Selling price should be greater than or equal to manufacture price\";\r\n    }\r\n\r\n    setPriceEditErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Save updated prices\r\n  const savePriceChanges = async () => {\r\n    console.log('=== savePriceChanges called ===');\r\n    console.log('editManufacturePrice:', editManufacturePrice);\r\n    console.log('editSellingPrice:', editSellingPrice);\r\n    console.log('finishedProductId:', finishedProductId);\r\n    console.log('loading state:', loading);\r\n\r\n    const validationResult = validatePriceEdit();\r\n    console.log('Validation result:', validationResult);\r\n    if (!validationResult) {\r\n      console.log('Validation failed - stopping execution');\r\n      return;\r\n    }\r\n\r\n    if (!finishedProductId) {\r\n      console.log('No finished product ID - stopping execution');\r\n      setError('Could not find finished product record');\r\n      return;\r\n    }\r\n\r\n    console.log('Validation passed, proceeding with API call...');\r\n\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Update the prices using the PATCH endpoint\r\n      const updateData = {\r\n        manufacture_price: parseFloat(editManufacturePrice),\r\n        selling_price: parseFloat(editSellingPrice)\r\n      };\r\n\r\n      console.log('Sending update data:', updateData);\r\n      await axios.patch(`http://localhost:8000/api/finished_product/update/${finishedProductId}/`, updateData);\r\n\r\n      // Update local state\r\n      setManufacturePrice(editManufacturePrice);\r\n      setSellingPrice(editSellingPrice);\r\n      setIsEditingPrices(false);\r\n      setSuccessMsg('Prices updated successfully');\r\n\r\n      // Clear success message after 3 seconds\r\n      setTimeout(() => {\r\n        setSuccessMsg('');\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Error updating prices:', err);\r\n      const errMsg = err.response && err.response.data\r\n        ? typeof err.response.data === 'object'\r\n          ? JSON.stringify(err.response.data)\r\n          : err.response.data\r\n        : \"Failed to update prices. Please try again.\";\r\n      setError(errMsg);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Generate PDF report for the product\r\n  const generateProductReport = () => {\r\n    setPdfLoading(true);\r\n\r\n    try {\r\n      // Create PDF document\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes\r\n      const titleFontSize = 16;\r\n      const headingFontSize = 12;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add header\r\n      doc.setFontSize(titleFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Product Report', 105, 20, { align: 'center' });\r\n\r\n      // Add product name\r\n      const pdfProductName = productName || (productDetails && productDetails.fabric_definition_data\r\n        ? productDetails.fabric_definition_data.fabric_name\r\n        : `Batch ID: ${id}`);\r\n      doc.setFontSize(headingFontSize);\r\n      doc.text(pdfProductName, 105, 30, { align: 'center' });\r\n\r\n      // Add approval date\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Approval Date: ${new Date().toLocaleDateString()}`, 105, 40, { align: 'center' });\r\n\r\n      // Add horizontal line\r\n      doc.setDrawColor(200, 200, 200);\r\n      doc.line(20, 45, 190, 45);\r\n\r\n      // Start Y position for content\r\n      let yPos = 55;\r\n\r\n      // Add pricing information\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Pricing Information', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Manufacture Price: LKR ${manufacturePrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Selling Price: LKR ${sellingPrice}`, 25, yPos);\r\n      yPos += 7;\r\n      doc.text(`Profit Margin: ${profitMargin}%`, 25, yPos);\r\n      yPos += 15;\r\n\r\n      // Add size distribution\r\n      doc.setFontSize(headingFontSize);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Size Distribution', 20, yPos);\r\n      yPos += 10;\r\n\r\n      doc.setFontSize(normalFontSize);\r\n      doc.setFont('helvetica', 'normal');\r\n\r\n      // Create a table for size distribution\r\n      const sizes = Object.entries(sizeQuantities);\r\n      const sizeHeaders = ['Size', 'Quantity', 'Percentage'];\r\n      const totalQuantity = sizes.reduce((sum, [_, qty]) => sum + qty, 0);\r\n\r\n      // Draw table headers\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text(sizeHeaders[0], 25, yPos);\r\n      doc.text(sizeHeaders[1], 60, yPos);\r\n      doc.text(sizeHeaders[2], 95, yPos);\r\n      yPos += 7;\r\n\r\n      // Draw table rows\r\n      doc.setFont('helvetica', 'normal');\r\n      sizes.forEach(([size, quantity]) => {\r\n        const percentage = totalQuantity > 0 ? ((quantity / totalQuantity) * 100).toFixed(1) : '0.0';\r\n        doc.text(size.toUpperCase(), 25, yPos);\r\n        doc.text(quantity.toString(), 60, yPos);\r\n        doc.text(`${percentage}%`, 95, yPos);\r\n        yPos += 7;\r\n      });\r\n\r\n      // Add total row\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Total', 25, yPos);\r\n      doc.text(totalQuantity.toString(), 60, yPos);\r\n      doc.text('100.0%', 95, yPos);\r\n      yPos += 15;\r\n\r\n      // Add color information\r\n      if (fabricDetails.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Color Information', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        fabricDetails.forEach((detail, index) => {\r\n          const colorName = detail.fabric_variant_data?.color_name || detail.color || 'N/A';\r\n          doc.text(`Color ${index + 1}: ${colorName}`, 25, yPos);\r\n          yPos += 7;\r\n        });\r\n\r\n        yPos += 8;\r\n      }\r\n\r\n      // Add product notes if available\r\n      if (productNotes) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Notes', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n\r\n        // Split notes into multiple lines if needed\r\n        const splitNotes = doc.splitTextToSize(productNotes, 160);\r\n        doc.text(splitNotes, 25, yPos);\r\n        yPos += splitNotes.length * 7 + 8;\r\n      }\r\n\r\n      // Add image information\r\n      if (existingImageUrls && existingImageUrls.length > 0) {\r\n        doc.setFontSize(headingFontSize);\r\n        doc.setFont('helvetica', 'bold');\r\n        doc.text('Product Images', 20, yPos);\r\n        yPos += 10;\r\n\r\n        doc.setFontSize(normalFontSize);\r\n        doc.setFont('helvetica', 'normal');\r\n        doc.text(`Number of Images: ${existingImageUrls.length}`, 25, yPos);\r\n        yPos += 7;\r\n        doc.text('Note: Images can be viewed in the system', 25, yPos);\r\n        yPos += 15;\r\n      }\r\n\r\n      // Add footer\r\n      doc.setFontSize(smallFontSize);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      doc.text('Pri Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Product_Report_${cleanProductName}_${new Date().toISOString().slice(0, 10)}.pdf`);\r\n\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      setError(`Failed to generate PDF: ${error.message}`);\r\n      setPdfLoading(false);\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Loading spinner\r\n  if (loading) return (\r\n    <div className=\"d-flex justify-content-center align-items-center\" style={{ height: \"100vh\" }}>\r\n      <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n        <span className=\"visually-hidden\">Loading...</span>\r\n      </Spinner>\r\n    </div>\r\n  );\r\n\r\n  // Render color swatch\r\n  const renderColorSwatch = (color) => {\r\n    if (!color) return null;\r\n\r\n    // For debugging - log the color value we're receiving\r\n    console.log('Color value received:', color);\r\n\r\n    // SIMPLIFIED APPROACH: Directly use the color value if it looks like a hex code\r\n    let bgColor;\r\n\r\n    // If it starts with #, use it directly\r\n    if (color.startsWith('#')) {\r\n      bgColor = color;\r\n    }\r\n    // If it looks like a hex code without #, add the #\r\n    else if (/^[0-9A-Fa-f]{6}$/.test(color) || /^[0-9A-Fa-f]{3}$/.test(color)) {\r\n      bgColor = `#${color}`;\r\n    }\r\n    // For named colors like \"Black\", \"Red\", etc.\r\n    else {\r\n      // Common color names mapping\r\n      const colorMap = {\r\n        'red': '#dc3545',\r\n        'blue': '#0d6efd',\r\n        'green': '#198754',\r\n        'yellow': '#ffc107',\r\n        'black': '#212529',\r\n        'white': '#f8f9fa',\r\n        'purple': '#6f42c1',\r\n        'orange': '#fd7e14',\r\n        'pink': '#d63384',\r\n        'brown': '#8B4513',\r\n        'gray': '#6c757d',\r\n      };\r\n\r\n      // Try to get from color map or use the name directly\r\n      bgColor = colorMap[color.toLowerCase()] || color;\r\n    }\r\n\r\n    return (\r\n      <OverlayTrigger\r\n        placement=\"top\"\r\n        overlay={<Tooltip className=\"custom-tooltip\">{color}</Tooltip>}\r\n      >\r\n        <div\r\n          className=\"color-swatch\"\r\n          style={{ backgroundColor: bgColor }}\r\n          data-color={color}\r\n        />\r\n      </OverlayTrigger>\r\n    );\r\n  };\r\n\r\n  // Render size quantity bars\r\n  const renderSizeQuantityBars = () => {\r\n    const sizes = Object.entries(sizeQuantities);\r\n    const maxQuantity = Math.max(...sizes.map(([_, qty]) => qty));\r\n\r\n    return sizes.map(([size, quantity]) => (\r\n      <div key={size} className=\"mb-2\">\r\n        <div className=\"d-flex justify-content-between mb-1\">\r\n          <span><strong>{size.toUpperCase()}</strong></span>\r\n          <span>{quantity} pcs</span>\r\n        </div>\r\n        <ProgressBar\r\n          now={maxQuantity ? (quantity / maxQuantity) * 100 : 0}\r\n          variant={quantity > 0 ? \"info\" : \"light\"}\r\n          className=\"size-quantity-bar\"\r\n        />\r\n      </div>\r\n    ));\r\n  };\r\n\r\n  // Confirmation Modal\r\n  const ConfirmationModal = () => (\r\n    <Modal\r\n      show={showConfirmModal}\r\n      onHide={handleCancelConfirmation}\r\n      centered\r\n      className=\"confirmation-modal\"\r\n    >\r\n      <Modal.Header closeButton>\r\n        <Modal.Title>Confirm Product Approval</Modal.Title>\r\n      </Modal.Header>\r\n      <Modal.Body>\r\n        <p>Are you sure you want to approve this product with the following details?</p>\r\n        <Table bordered hover size=\"sm\" className=\"mt-3\">\r\n          <tbody>\r\n            <tr>\r\n              <td><strong>Product Name:</strong></td>\r\n              <td>{productName || (productDetails && productDetails.fabric_definition_data ?\r\n                productDetails.fabric_definition_data.fabric_name :\r\n                `Batch ID: ${id}`)}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Manufacture Price:</strong></td>\r\n              <td>LKR {manufacturePrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Selling Price:</strong></td>\r\n              <td>LKR {sellingPrice}</td>\r\n            </tr>\r\n            <tr>\r\n              <td><strong>Profit Margin:</strong></td>\r\n              <td>{profitMargin}%</td>\r\n            </tr>\r\n            {productNotes && (\r\n              <tr>\r\n                <td><strong>Notes:</strong></td>\r\n                <td>{productNotes}</td>\r\n              </tr>\r\n            )}\r\n          </tbody>\r\n        </Table>\r\n        {imagePreviewUrls.length > 0 && (\r\n          <div className=\"text-center mt-3\">\r\n            <p><strong>Product Image:</strong></p>\r\n            <Image\r\n              src={imagePreviewUrls[0]}\r\n              alt=\"Product\"\r\n              thumbnail\r\n              style={{ maxHeight: \"100px\" }}\r\n            />\r\n          </div>\r\n        )}\r\n      </Modal.Body>\r\n      <Modal.Footer>\r\n        <Button variant=\"secondary\" onClick={handleCancelConfirmation}>\r\n          Cancel\r\n        </Button>\r\n        <Button variant=\"success\" onClick={handleSubmit}>\r\n          <FaCheck className=\"me-2\" />\r\n          Confirm Approval\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div className=\"main-content\">\r\n        <Row className=\"justify-content-center\">\r\n          <Col md={10} lg={8}>\r\n            <Card className=\"shadow product-card slide-in\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n              <Card.Body>\r\n                <h2 className=\"text-center mb-3\">Approve Finished Product</h2>\r\n\r\n                {/* Product Name with Edit Functionality */}\r\n                <div className=\"text-center mb-4\">\r\n                  {isEditingName ? (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                        <Form.Control\r\n                          type=\"text\"\r\n                          value={productName}\r\n                          onChange={(e) => setProductName(e.target.value)}\r\n                          isInvalid={!!productNameError}\r\n                          placeholder=\"Enter product name\"\r\n                        />\r\n                        <Form.Control.Feedback type=\"invalid\">\r\n                          {productNameError}\r\n                        </Form.Control.Feedback>\r\n                      </Form.Group>\r\n                      <Button\r\n                        variant=\"success\"\r\n                        size=\"sm\"\r\n                        className=\"me-1\"\r\n                        onClick={saveProductName}\r\n                      >\r\n                        <FaCheck />\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        size=\"sm\"\r\n                        onClick={cancelEditingName}\r\n                      >\r\n                        <FaUndo />\r\n                      </Button>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"d-flex justify-content-center align-items-center\">\r\n                      <p className=\"text-center text-muted mb-0 me-2\">\r\n                        {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                          productDetails.fabric_definition_data.fabric_name :\r\n                          `Batch ID: ${id}`)}\r\n                      </p>\r\n                      <Button\r\n                        variant=\"outline-primary\"\r\n                        size=\"sm\"\r\n                        onClick={startEditingName}\r\n                      >\r\n                        Edit Name\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {error && (\r\n                  <Alert variant=\"danger\" className=\"mb-4 fade-in\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div>\r\n                        <FaExclamationTriangle className=\"me-2\" />\r\n                        {error}\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={fetchProductData}\r\n                      >\r\n                        Retry\r\n                      </Button>\r\n                    </div>\r\n                  </Alert>\r\n                )}\r\n\r\n                {successMsg && (\r\n                  <Alert variant=\"success\" className=\"mb-4 fade-in\">\r\n                    <FaCheck className=\"me-2\" />\r\n                    {successMsg}\r\n                  </Alert>\r\n                )}\r\n\r\n                {isApproved ? (\r\n                  <div className=\"p-4 bg-white rounded mb-3 slide-in\">\r\n                    <h4 className=\"text-center mb-4 text-success\">\r\n                      <FaCheck className=\"me-2\" />\r\n                      Product Already Approved\r\n                    </h4>\r\n\r\n                    <div className=\"text-center mb-4\">\r\n                      {isEditingName ? (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <Form.Group className=\"mb-0 me-2 flex-grow-1\" style={{ maxWidth: '300px' }}>\r\n                            <Form.Control\r\n                              type=\"text\"\r\n                              value={productName}\r\n                              onChange={(e) => setProductName(e.target.value)}\r\n                              isInvalid={!!productNameError}\r\n                              placeholder=\"Enter product name\"\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {productNameError}\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                          <Button\r\n                            variant=\"success\"\r\n                            size=\"sm\"\r\n                            className=\"me-1\"\r\n                            onClick={saveProductName}\r\n                          >\r\n                            <FaCheck />\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"sm\"\r\n                            onClick={cancelEditingName}\r\n                          >\r\n                            <FaUndo />\r\n                          </Button>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <h5 className=\"mb-0 me-2\">\r\n                            {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                              productDetails.fabric_definition_data.fabric_name :\r\n                              `Batch ID: ${id}`)}\r\n                          </h5>\r\n                          <Button\r\n                            variant=\"outline-primary\"\r\n                            size=\"sm\"\r\n                            onClick={startEditingName}\r\n                          >\r\n                            Edit Name\r\n                          </Button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"p-4 bg-white rounded mb-3\">\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                              <h5 className=\"mb-0\"><FaMoneyBillWave className=\"me-2\" />Pricing Information</h5>\r\n                              {!isEditingPrices && (\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingPrices}\r\n                                >\r\n                                  Edit Prices\r\n                                </Button>\r\n                              )}\r\n                            </div>\r\n\r\n                            {isEditingPrices ? (\r\n                              <div className=\"p-3 bg-light rounded\">\r\n                                <Form onSubmit={(e) => e.preventDefault()}>\r\n                                  <Row>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Manufacture Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editManufacturePrice}\r\n                                          onChange={(e) => setEditManufacturePrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editManufacturePrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editManufacturePrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                    <Col md={6}>\r\n                                      <Form.Group className=\"mb-3\">\r\n                                        <Form.Label><strong>Selling Price (LKR):</strong></Form.Label>\r\n                                        <Form.Control\r\n                                          type=\"number\"\r\n                                          step=\"0.01\"\r\n                                          min=\"0\"\r\n                                          value={editSellingPrice}\r\n                                          onChange={(e) => setEditSellingPrice(e.target.value)}\r\n                                          isInvalid={!!priceEditErrors.editSellingPrice}\r\n                                        />\r\n                                        <Form.Control.Feedback type=\"invalid\">\r\n                                          {priceEditErrors.editSellingPrice}\r\n                                        </Form.Control.Feedback>\r\n                                      </Form.Group>\r\n                                    </Col>\r\n                                  </Row>\r\n\r\n                                  {editManufacturePrice && editSellingPrice && parseFloat(editManufacturePrice) > 0 && parseFloat(editSellingPrice) > 0 && (\r\n                                    <div className=\"mb-3 p-2 bg-info bg-opacity-10 rounded\">\r\n                                      <strong>New Profit Margin: </strong>\r\n                                      {(((parseFloat(editSellingPrice) - parseFloat(editManufacturePrice)) / parseFloat(editSellingPrice)) * 100).toFixed(2)}%\r\n                                    </div>\r\n                                  )}\r\n\r\n                                  <div className=\"d-flex gap-2\">\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      variant=\"success\"\r\n                                      size=\"sm\"\r\n                                      onClick={savePriceChanges}\r\n                                      disabled={loading}\r\n                                    >\r\n                                      <FaCheck className=\"me-1\" />\r\n                                      Save Changes\r\n                                    </Button>\r\n                                    <Button\r\n                                      type=\"button\"\r\n                                      variant=\"secondary\"\r\n                                      size=\"sm\"\r\n                                      onClick={cancelEditingPrices}\r\n                                    >\r\n                                      <FaUndo className=\"me-1\" />\r\n                                      Cancel\r\n                                    </Button>\r\n                                  </div>\r\n                                </Form>\r\n                              </div>\r\n                            ) : (\r\n                              <Table bordered hover>\r\n                                <tbody>\r\n                                  <tr>\r\n                                    <td><strong>Manufacture Price:</strong></td>\r\n                                    <td>LKR {manufacturePrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Selling Price:</strong></td>\r\n                                    <td>LKR {sellingPrice}</td>\r\n                                  </tr>\r\n                                  <tr>\r\n                                    <td><strong>Profit Margin:</strong></td>\r\n                                    <td>{profitMargin}%</td>\r\n                                  </tr>\r\n                                </tbody>\r\n                              </Table>\r\n                            )}\r\n\r\n                            {productNotes && (\r\n                              <div className=\"mt-4\">\r\n                                <h5 className=\"mb-3\"><FaClipboardList className=\"me-2\" />Notes</h5>\r\n                                <div className=\"p-3 bg-light rounded\">\r\n                                  {productNotes}\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3 mt-4\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n\r\n                            <div className=\"mt-4\">\r\n                              <Button\r\n                                variant=\"outline-primary\"\r\n                                className=\"w-100\"\r\n                                onClick={openPdfModal}\r\n                              >\r\n                                <FaFilePdf className=\"me-2\" />\r\n                                Generate Product Report\r\n                              </Button>\r\n                            </div>\r\n                          </Col>\r\n\r\n                          <Col md={6} className=\"text-center\">\r\n                            <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                            {existingImageUrls && existingImageUrls.length > 0 ? (\r\n                              <div className=\"product-images-container\">\r\n                                <div className=\"product-images-grid\">\r\n                                  {existingImageUrls.map((imageUrl, index) => (\r\n                                    <div\r\n                                      key={index}\r\n                                      className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                      onClick={() => setActiveImageIndex(index)}\r\n                                    >\r\n                                      <Image\r\n                                        src={imageUrl}\r\n                                        alt={`Product ${index + 1}`}\r\n                                        thumbnail\r\n                                        className=\"image-preview\"\r\n                                      />\r\n                                      <span className=\"image-number\">{index + 1}</span>\r\n                                    </div>\r\n                                  ))}\r\n                                </div>\r\n                                <div className=\"main-image-container mt-3\">\r\n                                  <Image\r\n                                    src={existingImageUrls[activeImageIndex]}\r\n                                    alt=\"Product\"\r\n                                    thumbnail\r\n                                    className=\"main-image-preview\"\r\n                                    style={{ maxHeight: \"250px\" }}\r\n                                  />\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-5 bg-light rounded\">\r\n                                <FaImage size={60} className=\"text-secondary\" />\r\n                                <p className=\"mt-3 text-muted\">No images available</p>\r\n                              </div>\r\n                            )}\r\n                          </Col>\r\n                        </Row>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"slide-in\">\r\n                    <Tabs\r\n                      id=\"product-approval-tabs\"\r\n                      activeKey={activeTab}\r\n                      onSelect={(k) => setActiveTab(k)}\r\n                      className=\"mb-4\"\r\n                    >\r\n                      <Tab eventKey=\"details\" title={<span><FaInfoCircle className=\"me-2\" />Product Details</span>}>\r\n                        <Row className=\"mt-3\">\r\n                          <Col md={6}>\r\n                            <h5 className=\"mb-3\"><FaInfoCircle className=\"me-2\" />Product Name</h5>\r\n                            {isEditingName ? (\r\n                              <div className=\"mb-4\">\r\n                                <Form.Group>\r\n                                  <Form.Control\r\n                                    type=\"text\"\r\n                                    value={productName}\r\n                                    onChange={(e) => setProductName(e.target.value)}\r\n                                    isInvalid={!!productNameError}\r\n                                    placeholder=\"Enter product name\"\r\n                                  />\r\n                                  <Form.Control.Feedback type=\"invalid\">\r\n                                    {productNameError}\r\n                                  </Form.Control.Feedback>\r\n                                </Form.Group>\r\n                                <div className=\"mt-2\">\r\n                                  <Button\r\n                                    variant=\"success\"\r\n                                    size=\"sm\"\r\n                                    className=\"me-1\"\r\n                                    onClick={saveProductName}\r\n                                  >\r\n                                    <FaCheck className=\"me-1\" /> Save\r\n                                  </Button>\r\n                                  <Button\r\n                                    variant=\"secondary\"\r\n                                    size=\"sm\"\r\n                                    onClick={cancelEditingName}\r\n                                  >\r\n                                    <FaUndo className=\"me-1\" /> Cancel\r\n                                  </Button>\r\n                                </div>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"p-3 bg-light rounded mb-4 d-flex justify-content-between align-items-center\">\r\n                                <strong>\r\n                                  {productName || (productDetails && productDetails.fabric_definition_data ?\r\n                                    productDetails.fabric_definition_data.fabric_name :\r\n                                    `Batch ID: ${id}`)}\r\n                                </strong>\r\n                                <Button\r\n                                  variant=\"outline-primary\"\r\n                                  size=\"sm\"\r\n                                  onClick={startEditingName}\r\n                                >\r\n                                  Edit Name\r\n                                </Button>\r\n                              </div>\r\n                            )}\r\n\r\n                            <h5 className=\"mb-3\"><FaTags className=\"me-2\" />Colors</h5>\r\n                            <div className=\"mb-4\">\r\n                              {fabricDetails.length > 0 ? (\r\n                                fabricDetails.map((detail, index) => (\r\n                                  <div key={index} className=\"mb-2\">\r\n                                    {renderColorSwatch(detail.fabric_variant_data?.color || detail.color || 'gray')}\r\n                                    <span className=\"ms-2\">{detail.fabric_variant_data?.color_name || detail.color}</span>\r\n                                  </div>\r\n                                ))\r\n                              ) : (\r\n                                <p className=\"text-muted\">No color information available</p>\r\n                              )}\r\n                            </div>\r\n\r\n                            <h5 className=\"mb-3\"><FaBoxOpen className=\"me-2\" />Size Distribution</h5>\r\n                            {renderSizeQuantityBars()}\r\n                          </Col>\r\n\r\n                          <Col md={6}>\r\n                            <div className=\"mb-3\">\r\n                              <h5 className=\"mb-3\"><FaImage className=\"me-2\" />Product Images</h5>\r\n                              <p className=\"text-muted mb-3\">Upload up to 10 images of the product (Current: {productImages.length}/10)</p>\r\n\r\n                              {/* Image preview grid */}\r\n                              {imagePreviewUrls.length > 0 && (\r\n                                <div className=\"product-images-container mb-4\">\r\n                                  <div className=\"product-images-grid\">\r\n                                    {imagePreviewUrls.map((previewUrl, index) => (\r\n                                      <div\r\n                                        key={index}\r\n                                        className={`product-image-item ${index === activeImageIndex ? 'active' : ''}`}\r\n                                        onClick={() => setActiveImageIndex(index)}\r\n                                      >\r\n                                        <div className=\"image-actions\">\r\n                                          <Button\r\n                                            variant=\"danger\"\r\n                                            size=\"sm\"\r\n                                            className=\"btn-remove-image\"\r\n                                            onClick={(e) => {\r\n                                              e.stopPropagation();\r\n                                              removeImage(index);\r\n                                            }}\r\n                                          >\r\n                                            <FaTrash />\r\n                                          </Button>\r\n                                        </div>\r\n                                        <Image\r\n                                          src={previewUrl}\r\n                                          alt={`Preview ${index + 1}`}\r\n                                          thumbnail\r\n                                          className=\"image-preview\"\r\n                                        />\r\n                                        <span className=\"image-number\">{index + 1}</span>\r\n                                      </div>\r\n                                    ))}\r\n                                  </div>\r\n\r\n                                  {imagePreviewUrls.length > 0 && (\r\n                                    <div className=\"main-image-container mt-3\">\r\n                                      <Image\r\n                                        src={imagePreviewUrls[activeImageIndex]}\r\n                                        alt=\"Product Preview\"\r\n                                        thumbnail\r\n                                        className=\"main-image-preview\"\r\n                                        style={{ maxHeight: \"250px\" }}\r\n                                      />\r\n                                    </div>\r\n                                  )}\r\n                                </div>\r\n                              )}\r\n\r\n                              {/* Upload area */}\r\n                              {isUploading ? (\r\n                                <div className=\"p-4 bg-light rounded text-center\">\r\n                                  <h5 className=\"mb-3\">Uploading Images</h5>\r\n                                  <ProgressBar\r\n                                    now={uploadProgress}\r\n                                    label={`${Math.round(uploadProgress)}%`}\r\n                                    variant=\"info\"\r\n                                    animated\r\n                                    className=\"mb-3\"\r\n                                  />\r\n                                  <p className=\"text-muted\">\r\n                                    <FaUpload className=\"me-2 text-primary\" />\r\n                                    Uploading {productImages.length} images...\r\n                                  </p>\r\n                                </div>\r\n                              ) : (\r\n                                productImages.length < 10 && (\r\n                                  <div {...getRootProps()} className={`image-upload-container ${isDragActive ? 'active' : ''}`}>\r\n                                    <input {...getInputProps()} multiple />\r\n                                    <div className=\"text-center\">\r\n                                      <FaUpload size={40} className=\"mb-3 text-primary\" />\r\n                                      <p>Drag & drop product images here, or click to select</p>\r\n                                      <p className=\"text-muted small\">Supported formats: JPEG, PNG, GIF (Max: 5MB each)</p>\r\n                                      <p className=\"text-muted small\">You can select multiple images at once (Max: 10)</p>\r\n                                    </div>\r\n                                  </div>\r\n                                )\r\n                              )}\r\n\r\n                              <Form.Control\r\n                                type=\"file\"\r\n                                ref={fileInputRef}\r\n                                onChange={handleImageChange}\r\n                                accept=\"image/*\"\r\n                                multiple\r\n                                style={{ display: 'none' }}\r\n                              />\r\n                            </div>\r\n                          </Col>\r\n                        </Row>\r\n                      </Tab>\r\n\r\n                      <Tab eventKey=\"pricing\" title={<span><FaMoneyBillWave className=\"me-2\" />Pricing</span>}>\r\n                        <Form onSubmit={handleFormSubmit} className=\"mt-3\">\r\n                          <Row>\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Manufacture Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The cost to manufacture this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={manufacturePrice}\r\n                                  onChange={(e) => setManufacturePrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.manufacturePrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.manufacturePrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Selling Price (LKR):</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>The price at which this product will be sold</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  step=\"0.01\"\r\n                                  min=\"0\"\r\n                                  value={sellingPrice}\r\n                                  onChange={(e) => setSellingPrice(e.target.value)}\r\n                                  isInvalid={!!validationErrors.sellingPrice}\r\n                                  required\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.sellingPrice}\r\n                                </Form.Control.Feedback>\r\n                              </Form.Group>\r\n\r\n                              {manufacturePrice && sellingPrice && parseFloat(manufacturePrice) > 0 && parseFloat(sellingPrice) > 0 && (\r\n                                <div className=\"mb-4 p-3 bg-light rounded\">\r\n                                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                                    <span><strong>Profit Margin:</strong></span>\r\n                                    <Badge bg={\r\n                                      profitMargin < 10 ? \"danger\" :\r\n                                      profitMargin < 20 ? \"warning\" :\r\n                                      \"success\"\r\n                                    }>\r\n                                      <FaPercentage className=\"me-1\" />\r\n                                      {profitMargin}%\r\n                                    </Badge>\r\n                                  </div>\r\n                                  <div className=\"profit-margin-indicator\" />\r\n                                  <div className=\"d-flex justify-content-between mt-1\">\r\n                                    <small>Low</small>\r\n                                    <small>High</small>\r\n                                  </div>\r\n                                </div>\r\n                              )}\r\n                            </Col>\r\n\r\n                            <Col md={6}>\r\n                              <Form.Group className=\"mb-3\">\r\n                                <Form.Label>\r\n                                  <strong>Product Notes:</strong>\r\n                                  <OverlayTrigger\r\n                                    placement=\"top\"\r\n                                    overlay={<Tooltip>Additional information about this product</Tooltip>}\r\n                                  >\r\n                                    <FaInfoCircle className=\"ms-2 text-muted\" />\r\n                                  </OverlayTrigger>\r\n                                </Form.Label>\r\n                                <Form.Control\r\n                                  as=\"textarea\"\r\n                                  rows={5}\r\n                                  value={productNotes}\r\n                                  onChange={(e) => setProductNotes(e.target.value)}\r\n                                  isInvalid={!!validationErrors.productNotes}\r\n                                  placeholder=\"Enter any additional notes about this product...\"\r\n                                />\r\n                                <Form.Control.Feedback type=\"invalid\">\r\n                                  {validationErrors.productNotes}\r\n                                </Form.Control.Feedback>\r\n                                <Form.Text className=\"text-muted\">\r\n                                  {productNotes ? 500 - productNotes.length : 500} characters remaining\r\n                                </Form.Text>\r\n                              </Form.Group>\r\n                            </Col>\r\n                          </Row>\r\n\r\n                          <div className=\"d-grid gap-2 mt-4\">\r\n                            <Button type=\"submit\" className=\"btn-approve\" size=\"lg\">\r\n                              <FaCheck className=\"me-2\" />\r\n                              Approve Product\r\n                            </Button>\r\n                          </div>\r\n                        </Form>\r\n                      </Tab>\r\n                    </Tabs>\r\n                  </div>\r\n                )}\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n\r\n      {/* Confirmation Modal */}\r\n      <ConfirmationModal />\r\n\r\n      {/* PDF Report Modal */}\r\n      <Modal show={showPdfModal} onHide={closePdfModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"text-danger me-2\" />\r\n            Generate Product Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Are you sure you want to generate a PDF report for this product?</p>\r\n          <div className=\"bg-light p-3 rounded\">\r\n            <p className=\"mb-1\"><strong>Product:</strong> {productName || (productDetails && productDetails.fabric_definition_data ?\r\n              productDetails.fabric_definition_data.fabric_name :\r\n              `Batch ID: ${id}`)}</p>\r\n            <p className=\"mb-1\"><strong>Manufacture Price:</strong> LKR {manufacturePrice}</p>\r\n            <p className=\"mb-1\"><strong>Selling Price:</strong> LKR {sellingPrice}</p>\r\n            <p className=\"mb-0\"><strong>Profit Margin:</strong> {profitMargin}%</p>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closePdfModal}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={generateProductReport}\r\n            disabled={pdfLoading}\r\n          >\r\n            {pdfLoading ? (\r\n              <>\r\n                <Spinner\r\n                  as=\"span\"\r\n                  animation=\"border\"\r\n                  size=\"sm\"\r\n                  role=\"status\"\r\n                  aria-hidden=\"true\"\r\n                  className=\"me-2\"\r\n                />\r\n                Generating...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FaDownload className=\"me-2\" />\r\n                Generate PDF\r\n              </>\r\n            )}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ApproveFinishedProduct;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAEC,OAAO,QAAQ,iBAAiB;AAC3J,SACEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EACjEC,YAAY,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EACtDC,OAAO,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,UAAU,QACxD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAO,8BAA8B;AACrC,SAASC,KAAK,QAAQ,OAAO;AAC7B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAG,CAAC,GAAG1C,SAAS,CAAC,CAAC;EAC1B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,YAAY,GAAG/C,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACA,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC;IACnDoF,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1F,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC2G,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC+G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIiD,gBAAgB,IAAIE,YAAY,EAAE;MACpC,MAAMiE,MAAM,GAAGC,UAAU,CAACpE,gBAAgB,CAAC;MAC3C,MAAMqE,MAAM,GAAGD,UAAU,CAAClE,YAAY,CAAC;MAEvC,IAAIiE,MAAM,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAMC,MAAM,GAAI,CAACD,MAAM,GAAGF,MAAM,IAAIE,MAAM,GAAI,GAAG;QACjDnB,eAAe,CAACoB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;EACF,CAAC,EAAE,CAACvE,gBAAgB,EAAEE,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM4H,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAMC,gBAAgB,GAAG1H,WAAW,CAAC,YAAY;IAC/C,IAAI;MACF4D,UAAU,CAAC,IAAI,CAAC;MAChBN,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMqE,WAAW,GAAG,MAAM1H,KAAK,CAAC2H,GAAG,CAAC,qDAAqDhF,EAAE,GAAG,CAAC;;MAE/F;MACA,MAAMiF,UAAU,GAAG,MAAM5H,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;MAEtF,IAAI+E,WAAW,CAACG,IAAI,IAAIH,WAAW,CAACG,IAAI,CAACC,WAAW,EAAE;QACpDrE,aAAa,CAAC,IAAI,CAAC;QACnBV,mBAAmB,CAAC2E,WAAW,CAACG,IAAI,CAACE,iBAAiB,CAAC;QACvD9E,eAAe,CAACyE,WAAW,CAACG,IAAI,CAACG,aAAa,CAAC;QAC/ChB,oBAAoB,CAACU,WAAW,CAACG,IAAI,CAACI,mBAAmB,CAAC;;QAE1D;QACA,IAAIP,WAAW,CAACG,IAAI,CAACK,cAAc,IAAIC,KAAK,CAACC,OAAO,CAACV,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC,EAAE;UACrFjE,oBAAoB,CAACyD,WAAW,CAACG,IAAI,CAACK,cAAc,CAAC;QACvD,CAAC,MAAM,IAAIR,WAAW,CAACG,IAAI,CAACQ,aAAa,EAAE;UACzC;UACApE,oBAAoB,CAAC,CAACyD,WAAW,CAACG,IAAI,CAACQ,aAAa,CAAC,CAAC;QACxD;QAEA,IAAIX,WAAW,CAACG,IAAI,CAACS,KAAK,EAAE;UAC1BnF,eAAe,CAACuE,WAAW,CAACG,IAAI,CAACS,KAAK,CAAC;QACzC;MACF;MAEA,IAAIV,UAAU,CAACC,IAAI,EAAE;QACnBlD,iBAAiB,CAACiD,UAAU,CAACC,IAAI,CAAC;;QAElC;QACA,IAAID,UAAU,CAACC,IAAI,CAACU,YAAY,EAAE;UAChCrC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACU,YAAY,CAAC;QAC9C,CAAC,MAAM,IAAIX,UAAU,CAACC,IAAI,CAACW,sBAAsB,EAAE;UACjD;UACAtC,cAAc,CAAC0B,UAAU,CAACC,IAAI,CAACW,sBAAsB,CAACC,WAAW,CAAC;QACpE;;QAEA;QACA,IAAIb,UAAU,CAACC,IAAI,CAACa,OAAO,IAAId,UAAU,CAACC,IAAI,CAACa,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UACjE;UACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEjB,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC;UAEhE7D,gBAAgB,CAAC+C,UAAU,CAACC,IAAI,CAACa,OAAO,CAAC;;UAEzC;UACA,MAAMI,KAAK,GAAG;YAAC9D,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAC,CAAC;UAC9CwC,UAAU,CAACC,IAAI,CAACa,OAAO,CAACK,OAAO,CAACC,MAAM,IAAI;YACxCF,KAAK,CAAC9D,EAAE,IAAIgE,MAAM,CAAChE,EAAE,IAAI,CAAC;YAC1B8D,KAAK,CAAC7D,CAAC,IAAI+D,MAAM,CAAC/D,CAAC,IAAI,CAAC;YACxB6D,KAAK,CAAC5D,CAAC,IAAI8D,MAAM,CAAC9D,CAAC,IAAI,CAAC;YACxB4D,KAAK,CAAC3D,CAAC,IAAI6D,MAAM,CAAC7D,CAAC,IAAI,CAAC;YACxB2D,KAAK,CAAC1D,EAAE,IAAI4D,MAAM,CAAC5D,EAAE,IAAI,CAAC;UAC5B,CAAC,CAAC;UACFL,iBAAiB,CAAC+D,KAAK,CAAC;QAC1B;MACF;;MAEA;MACAvB,aAAa,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,+BAA+B,EAAE6F,GAAG,CAAC;;MAEnD;MACA,MAAMC,YAAY,GAAGD,GAAG,CAACE,QAAQ,GAC7B,UAAUF,GAAG,CAACE,QAAQ,CAACC,MAAM,MAAMH,GAAG,CAACE,QAAQ,CAACE,UAAU,EAAE,GAC5DJ,GAAG,CAACK,OAAO,GACT,oEAAoE,GACpE,wDAAwD;MAE9DjG,QAAQ,CAAC,iCAAiC6F,YAAY,EAAE,CAAC;;MAEzD;MACA,IAAI5B,UAAU,GAAGE,UAAU,EAAE;QAC3BD,aAAa,CAACgC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BC,UAAU,CAAC,MAAM;UACf/B,gBAAgB,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,SAAS;MACR9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAAChB,EAAE,EAAE2E,UAAU,CAAC,CAAC;;EAEpB;EACAzH,SAAS,CAAC,MAAM;IACd4H,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMgC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,IAAI,CAACF,CAAC,CAACG,MAAM,CAACF,KAAK,CAAC;IACxC,IAAIA,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAE;MACpBmB,iBAAiB,CAACH,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAG/J,WAAW,CAAE4J,KAAK,IAAK;IAC/C;IACA,IAAI/F,aAAa,CAAC+E,MAAM,GAAGgB,KAAK,CAAChB,MAAM,GAAG,EAAE,EAAE;MAC5CtF,QAAQ,CAAC,yDAAyDO,aAAa,CAAC+E,MAAM,UAAU,CAAC;MACjG;IACF;IAEA,MAAMoB,SAAS,GAAG,EAAE;IACpB,MAAMC,cAAc,GAAG,CAAC,GAAGlG,gBAAgB,CAAC;IAE5C6F,KAAK,CAACZ,OAAO,CAACkB,IAAI,IAAI;MACpB;MACA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACC,KAAK,CAAC,SAAS,CAAC,EAAE;QAC/B9G,QAAQ,CAAC,kDAAkD,CAAC;QAC5D;MACF;;MAEA;MACA,IAAI4G,IAAI,CAACG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B/G,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA0G,SAAS,CAACM,IAAI,CAACJ,IAAI,CAAC;;MAEpB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBR,cAAc,CAACK,IAAI,CAACC,MAAM,CAACG,MAAM,CAAC;QAClC1G,mBAAmB,CAAC,CAAC,GAAGiG,cAAc,CAAC,CAAC;MAC1C,CAAC;MACDM,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEFpG,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,GAAGmG,SAAS,CAAC,CAAC;EACpD,CAAC,EAAE,CAACnG,aAAa,EAAEE,gBAAgB,CAAC,CAAC;;EAErC;EACA,MAAM6G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9H,YAAY,CAAC+H,OAAO,CAACC,KAAK,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,MAAM,GAAG/K,WAAW,CAAEgL,aAAa,IAAK;IAC5C,IAAIA,aAAa,IAAIA,aAAa,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC7CmB,iBAAiB,CAACiB,aAAa,CAAC;IAClC;EACF,CAAC,EAAE,CAACjB,iBAAiB,CAAC,CAAC;EAEvB,MAAM;IAAEkB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGhJ,WAAW,CAAC;IAChE4I,MAAM;IACNK,MAAM,EAAE;MACN,SAAS,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC7C,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,MAAMvB,SAAS,GAAG,CAAC,GAAGnG,aAAa,CAAC;IACpC,MAAMoG,cAAc,GAAG,CAAC,GAAGlG,gBAAgB,CAAC;IAE5CiG,SAAS,CAACwB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC1BtB,cAAc,CAACuB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAE/BzH,gBAAgB,CAACkG,SAAS,CAAC;IAC3BhG,mBAAmB,CAACiG,cAAc,CAAC;;IAEnC;IACA,IAAIsB,KAAK,KAAKlH,gBAAgB,EAAE;MAC9BC,mBAAmB,CAACmH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIA,KAAK,GAAGlH,gBAAgB,EAAE;MACnCC,mBAAmB,CAACD,gBAAgB,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMsH,cAAc,GAAIJ,KAAK,IAAK;IAChCjH,mBAAmB,CAACiH,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC9I,gBAAgB,IAAIA,gBAAgB,CAAC+I,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvDD,MAAM,CAAC9I,gBAAgB,GAAG,+BAA+B;IAC3D,CAAC,MAAM,IAAIoE,UAAU,CAACpE,gBAAgB,CAAC,IAAI,CAAC,EAAE;MAC5C8I,MAAM,CAAC9I,gBAAgB,GAAG,6CAA6C;IACzE,CAAC,MAAM,IAAIgJ,KAAK,CAAC5E,UAAU,CAACpE,gBAAgB,CAAC,CAAC,EAAE;MAC9C8I,MAAM,CAAC9I,gBAAgB,GAAG,0CAA0C;IACtE;;IAEA;IACA,IAAI,CAACE,YAAY,IAAIA,YAAY,CAAC6I,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/CD,MAAM,CAAC5I,YAAY,GAAG,2BAA2B;IACnD,CAAC,MAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,IAAI,CAAC,EAAE;MACxC4I,MAAM,CAAC5I,YAAY,GAAG,yCAAyC;IACjE,CAAC,MAAM,IAAI8I,KAAK,CAAC5E,UAAU,CAAClE,YAAY,CAAC,CAAC,EAAE;MAC1C4I,MAAM,CAAC5I,YAAY,GAAG,sCAAsC;IAC9D,CAAC,MAAM,IAAIkE,UAAU,CAAClE,YAAY,CAAC,GAAGkE,UAAU,CAACpE,gBAAgB,CAAC,EAAE;MAClE8I,MAAM,CAAC5I,YAAY,GAAG,oEAAoE;IAC5F;;IAEA;IACA,IAAIE,YAAY,IAAIA,YAAY,CAACyF,MAAM,GAAG,GAAG,EAAE;MAC7CiD,MAAM,CAAC1I,YAAY,GAAG,0CAA0C;IAClE;IAEA4C,mBAAmB,CAAC8F,MAAM,CAAC;IAC3B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAIvC,CAAC,IAAK;IAC9BA,CAAC,CAACwC,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACAnG,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM2G,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B9I,QAAQ,CAAC,EAAE,CAAC;IACZE,aAAa,CAAC,EAAE,CAAC;IACjBiC,mBAAmB,CAAC,KAAK,CAAC;IAE1B,IAAI;MACF;MACA7B,UAAU,CAAC,IAAI,CAAC;MAChBc,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM2H,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAE3J,EAAE,CAAC;MACrCyJ,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEpF,UAAU,CAACpE,gBAAgB,CAAC,CAAC;MAClEsJ,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEpF,UAAU,CAAClE,YAAY,CAAC,CAAC;MAE1D,IAAIE,YAAY,EAAE;QAChBkJ,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEpJ,YAAY,CAAC;MACxC;;MAEA;MACA,IAAIU,aAAa,IAAIA,aAAa,CAAC+E,MAAM,GAAG,CAAC,EAAE;QAC7CpE,iBAAiB,CAAC,CAAC,CAAC;;QAEpB;QACAX,aAAa,CAACmF,OAAO,CAACwD,KAAK,IAAI;UAC7BH,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAEC,KAAK,CAAC;QAC1C,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMpD,QAAQ,GAAG,MAAMnJ,KAAK,CAACwM,IAAI,CAC/B,qDAAqD,EACrDJ,QAAQ,EACR;QACEK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,gBAAgB,EAAGC,aAAa,IAAK;UACnC,MAAMC,gBAAgB,GAAGpB,IAAI,CAACqB,KAAK,CAChCF,aAAa,CAACG,MAAM,GAAG,GAAG,GAAIH,aAAa,CAACI,KAC/C,CAAC;UACDxI,iBAAiB,CAACqI,gBAAgB,CAAC;QACrC;MACF,CACF,CAAC;MAEDrJ,aAAa,CAAC4F,QAAQ,CAACtB,IAAI,CAACmF,OAAO,IAAI,gCAAgC,CAAC;MACxEvI,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA+E,UAAU,CAAC,MAAM;QACf5G,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOqG,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,mCAAmC,EAAE6F,GAAG,CAAC;MACvD,MAAMgE,MAAM,GAAGhE,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAC5C,OAAOoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,KAAK,QAAQ,GACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,GACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GACnB,uDAAuD;MAC3DxE,QAAQ,CAAC4J,MAAM,CAAC;MAChBxI,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyJ,wBAAwB,GAAGA,CAAA,KAAM;IACrC5H,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM6H,YAAY,GAAGA,CAAA,KAAM;IACzB3H,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM4H,aAAa,GAAGA,CAAA,KAAM;IAC1B5H,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM6H,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnH,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMoH,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpH,gBAAgB,CAAC,KAAK,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC;IACvB;IACA,IAAI5B,cAAc,IAAIA,cAAc,CAAC6D,YAAY,EAAE;MACjDrC,cAAc,CAACxB,cAAc,CAAC6D,YAAY,CAAC;IAC7C,CAAC,MAAM,IAAI7D,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,EAAE;MAClEtC,cAAc,CAACxB,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,CAAC;IACnE;EACF,CAAC;;EAED;EACA,MAAMgF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC;IACA,IAAI,CAACxH,WAAW,CAAC4F,IAAI,CAAC,CAAC,EAAE;MACvBvF,mBAAmB,CAAC,8BAA8B,CAAC;MACnD;IACF;IAEAA,mBAAmB,CAAC,EAAE,CAAC;IACvB3C,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM+J,aAAa,GAAG,MAAM1N,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;;MAEzF;MACA,MAAMgL,OAAO,GAAG;QACdC,iBAAiB,EAAEF,aAAa,CAAC7F,IAAI,CAAC+F,iBAAiB;QACvDC,YAAY,EAAEH,aAAa,CAAC7F,IAAI,CAACgG,YAAY;QAC7CtF,YAAY,EAAEtC,WAAW;QACzByC,OAAO,EAAEgF,aAAa,CAAC7F,IAAI,CAACa,OAAO,CAAE;MACvC,CAAC;;MAED;MACA,MAAM1I,KAAK,CAAC8N,GAAG,CAAC,qDAAqDnL,EAAE,GAAG,EAAEgL,OAAO,CAAC;;MAEpF;MACAhJ,iBAAiB,CAAC;QAChB,GAAGD,cAAc;QACjB6D,YAAY,EAAEtC;MAChB,CAAC,CAAC;MAEFG,gBAAgB,CAAC,KAAK,CAAC;MACvB7C,aAAa,CAAC,mCAAmC,CAAC;;MAElD;MACAiG,UAAU,CAAC,MAAM;QACfjG,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO0F,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,8BAA8B,EAAE6F,GAAG,CAAC;;MAElD;MACA,IAAI;QACF,MAAM8E,aAAa,GAAG,MAAM/N,KAAK,CAAC2H,GAAG,CAAC,6CAA6ChF,EAAE,GAAG,CAAC;QACzF,IAAIoL,aAAa,CAAClG,IAAI,CAACU,YAAY,KAAKtC,WAAW,EAAE;UACnD;UACAtB,iBAAiB,CAAC;YAChB,GAAGD,cAAc;YACjB6D,YAAY,EAAEtC;UAChB,CAAC,CAAC;UACFG,gBAAgB,CAAC,KAAK,CAAC;UACvB7C,aAAa,CAAC,mCAAmC,CAAC;;UAElD;UACAiG,UAAU,CAAC,MAAM;YACfjG,aAAa,CAAC,EAAE,CAAC;UACnB,CAAC,EAAE,IAAI,CAAC;UACR;QACF;MACF,CAAC,CAAC,OAAOyK,QAAQ,EAAE;QACjB;QACApF,OAAO,CAACxF,KAAK,CAAC,qCAAqC,EAAE4K,QAAQ,CAAC;MAChE;;MAEA;MACA3K,QAAQ,CAAC,kDAAkD,CAAC;IAC9D,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvH,uBAAuB,CAAC5D,gBAAgB,CAAC;IACzC8D,mBAAmB,CAAC5D,YAAY,CAAC;IACjCwD,kBAAkB,CAAC,IAAI,CAAC;IACxBM,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMoH,mBAAmB,GAAGA,CAAA,KAAM;IAChC1H,kBAAkB,CAAC,KAAK,CAAC;IACzBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,mBAAmB,CAAC,EAAE,CAAC;IACvBE,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMvC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,MAAMwC,cAAc,GAAGC,MAAM,CAAC5H,oBAAoB,IAAI,EAAE,CAAC,CAACoF,IAAI,CAAC,CAAC;IAChE,IAAI,CAACpF,oBAAoB,IAAI2H,cAAc,KAAK,EAAE,EAAE;MAClDxC,MAAM,CAACnF,oBAAoB,GAAG,+BAA+B;IAC/D,CAAC,MAAM,IAAIS,UAAU,CAACT,oBAAoB,CAAC,IAAI,CAAC,EAAE;MAChDmF,MAAM,CAACnF,oBAAoB,GAAG,6CAA6C;IAC7E,CAAC,MAAM,IAAIqF,KAAK,CAAC5E,UAAU,CAACT,oBAAoB,CAAC,CAAC,EAAE;MAClDmF,MAAM,CAACnF,oBAAoB,GAAG,0CAA0C;IAC1E;;IAEA;IACA,MAAM6H,UAAU,GAAGD,MAAM,CAAC1H,gBAAgB,IAAI,EAAE,CAAC,CAACkF,IAAI,CAAC,CAAC;IACxD,IAAI,CAAClF,gBAAgB,IAAI2H,UAAU,KAAK,EAAE,EAAE;MAC1C1C,MAAM,CAACjF,gBAAgB,GAAG,2BAA2B;IACvD,CAAC,MAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,IAAI,CAAC,EAAE;MAC5CiF,MAAM,CAACjF,gBAAgB,GAAG,yCAAyC;IACrE,CAAC,MAAM,IAAImF,KAAK,CAAC5E,UAAU,CAACP,gBAAgB,CAAC,CAAC,EAAE;MAC9CiF,MAAM,CAACjF,gBAAgB,GAAG,sCAAsC;IAClE,CAAC,MAAM,IAAIO,UAAU,CAACP,gBAAgB,CAAC,GAAGO,UAAU,CAACT,oBAAoB,CAAC,EAAE;MAC1EmF,MAAM,CAACjF,gBAAgB,GAAG,oEAAoE;IAChG;IAEAG,kBAAkB,CAAC8E,MAAM,CAAC;IAC1B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACjD,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAM4F,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC3F,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpC,oBAAoB,CAAC;IAC1DmC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAElC,gBAAgB,CAAC;IAClDiC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE9B,iBAAiB,CAAC;IACpD6B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEnF,OAAO,CAAC;IAEtC,MAAM8K,gBAAgB,GAAGL,iBAAiB,CAAC,CAAC;IAC5CvF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2F,gBAAgB,CAAC;IACnD,IAAI,CAACA,gBAAgB,EAAE;MACrB5F,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;IACF;IAEA,IAAI,CAAC9B,iBAAiB,EAAE;MACtB6B,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DxF,QAAQ,CAAC,wCAAwC,CAAC;MAClD;IACF;IAEAuF,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAE7DlF,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMoL,UAAU,GAAG;QACjB1G,iBAAiB,EAAEb,UAAU,CAACT,oBAAoB,CAAC;QACnDuB,aAAa,EAAEd,UAAU,CAACP,gBAAgB;MAC5C,CAAC;MAEDiC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4F,UAAU,CAAC;MAC/C,MAAMzO,KAAK,CAAC0O,KAAK,CAAC,qDAAqD3H,iBAAiB,GAAG,EAAE0H,UAAU,CAAC;;MAExG;MACA1L,mBAAmB,CAAC0D,oBAAoB,CAAC;MACzCxD,eAAe,CAAC0D,gBAAgB,CAAC;MACjCH,kBAAkB,CAAC,KAAK,CAAC;MACzBjD,aAAa,CAAC,6BAA6B,CAAC;;MAE5C;MACAiG,UAAU,CAAC,MAAM;QACfjG,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO0F,GAAG,EAAE;MACZL,OAAO,CAACxF,KAAK,CAAC,wBAAwB,EAAE6F,GAAG,CAAC;MAC5C,MAAMgE,MAAM,GAAGhE,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACtB,IAAI,GAC5C,OAAOoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,KAAK,QAAQ,GACnCqF,IAAI,CAACC,SAAS,CAAClE,GAAG,CAACE,QAAQ,CAACtB,IAAI,CAAC,GACjCoB,GAAG,CAACE,QAAQ,CAACtB,IAAI,GACnB,4CAA4C;MAChDxE,QAAQ,CAAC4J,MAAM,CAAC;IAClB,CAAC,SAAS;MACRtJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgL,qBAAqB,GAAGA,CAAA,KAAM;IAClC/I,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF;MACA,MAAMgJ,GAAG,GAAG,IAAIxM,KAAK,CAAC;QACpByM,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC;MAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAExD;MACA,MAAMC,cAAc,GAAGvJ,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GAC1F9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE,CAAC;MACtBiM,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACU,IAAI,CAACE,cAAc,EAAE,GAAG,EAAE,EAAE,EAAE;QAAED,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEtD;MACAX,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,kBAAkB,IAAIG,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEH,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE3F;MACAX,GAAG,CAACe,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/Bf,GAAG,CAACgB,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;;MAEzB;MACA,IAAIC,IAAI,GAAG,EAAE;;MAEb;MACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAEO,IAAI,CAAC;MACzCA,IAAI,IAAI,EAAE;MAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,0BAA0BxM,gBAAgB,EAAE,EAAE,EAAE,EAAE+M,IAAI,CAAC;MAChEA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACU,IAAI,CAAC,sBAAsBtM,YAAY,EAAE,EAAE,EAAE,EAAE6M,IAAI,CAAC;MACxDA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACU,IAAI,CAAC,kBAAkBvJ,YAAY,GAAG,EAAE,EAAE,EAAE8J,IAAI,CAAC;MACrDA,IAAI,IAAI,EAAE;;MAEV;MACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAEO,IAAI,CAAC;MACvCA,IAAI,IAAI,EAAE;MAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACA,MAAMvG,KAAK,GAAGiD,MAAM,CAAC+D,OAAO,CAAChL,cAAc,CAAC;MAC5C,MAAMiL,WAAW,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC;MACtD,MAAMC,aAAa,GAAGlH,KAAK,CAACmH,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,CAAC,EAAEC,GAAG,CAAC,KAAKF,GAAG,GAAGE,GAAG,EAAE,CAAC,CAAC;;MAEnE;MACAxB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCjB,GAAG,CAACU,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEF,IAAI,CAAC;MAClCA,IAAI,IAAI,CAAC;;MAET;MACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCvG,KAAK,CAACC,OAAO,CAAC,CAAC,CAACqB,IAAI,EAAEiG,QAAQ,CAAC,KAAK;QAClC,MAAMC,UAAU,GAAGN,aAAa,GAAG,CAAC,GAAG,CAAEK,QAAQ,GAAGL,aAAa,GAAI,GAAG,EAAE3I,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;QAC5FuH,GAAG,CAACU,IAAI,CAAClF,IAAI,CAACmG,WAAW,CAAC,CAAC,EAAE,EAAE,EAAEV,IAAI,CAAC;QACtCjB,GAAG,CAACU,IAAI,CAACe,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAEX,IAAI,CAAC;QACvCjB,GAAG,CAACU,IAAI,CAAC,GAAGgB,UAAU,GAAG,EAAE,EAAE,EAAET,IAAI,CAAC;QACpCA,IAAI,IAAI,CAAC;MACX,CAAC,CAAC;;MAEF;MACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,EAAE,EAAE,EAAEO,IAAI,CAAC;MAC3BjB,GAAG,CAACU,IAAI,CAACU,aAAa,CAACQ,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAEX,IAAI,CAAC;MAC5CjB,GAAG,CAACU,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAEO,IAAI,CAAC;MAC5BA,IAAI,IAAI,EAAE;;MAEV;MACA,IAAIjL,aAAa,CAAC+D,MAAM,GAAG,CAAC,EAAE;QAC5BiG,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAEO,IAAI,CAAC;QACvCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAElCzK,aAAa,CAACmE,OAAO,CAAC,CAACC,MAAM,EAAEsC,KAAK,KAAK;UAAA,IAAAmF,qBAAA;UACvC,MAAMC,SAAS,GAAG,EAAAD,qBAAA,GAAAzH,MAAM,CAAC2H,mBAAmB,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,UAAU,KAAI5H,MAAM,CAAC6H,KAAK,IAAI,KAAK;UACjFjC,GAAG,CAACU,IAAI,CAAC,SAAShE,KAAK,GAAG,CAAC,KAAKoF,SAAS,EAAE,EAAE,EAAE,EAAEb,IAAI,CAAC;UACtDA,IAAI,IAAI,CAAC;QACX,CAAC,CAAC;QAEFA,IAAI,IAAI,CAAC;MACX;;MAEA;MACA,IAAI3M,YAAY,EAAE;QAChB0L,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,eAAe,EAAE,EAAE,EAAEO,IAAI,CAAC;QACnCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;QAElC;QACA,MAAMyB,UAAU,GAAGlC,GAAG,CAACmC,eAAe,CAAC7N,YAAY,EAAE,GAAG,CAAC;QACzD0L,GAAG,CAACU,IAAI,CAACwB,UAAU,EAAE,EAAE,EAAEjB,IAAI,CAAC;QAC9BA,IAAI,IAAIiB,UAAU,CAACnI,MAAM,GAAG,CAAC,GAAG,CAAC;MACnC;;MAEA;MACA,IAAI3E,iBAAiB,IAAIA,iBAAiB,CAAC2E,MAAM,GAAG,CAAC,EAAE;QACrDiG,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;QAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEO,IAAI,CAAC;QACpCA,IAAI,IAAI,EAAE;QAEVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;QAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCT,GAAG,CAACU,IAAI,CAAC,qBAAqBtL,iBAAiB,CAAC2E,MAAM,EAAE,EAAE,EAAE,EAAEkH,IAAI,CAAC;QACnEA,IAAI,IAAI,CAAC;QACTjB,GAAG,CAACU,IAAI,CAAC,0CAA0C,EAAE,EAAE,EAAEO,IAAI,CAAC;QAC9DA,IAAI,IAAI,EAAE;MACZ;;MAEA;MACAjB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC;MAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,IAAIG,IAAI,CAAC,CAAC,CAACuB,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEzB,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFX,GAAG,CAACU,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAEhF;MACA,MAAM0B,gBAAgB,GAAGhL,WAAW,CAACiL,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAClEtC,GAAG,CAACuC,IAAI,CAAC,kBAAkBF,gBAAgB,IAAI,IAAIxB,IAAI,CAAC,CAAC,CAAC2B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;MAE3FzL,aAAa,CAAC,KAAK,CAAC;MACpBF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2BAA2BD,KAAK,CAAC4J,OAAO,EAAE,CAAC;MACpDpH,aAAa,CAAC,KAAK,CAAC;MACpBF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,IAAIhC,OAAO,EAAE,oBACXpB,OAAA;IAAKgP,SAAS,EAAC,kDAAkD;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,eAC3FnP,OAAA,CAAC7B,OAAO;MAACiR,SAAS,EAAC,QAAQ;MAACC,IAAI,EAAC,QAAQ;MAACC,OAAO,EAAC,SAAS;MAAAH,QAAA,eACzDnP,OAAA;QAAMgP,SAAS,EAAC,iBAAiB;QAAAG,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;;EAGR;EACA,MAAMC,iBAAiB,GAAIpB,KAAK,IAAK;IACnC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;;IAEvB;IACAjI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgI,KAAK,CAAC;;IAE3C;IACA,IAAIqB,OAAO;;IAEX;IACA,IAAIrB,KAAK,CAACsB,UAAU,CAAC,GAAG,CAAC,EAAE;MACzBD,OAAO,GAAGrB,KAAK;IACjB;IACA;IAAA,KACK,IAAI,kBAAkB,CAACuB,IAAI,CAACvB,KAAK,CAAC,IAAI,kBAAkB,CAACuB,IAAI,CAACvB,KAAK,CAAC,EAAE;MACzEqB,OAAO,GAAG,IAAIrB,KAAK,EAAE;IACvB;IACA;IAAA,KACK;MACH;MACA,MAAMwB,QAAQ,GAAG;QACf,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACV,CAAC;;MAED;MACAH,OAAO,GAAGG,QAAQ,CAACxB,KAAK,CAACyB,WAAW,CAAC,CAAC,CAAC,IAAIzB,KAAK;IAClD;IAEA,oBACEvO,OAAA,CAACrB,cAAc;MACbsR,SAAS,EAAC,KAAK;MACfC,OAAO,eAAElQ,OAAA,CAACpB,OAAO;QAACoQ,SAAS,EAAC,gBAAgB;QAAAG,QAAA,EAAEZ;MAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAE;MAAAP,QAAA,eAE/DnP,OAAA;QACEgP,SAAS,EAAC,cAAc;QACxBC,KAAK,EAAE;UAAEkB,eAAe,EAAEP;QAAQ,CAAE;QACpC,cAAYrB;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAErB,CAAC;;EAED;EACA,MAAMU,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAM5J,KAAK,GAAGiD,MAAM,CAAC+D,OAAO,CAAChL,cAAc,CAAC;IAC5C,MAAM6N,WAAW,GAAGnH,IAAI,CAACC,GAAG,CAAC,GAAG3C,KAAK,CAAC8J,GAAG,CAAC,CAAC,CAACzC,CAAC,EAAEC,GAAG,CAAC,KAAKA,GAAG,CAAC,CAAC;IAE7D,OAAOtH,KAAK,CAAC8J,GAAG,CAAC,CAAC,CAACxI,IAAI,EAAEiG,QAAQ,CAAC,kBAChC/N,OAAA;MAAgBgP,SAAS,EAAC,MAAM;MAAAG,QAAA,gBAC9BnP,OAAA;QAAKgP,SAAS,EAAC,qCAAqC;QAAAG,QAAA,gBAClDnP,OAAA;UAAAmP,QAAA,eAAMnP,OAAA;YAAAmP,QAAA,EAASrH,IAAI,CAACmG,WAAW,CAAC;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClD1P,OAAA;UAAAmP,QAAA,GAAOpB,QAAQ,EAAC,MAAI;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACN1P,OAAA,CAAC1B,WAAW;QACViS,GAAG,EAAEF,WAAW,GAAItC,QAAQ,GAAGsC,WAAW,GAAI,GAAG,GAAG,CAAE;QACtDf,OAAO,EAAEvB,QAAQ,GAAG,CAAC,GAAG,MAAM,GAAG,OAAQ;QACzCiB,SAAS,EAAC;MAAmB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA,GATM5H,IAAI;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUT,CACN,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAAA,kBACxBxQ,OAAA,CAAC3B,KAAK;IACJoS,IAAI,EAAExN,gBAAiB;IACvByN,MAAM,EAAE5F,wBAAyB;IACjC6F,QAAQ;IACR3B,SAAS,EAAC,oBAAoB;IAAAG,QAAA,gBAE9BnP,OAAA,CAAC3B,KAAK,CAACuS,MAAM;MAACC,WAAW;MAAA1B,QAAA,eACvBnP,OAAA,CAAC3B,KAAK,CAACyS,KAAK;QAAA3B,QAAA,EAAC;MAAwB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACf1P,OAAA,CAAC3B,KAAK,CAAC0S,IAAI;MAAA5B,QAAA,gBACTnP,OAAA;QAAAmP,QAAA,EAAG;MAAyE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChF1P,OAAA,CAACtB,KAAK;QAACsS,QAAQ;QAACC,KAAK;QAACnJ,IAAI,EAAC,IAAI;QAACkH,SAAS,EAAC,MAAM;QAAAG,QAAA,eAC9CnP,OAAA;UAAAmP,QAAA,gBACEnP,OAAA;YAAAmP,QAAA,gBACEnP,OAAA;cAAAmP,QAAA,eAAInP,OAAA;gBAAAmP,QAAA,EAAQ;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvC1P,OAAA;cAAAmP,QAAA,EAAKxL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GAC1E9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;YAAC;cAAAkP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACL1P,OAAA;YAAAmP,QAAA,gBACEnP,OAAA;cAAAmP,QAAA,eAAInP,OAAA;gBAAAmP,QAAA,EAAQ;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C1P,OAAA;cAAAmP,QAAA,GAAI,MAAI,EAAC3O,gBAAgB;YAAA;cAAA+O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACL1P,OAAA;YAAAmP,QAAA,gBACEnP,OAAA;cAAAmP,QAAA,eAAInP,OAAA;gBAAAmP,QAAA,EAAQ;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC1P,OAAA;cAAAmP,QAAA,GAAI,MAAI,EAACzO,YAAY;YAAA;cAAA6O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACL1P,OAAA;YAAAmP,QAAA,gBACEnP,OAAA;cAAAmP,QAAA,eAAInP,OAAA;gBAAAmP,QAAA,EAAQ;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC1P,OAAA;cAAAmP,QAAA,GAAK1L,YAAY,EAAC,GAAC;YAAA;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,EACJ9O,YAAY,iBACXZ,OAAA;YAAAmP,QAAA,gBACEnP,OAAA;cAAAmP,QAAA,eAAInP,OAAA;gBAAAmP,QAAA,EAAQ;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC1P,OAAA;cAAAmP,QAAA,EAAKvO;YAAY;cAAA2O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACPlO,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;QAAKgP,SAAS,EAAC,kBAAkB;QAAAG,QAAA,gBAC/BnP,OAAA;UAAAmP,QAAA,eAAGnP,OAAA;YAAAmP,QAAA,EAAQ;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtC1P,OAAA,CAAC5B,KAAK;UACJ8S,GAAG,EAAE1P,gBAAgB,CAAC,CAAC,CAAE;UACzB2P,GAAG,EAAC,SAAS;UACbC,SAAS;UACTnC,KAAK,EAAE;YAAEoC,SAAS,EAAE;UAAQ;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eACb1P,OAAA,CAAC3B,KAAK,CAACiT,MAAM;MAAAnC,QAAA,gBACXnP,OAAA,CAACjC,MAAM;QAACuR,OAAO,EAAC,WAAW;QAACiC,OAAO,EAAEzG,wBAAyB;QAAAqE,QAAA,EAAC;MAE/D;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1P,OAAA,CAACjC,MAAM;QAACuR,OAAO,EAAC,SAAS;QAACiC,OAAO,EAAE1H,YAAa;QAAAsF,QAAA,gBAC9CnP,OAAA,CAACnB,OAAO;UAACmQ,SAAS,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE9B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACR;EAED,oBACE1P,OAAA,CAAAE,SAAA;IAAAiP,QAAA,gBACEnP,OAAA,CAACH,eAAe;MAAA0P,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnB1P,OAAA;MAAKgP,SAAS,EAAC,cAAc;MAAAG,QAAA,eAC3BnP,OAAA,CAAC/B,GAAG;QAAC+Q,SAAS,EAAC,wBAAwB;QAAAG,QAAA,eACrCnP,OAAA,CAAC9B,GAAG;UAACsT,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eACjBnP,OAAA,CAACnC,IAAI;YAACmR,SAAS,EAAC,8BAA8B;YAACC,KAAK,EAAE;cAAEkB,eAAe,EAAE,SAAS;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAAvC,QAAA,eACzGnP,OAAA,CAACnC,IAAI,CAACkT,IAAI;cAAA5B,QAAA,gBACRnP,OAAA;gBAAIgP,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,EAAC;cAAwB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAG9D1P,OAAA;gBAAKgP,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,EAC9BtL,aAAa,gBACZ7D,OAAA;kBAAKgP,SAAS,EAAC,kDAAkD;kBAAAG,QAAA,gBAC/DnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;oBAAC3C,SAAS,EAAC,uBAAuB;oBAACC,KAAK,EAAE;sBAAE2C,QAAQ,EAAE;oBAAQ,CAAE;oBAAAzC,QAAA,gBACzEnP,OAAA,CAAClC,IAAI,CAAC+T,OAAO;sBACXjK,IAAI,EAAC,MAAM;sBACXkK,KAAK,EAAEnO,WAAY;sBACnBoO,QAAQ,EAAG3K,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;sBAChDE,SAAS,EAAE,CAAC,CAACjO,gBAAiB;sBAC9BkO,WAAW,EAAC;oBAAoB;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;sBAACtK,IAAI,EAAC,SAAS;sBAAAuH,QAAA,EAClCpL;oBAAgB;sBAAAwL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACb1P,OAAA,CAACjC,MAAM;oBACLuR,OAAO,EAAC,SAAS;oBACjBxH,IAAI,EAAC,IAAI;oBACTkH,SAAS,EAAC,MAAM;oBAChBuC,OAAO,EAAEpG,eAAgB;oBAAAgE,QAAA,eAEzBnP,OAAA,CAACnB,OAAO;sBAAA0Q,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACT1P,OAAA,CAACjC,MAAM;oBACLuR,OAAO,EAAC,WAAW;oBACnBxH,IAAI,EAAC,IAAI;oBACTyJ,OAAO,EAAErG,iBAAkB;oBAAAiE,QAAA,eAE3BnP,OAAA,CAACR,MAAM;sBAAA+P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN1P,OAAA;kBAAKgP,SAAS,EAAC,kDAAkD;kBAAAG,QAAA,gBAC/DnP,OAAA;oBAAGgP,SAAS,EAAC,kCAAkC;oBAAAG,QAAA,EAC5CxL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;kBAAC;oBAAAkP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACJ1P,OAAA,CAACjC,MAAM;oBACLuR,OAAO,EAAC,iBAAiB;oBACzBxH,IAAI,EAAC,IAAI;oBACTyJ,OAAO,EAAEtG,gBAAiB;oBAAAkE,QAAA,EAC3B;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL5O,KAAK,iBACJd,OAAA,CAAChC,KAAK;gBAACsR,OAAO,EAAC,QAAQ;gBAACN,SAAS,EAAC,cAAc;gBAAAG,QAAA,eAC9CnP,OAAA;kBAAKgP,SAAS,EAAC,mDAAmD;kBAAAG,QAAA,gBAChEnP,OAAA;oBAAAmP,QAAA,gBACEnP,OAAA,CAACP,qBAAqB;sBAACuP,SAAS,EAAC;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzC5O,KAAK;kBAAA;oBAAAyO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1P,OAAA,CAACjC,MAAM;oBACLuR,OAAO,EAAC,gBAAgB;oBACxBxH,IAAI,EAAC,IAAI;oBACTyJ,OAAO,EAAEpM,gBAAiB;oBAAAgK,QAAA,EAC3B;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,EAEA1O,UAAU,iBACThB,OAAA,CAAChC,KAAK;gBAACsR,OAAO,EAAC,SAAS;gBAACN,SAAS,EAAC,cAAc;gBAAAG,QAAA,gBAC/CnP,OAAA,CAACnB,OAAO;kBAACmQ,SAAS,EAAC;gBAAM;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC3B1O,UAAU;cAAA;gBAAAuO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACR,EAEAxO,UAAU,gBACTlB,OAAA;gBAAKgP,SAAS,EAAC,oCAAoC;gBAAAG,QAAA,gBACjDnP,OAAA;kBAAIgP,SAAS,EAAC,+BAA+B;kBAAAG,QAAA,gBAC3CnP,OAAA,CAACnB,OAAO;oBAACmQ,SAAS,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAEL1P,OAAA;kBAAKgP,SAAS,EAAC,kBAAkB;kBAAAG,QAAA,EAC9BtL,aAAa,gBACZ7D,OAAA;oBAAKgP,SAAS,EAAC,kDAAkD;oBAAAG,QAAA,gBAC/DnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;sBAAC3C,SAAS,EAAC,uBAAuB;sBAACC,KAAK,EAAE;wBAAE2C,QAAQ,EAAE;sBAAQ,CAAE;sBAAAzC,QAAA,gBACzEnP,OAAA,CAAClC,IAAI,CAAC+T,OAAO;wBACXjK,IAAI,EAAC,MAAM;wBACXkK,KAAK,EAAEnO,WAAY;wBACnBoO,QAAQ,EAAG3K,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;wBAChDE,SAAS,EAAE,CAAC,CAACjO,gBAAiB;wBAC9BkO,WAAW,EAAC;sBAAoB;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;wBAACtK,IAAI,EAAC,SAAS;wBAAAuH,QAAA,EAClCpL;sBAAgB;wBAAAwL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACb1P,OAAA,CAACjC,MAAM;sBACLuR,OAAO,EAAC,SAAS;sBACjBxH,IAAI,EAAC,IAAI;sBACTkH,SAAS,EAAC,MAAM;sBAChBuC,OAAO,EAAEpG,eAAgB;sBAAAgE,QAAA,eAEzBnP,OAAA,CAACnB,OAAO;wBAAA0Q,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACT1P,OAAA,CAACjC,MAAM;sBACLuR,OAAO,EAAC,WAAW;sBACnBxH,IAAI,EAAC,IAAI;sBACTyJ,OAAO,EAAErG,iBAAkB;sBAAAiE,QAAA,eAE3BnP,OAAA,CAACR,MAAM;wBAAA+P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEN1P,OAAA;oBAAKgP,SAAS,EAAC,kDAAkD;oBAAAG,QAAA,gBAC/DnP,OAAA;sBAAIgP,SAAS,EAAC,WAAW;sBAAAG,QAAA,EACtBxL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;oBAAC;sBAAAkP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACL1P,OAAA,CAACjC,MAAM;sBACLuR,OAAO,EAAC,iBAAiB;sBACzBxH,IAAI,EAAC,IAAI;sBACTyJ,OAAO,EAAEtG,gBAAiB;sBAAAkE,QAAA,EAC3B;oBAED;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN1P,OAAA;kBAAKgP,SAAS,EAAC,2BAA2B;kBAAAG,QAAA,eACtCnP,OAAA,CAAC/B,GAAG;oBAAC+Q,SAAS,EAAC,MAAM;oBAAAG,QAAA,gBACnBnP,OAAA,CAAC9B,GAAG;sBAACsT,EAAE,EAAE,CAAE;sBAAArC,QAAA,gBACTnP,OAAA;wBAAKgP,SAAS,EAAC,wDAAwD;wBAAAG,QAAA,gBACrEnP,OAAA;0BAAIgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAACnP,OAAA,CAACd,eAAe;4BAAC8P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,uBAAmB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EAChF,CAACzL,eAAe,iBACfjE,OAAA,CAACjC,MAAM;0BACLuR,OAAO,EAAC,iBAAiB;0BACzBxH,IAAI,EAAC,IAAI;0BACTyJ,OAAO,EAAE5F,kBAAmB;0BAAAwD,QAAA,EAC7B;wBAED;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAELzL,eAAe,gBACdjE,OAAA;wBAAKgP,SAAS,EAAC,sBAAsB;wBAAAG,QAAA,eACnCnP,OAAA,CAAClC,IAAI;0BAACqU,QAAQ,EAAG/K,CAAC,IAAKA,CAAC,CAACwC,cAAc,CAAC,CAAE;0BAAAuF,QAAA,gBACxCnP,OAAA,CAAC/B,GAAG;4BAAAkR,QAAA,gBACFnP,OAAA,CAAC9B,GAAG;8BAACsT,EAAE,EAAE,CAAE;8BAAArC,QAAA,eACTnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;gCAAC3C,SAAS,EAAC,MAAM;gCAAAG,QAAA,gBAC1BnP,OAAA,CAAClC,IAAI,CAACsU,KAAK;kCAAAjD,QAAA,eAACnP,OAAA;oCAAAmP,QAAA,EAAQ;kCAAwB;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAClE1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;kCACXjK,IAAI,EAAC,QAAQ;kCACbyK,IAAI,EAAC,MAAM;kCACXC,GAAG,EAAC,GAAG;kCACPR,KAAK,EAAE3N,oBAAqB;kCAC5B4N,QAAQ,EAAG3K,CAAC,IAAKhD,uBAAuB,CAACgD,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;kCACzDE,SAAS,EAAE,CAAC,CAACzN,eAAe,CAACJ;gCAAqB;kCAAAoL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACnD,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;kCAACtK,IAAI,EAAC,SAAS;kCAAAuH,QAAA,EAClC5K,eAAe,CAACJ;gCAAoB;kCAAAoL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChB,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACN1P,OAAA,CAAC9B,GAAG;8BAACsT,EAAE,EAAE,CAAE;8BAAArC,QAAA,eACTnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;gCAAC3C,SAAS,EAAC,MAAM;gCAAAG,QAAA,gBAC1BnP,OAAA,CAAClC,IAAI,CAACsU,KAAK;kCAAAjD,QAAA,eAACnP,OAAA;oCAAAmP,QAAA,EAAQ;kCAAoB;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAQ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC,eAC9D1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;kCACXjK,IAAI,EAAC,QAAQ;kCACbyK,IAAI,EAAC,MAAM;kCACXC,GAAG,EAAC,GAAG;kCACPR,KAAK,EAAEzN,gBAAiB;kCACxB0N,QAAQ,EAAG3K,CAAC,IAAK9C,mBAAmB,CAAC8C,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;kCACrDE,SAAS,EAAE,CAAC,CAACzN,eAAe,CAACF;gCAAiB;kCAAAkL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC/C,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;kCAACtK,IAAI,EAAC,SAAS;kCAAAuH,QAAA,EAClC5K,eAAe,CAACF;gCAAgB;kCAAAkL,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EAELvL,oBAAoB,IAAIE,gBAAgB,IAAIO,UAAU,CAACT,oBAAoB,CAAC,GAAG,CAAC,IAAIS,UAAU,CAACP,gBAAgB,CAAC,GAAG,CAAC,iBACnHrE,OAAA;4BAAKgP,SAAS,EAAC,wCAAwC;4BAAAG,QAAA,gBACrDnP,OAAA;8BAAAmP,QAAA,EAAQ;4BAAmB;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,EACnC,CAAE,CAAC9K,UAAU,CAACP,gBAAgB,CAAC,GAAGO,UAAU,CAACT,oBAAoB,CAAC,IAAIS,UAAU,CAACP,gBAAgB,CAAC,GAAI,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC,EAAC,GACzH;0BAAA;4BAAAwK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN,eAED1P,OAAA;4BAAKgP,SAAS,EAAC,cAAc;4BAAAG,QAAA,gBAC3BnP,OAAA,CAACjC,MAAM;8BACL6J,IAAI,EAAC,QAAQ;8BACb0H,OAAO,EAAC,SAAS;8BACjBxH,IAAI,EAAC,IAAI;8BACTyJ,OAAO,EAAEtF,gBAAiB;8BAC1BsG,QAAQ,EAAEnR,OAAQ;8BAAA+N,QAAA,gBAElBnP,OAAA,CAACnB,OAAO;gCAACmQ,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,gBAE9B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACT1P,OAAA,CAACjC,MAAM;8BACL6J,IAAI,EAAC,QAAQ;8BACb0H,OAAO,EAAC,WAAW;8BACnBxH,IAAI,EAAC,IAAI;8BACTyJ,OAAO,EAAE3F,mBAAoB;8BAAAuD,QAAA,gBAE7BnP,OAAA,CAACR,MAAM;gCAACwP,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,UAE7B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,gBAEN1P,OAAA,CAACtB,KAAK;wBAACsS,QAAQ;wBAACC,KAAK;wBAAA9B,QAAA,eACnBnP,OAAA;0BAAAmP,QAAA,gBACEnP,OAAA;4BAAAmP,QAAA,gBACEnP,OAAA;8BAAAmP,QAAA,eAAInP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAkB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC5C1P,OAAA;8BAAAmP,QAAA,GAAI,MAAI,EAAC3O,gBAAgB;4BAAA;8BAAA+O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B,CAAC,eACL1P,OAAA;4BAAAmP,QAAA,gBACEnP,OAAA;8BAAAmP,QAAA,eAAInP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACxC1P,OAAA;8BAAAmP,QAAA,GAAI,MAAI,EAACzO,YAAY;4BAAA;8BAAA6O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB,CAAC,eACL1P,OAAA;4BAAAmP,QAAA,gBACEnP,OAAA;8BAAAmP,QAAA,eAAInP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACxC1P,OAAA;8BAAAmP,QAAA,GAAK1L,YAAY,EAAC,GAAC;4BAAA;8BAAA8L,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACR,EAEA9O,YAAY,iBACXZ,OAAA;wBAAKgP,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBACnBnP,OAAA;0BAAIgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAACnP,OAAA,CAACV,eAAe;4BAAC0P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,SAAK;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnE1P,OAAA;0BAAKgP,SAAS,EAAC,sBAAsB;0BAAAG,QAAA,EAClCvO;wBAAY;0BAAA2O,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN,eAED1P,OAAA;wBAAIgP,SAAS,EAAC,WAAW;wBAAAG,QAAA,gBAACnP,OAAA,CAAChB,MAAM;0BAACgQ,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAAM;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChE1P,OAAA;wBAAKgP,SAAS,EAAC,MAAM;wBAAAG,QAAA,EAClB7M,aAAa,CAAC+D,MAAM,GAAG,CAAC,GACvB/D,aAAa,CAACgO,GAAG,CAAC,CAAC5J,MAAM,EAAEsC,KAAK;0BAAA,IAAAwJ,sBAAA,EAAAC,sBAAA;0BAAA,oBAC9BzS,OAAA;4BAAiBgP,SAAS,EAAC,MAAM;4BAAAG,QAAA,GAC9BQ,iBAAiB,CAAC,EAAA6C,sBAAA,GAAA9L,MAAM,CAAC2H,mBAAmB,cAAAmE,sBAAA,uBAA1BA,sBAAA,CAA4BjE,KAAK,KAAI7H,MAAM,CAAC6H,KAAK,IAAI,MAAM,CAAC,eAC/EvO,OAAA;8BAAMgP,SAAS,EAAC,MAAM;8BAAAG,QAAA,EAAE,EAAAsD,sBAAA,GAAA/L,MAAM,CAAC2H,mBAAmB,cAAAoE,sBAAA,uBAA1BA,sBAAA,CAA4BnE,UAAU,KAAI5H,MAAM,CAAC6H;4BAAK;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,GAF9E1G,KAAK;4BAAAuG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAGV,CAAC;wBAAA,CACP,CAAC,gBAEF1P,OAAA;0BAAGgP,SAAS,EAAC,YAAY;0BAAAG,QAAA,EAAC;wBAA8B;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG;sBAC5D;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAEN1P,OAAA;wBAAIgP,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBAACnP,OAAA,CAACX,SAAS;0BAAC2P,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,qBAAiB;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACxEU,sBAAsB,CAAC,CAAC,eAEzBpQ,OAAA;wBAAKgP,SAAS,EAAC,MAAM;wBAAAG,QAAA,eACnBnP,OAAA,CAACjC,MAAM;0BACLuR,OAAO,EAAC,iBAAiB;0BACzBN,SAAS,EAAC,OAAO;0BACjBuC,OAAO,EAAExG,YAAa;0BAAAoE,QAAA,gBAEtBnP,OAAA,CAACN,SAAS;4BAACsP,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,2BAEhC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1P,OAAA,CAAC9B,GAAG;sBAACsT,EAAE,EAAE,CAAE;sBAACxC,SAAS,EAAC,aAAa;sBAAAG,QAAA,gBACjCnP,OAAA;wBAAIgP,SAAS,EAAC,MAAM;wBAAAG,QAAA,gBAACnP,OAAA,CAACjB,OAAO;0BAACiQ,SAAS,EAAC;wBAAM;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,kBAAc;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACnEhO,iBAAiB,IAAIA,iBAAiB,CAAC2E,MAAM,GAAG,CAAC,gBAChDrG,OAAA;wBAAKgP,SAAS,EAAC,0BAA0B;wBAAAG,QAAA,gBACvCnP,OAAA;0BAAKgP,SAAS,EAAC,qBAAqB;0BAAAG,QAAA,EACjCzN,iBAAiB,CAAC4O,GAAG,CAAC,CAACoC,QAAQ,EAAE1J,KAAK,kBACrChJ,OAAA;4BAEEgP,SAAS,EAAE,sBAAsBhG,KAAK,KAAKlH,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;4BAC9EyP,OAAO,EAAEA,CAAA,KAAMxP,mBAAmB,CAACiH,KAAK,CAAE;4BAAAmG,QAAA,gBAE1CnP,OAAA,CAAC5B,KAAK;8BACJ8S,GAAG,EAAEwB,QAAS;8BACdvB,GAAG,EAAE,WAAWnI,KAAK,GAAG,CAAC,EAAG;8BAC5BoI,SAAS;8BACTpC,SAAS,EAAC;4BAAe;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1B,CAAC,eACF1P,OAAA;8BAAMgP,SAAS,EAAC,cAAc;8BAAAG,QAAA,EAAEnG,KAAK,GAAG;4BAAC;8BAAAuG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA,GAV5C1G,KAAK;4BAAAuG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAWP,CACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN1P,OAAA;0BAAKgP,SAAS,EAAC,2BAA2B;0BAAAG,QAAA,eACxCnP,OAAA,CAAC5B,KAAK;4BACJ8S,GAAG,EAAExP,iBAAiB,CAACI,gBAAgB,CAAE;4BACzCqP,GAAG,EAAC,SAAS;4BACbC,SAAS;4BACTpC,SAAS,EAAC,oBAAoB;4BAC9BC,KAAK,EAAE;8BAAEoC,SAAS,EAAE;4BAAQ;0BAAE;4BAAA9B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,gBAEN1P,OAAA;wBAAKgP,SAAS,EAAC,sBAAsB;wBAAAG,QAAA,gBACnCnP,OAAA,CAACjB,OAAO;0BAAC+I,IAAI,EAAE,EAAG;0BAACkH,SAAS,EAAC;wBAAgB;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChD1P,OAAA;0BAAGgP,SAAS,EAAC,iBAAiB;0BAAAG,QAAA,EAAC;wBAAmB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEN1P,OAAA;gBAAKgP,SAAS,EAAC,UAAU;gBAAAG,QAAA,eACvBnP,OAAA,CAACxB,IAAI;kBACH6B,EAAE,EAAC,uBAAuB;kBAC1BsS,SAAS,EAAE5P,SAAU;kBACrB6P,QAAQ,EAAGC,CAAC,IAAK7P,YAAY,CAAC6P,CAAC,CAAE;kBACjC7D,SAAS,EAAC,MAAM;kBAAAG,QAAA,gBAEhBnP,OAAA,CAACvB,GAAG;oBAACqU,QAAQ,EAAC,SAAS;oBAACC,KAAK,eAAE/S,OAAA;sBAAAmP,QAAA,gBAAMnP,OAAA,CAACf,YAAY;wBAAC+P,SAAS,EAAC;sBAAM;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAAe;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAAAP,QAAA,eAC3FnP,OAAA,CAAC/B,GAAG;sBAAC+Q,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBACnBnP,OAAA,CAAC9B,GAAG;wBAACsT,EAAE,EAAE,CAAE;wBAAArC,QAAA,gBACTnP,OAAA;0BAAIgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAACnP,OAAA,CAACf,YAAY;4BAAC+P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAY;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACtE7L,aAAa,gBACZ7D,OAAA;0BAAKgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBACnBnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;4BAAAxC,QAAA,gBACTnP,OAAA,CAAClC,IAAI,CAAC+T,OAAO;8BACXjK,IAAI,EAAC,MAAM;8BACXkK,KAAK,EAAEnO,WAAY;8BACnBoO,QAAQ,EAAG3K,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;8BAChDE,SAAS,EAAE,CAAC,CAACjO,gBAAiB;8BAC9BkO,WAAW,EAAC;4BAAoB;8BAAA1C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjC,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;8BAACtK,IAAI,EAAC,SAAS;8BAAAuH,QAAA,EAClCpL;4BAAgB;8BAAAwL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACb1P,OAAA;4BAAKgP,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBACnBnP,OAAA,CAACjC,MAAM;8BACLuR,OAAO,EAAC,SAAS;8BACjBxH,IAAI,EAAC,IAAI;8BACTkH,SAAS,EAAC,MAAM;8BAChBuC,OAAO,EAAEpG,eAAgB;8BAAAgE,QAAA,gBAEzBnP,OAAA,CAACnB,OAAO;gCAACmQ,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,SAC9B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC,eACT1P,OAAA,CAACjC,MAAM;8BACLuR,OAAO,EAAC,WAAW;8BACnBxH,IAAI,EAAC,IAAI;8BACTyJ,OAAO,EAAErG,iBAAkB;8BAAAiE,QAAA,gBAE3BnP,OAAA,CAACR,MAAM;gCAACwP,SAAS,EAAC;8BAAM;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,WAC7B;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEN1P,OAAA;0BAAKgP,SAAS,EAAC,6EAA6E;0BAAAG,QAAA,gBAC1FnP,OAAA;4BAAAmP,QAAA,EACGxL,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACtE9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE;0BAAC;4BAAAkP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACT1P,OAAA,CAACjC,MAAM;4BACLuR,OAAO,EAAC,iBAAiB;4BACzBxH,IAAI,EAAC,IAAI;4BACTyJ,OAAO,EAAEtG,gBAAiB;4BAAAkE,QAAA,EAC3B;0BAED;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACN,eAED1P,OAAA;0BAAIgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAACnP,OAAA,CAAChB,MAAM;4BAACgQ,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,UAAM;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3D1P,OAAA;0BAAKgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,EAClB7M,aAAa,CAAC+D,MAAM,GAAG,CAAC,GACvB/D,aAAa,CAACgO,GAAG,CAAC,CAAC5J,MAAM,EAAEsC,KAAK;4BAAA,IAAAgK,sBAAA,EAAAC,sBAAA;4BAAA,oBAC9BjT,OAAA;8BAAiBgP,SAAS,EAAC,MAAM;8BAAAG,QAAA,GAC9BQ,iBAAiB,CAAC,EAAAqD,sBAAA,GAAAtM,MAAM,CAAC2H,mBAAmB,cAAA2E,sBAAA,uBAA1BA,sBAAA,CAA4BzE,KAAK,KAAI7H,MAAM,CAAC6H,KAAK,IAAI,MAAM,CAAC,eAC/EvO,OAAA;gCAAMgP,SAAS,EAAC,MAAM;gCAAAG,QAAA,EAAE,EAAA8D,sBAAA,GAAAvM,MAAM,CAAC2H,mBAAmB,cAAA4E,sBAAA,uBAA1BA,sBAAA,CAA4B3E,UAAU,KAAI5H,MAAM,CAAC6H;8BAAK;gCAAAgB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA,GAF9E1G,KAAK;8BAAAuG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAGV,CAAC;0BAAA,CACP,CAAC,gBAEF1P,OAAA;4BAAGgP,SAAS,EAAC,YAAY;4BAAAG,QAAA,EAAC;0BAA8B;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAC5D;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAEN1P,OAAA;0BAAIgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBAACnP,OAAA,CAACX,SAAS;4BAAC2P,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,qBAAiB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACxEU,sBAAsB,CAAC,CAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eAEN1P,OAAA,CAAC9B,GAAG;wBAACsT,EAAE,EAAE,CAAE;wBAAArC,QAAA,eACTnP,OAAA;0BAAKgP,SAAS,EAAC,MAAM;0BAAAG,QAAA,gBACnBnP,OAAA;4BAAIgP,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAACnP,OAAA,CAACjB,OAAO;8BAACiQ,SAAS,EAAC;4BAAM;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,kBAAc;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpE1P,OAAA;4BAAGgP,SAAS,EAAC,iBAAiB;4BAAAG,QAAA,GAAC,kDAAgD,EAAC7N,aAAa,CAAC+E,MAAM,EAAC,MAAI;0BAAA;4BAAAkJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,EAG5GlO,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;4BAAKgP,SAAS,EAAC,+BAA+B;4BAAAG,QAAA,gBAC5CnP,OAAA;8BAAKgP,SAAS,EAAC,qBAAqB;8BAAAG,QAAA,EACjC3N,gBAAgB,CAAC8O,GAAG,CAAC,CAAC4C,UAAU,EAAElK,KAAK,kBACtChJ,OAAA;gCAEEgP,SAAS,EAAE,sBAAsBhG,KAAK,KAAKlH,gBAAgB,GAAG,QAAQ,GAAG,EAAE,EAAG;gCAC9EyP,OAAO,EAAEA,CAAA,KAAMxP,mBAAmB,CAACiH,KAAK,CAAE;gCAAAmG,QAAA,gBAE1CnP,OAAA;kCAAKgP,SAAS,EAAC,eAAe;kCAAAG,QAAA,eAC5BnP,OAAA,CAACjC,MAAM;oCACLuR,OAAO,EAAC,QAAQ;oCAChBxH,IAAI,EAAC,IAAI;oCACTkH,SAAS,EAAC,kBAAkB;oCAC5BuC,OAAO,EAAGnK,CAAC,IAAK;sCACdA,CAAC,CAAC+L,eAAe,CAAC,CAAC;sCACnBpK,WAAW,CAACC,KAAK,CAAC;oCACpB,CAAE;oCAAAmG,QAAA,eAEFnP,OAAA,CAACT,OAAO;sCAAAgQ,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACL;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACN,CAAC,eACN1P,OAAA,CAAC5B,KAAK;kCACJ8S,GAAG,EAAEgC,UAAW;kCAChB/B,GAAG,EAAE,WAAWnI,KAAK,GAAG,CAAC,EAAG;kCAC5BoI,SAAS;kCACTpC,SAAS,EAAC;gCAAe;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACF1P,OAAA;kCAAMgP,SAAS,EAAC,cAAc;kCAAAG,QAAA,EAAEnG,KAAK,GAAG;gCAAC;kCAAAuG,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA,GAvB5C1G,KAAK;gCAAAuG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAwBP,CACN;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,EAELlO,gBAAgB,CAAC6E,MAAM,GAAG,CAAC,iBAC1BrG,OAAA;8BAAKgP,SAAS,EAAC,2BAA2B;8BAAAG,QAAA,eACxCnP,OAAA,CAAC5B,KAAK;gCACJ8S,GAAG,EAAE1P,gBAAgB,CAACM,gBAAgB,CAAE;gCACxCqP,GAAG,EAAC,iBAAiB;gCACrBC,SAAS;gCACTpC,SAAS,EAAC,oBAAoB;gCAC9BC,KAAK,EAAE;kCAAEoC,SAAS,EAAE;gCAAQ;8BAAE;gCAAA9B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CACN,EAGAxN,WAAW,gBACVlC,OAAA;4BAAKgP,SAAS,EAAC,kCAAkC;4BAAAG,QAAA,gBAC/CnP,OAAA;8BAAIgP,SAAS,EAAC,MAAM;8BAAAG,QAAA,EAAC;4BAAgB;8BAAAI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC1C1P,OAAA,CAAC1B,WAAW;8BACViS,GAAG,EAAEvO,cAAe;8BACpBoR,KAAK,EAAE,GAAGlK,IAAI,CAACqB,KAAK,CAACvI,cAAc,CAAC,GAAI;8BACxCsN,OAAO,EAAC,MAAM;8BACd+D,QAAQ;8BACRrE,SAAS,EAAC;4BAAM;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,eACF1P,OAAA;8BAAGgP,SAAS,EAAC,YAAY;8BAAAG,QAAA,gBACvBnP,OAAA,CAAClB,QAAQ;gCAACkQ,SAAS,EAAC;8BAAmB;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,cAChC,EAACpO,aAAa,CAAC+E,MAAM,EAAC,YAClC;4BAAA;8BAAAkJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC,GAENpO,aAAa,CAAC+E,MAAM,GAAG,EAAE,iBACvBrG,OAAA;4BAAA,GAAS0I,YAAY,CAAC,CAAC;4BAAEsG,SAAS,EAAE,0BAA0BpG,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;4BAAAuG,QAAA,gBAC3FnP,OAAA;8BAAA,GAAW2I,aAAa,CAAC,CAAC;8BAAE2K,QAAQ;4BAAA;8BAAA/D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACvC1P,OAAA;8BAAKgP,SAAS,EAAC,aAAa;8BAAAG,QAAA,gBAC1BnP,OAAA,CAAClB,QAAQ;gCAACgJ,IAAI,EAAE,EAAG;gCAACkH,SAAS,EAAC;8BAAmB;gCAAAO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eACpD1P,OAAA;gCAAAmP,QAAA,EAAG;8BAAmD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eAC1D1P,OAAA;gCAAGgP,SAAS,EAAC,kBAAkB;gCAAAG,QAAA,EAAC;8BAAiD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,eACrF1P,OAAA;gCAAGgP,SAAS,EAAC,kBAAkB;gCAAAG,QAAA,EAAC;8BAAgD;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAER,eAED1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;4BACXjK,IAAI,EAAC,MAAM;4BACX2L,GAAG,EAAEhT,YAAa;4BAClBwR,QAAQ,EAAE5K,iBAAkB;4BAC5B0B,MAAM,EAAC,SAAS;4BAChByK,QAAQ;4BACRrE,KAAK,EAAE;8BAAEuE,OAAO,EAAE;4BAAO;0BAAE;4BAAAjE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5B,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN1P,OAAA,CAACvB,GAAG;oBAACqU,QAAQ,EAAC,SAAS;oBAACC,KAAK,eAAE/S,OAAA;sBAAAmP,QAAA,gBAAMnP,OAAA,CAACd,eAAe;wBAAC8P,SAAS,EAAC;sBAAM;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAAO;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAAAP,QAAA,eACtFnP,OAAA,CAAClC,IAAI;sBAACqU,QAAQ,EAAExI,gBAAiB;sBAACqF,SAAS,EAAC,MAAM;sBAAAG,QAAA,gBAChDnP,OAAA,CAAC/B,GAAG;wBAAAkR,QAAA,gBACFnP,OAAA,CAAC9B,GAAG;0BAACsT,EAAE,EAAE,CAAE;0BAAArC,QAAA,gBACTnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1BnP,OAAA,CAAClC,IAAI,CAACsU,KAAK;8BAAAjD,QAAA,gBACTnP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAwB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACzC1P,OAAA,CAACrB,cAAc;gCACbsR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAElQ,OAAA,CAACpB,OAAO;kCAAAuQ,QAAA,EAAC;gCAAoC;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEjEnP,OAAA,CAACf,YAAY;kCAAC+P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACb1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;8BACXjK,IAAI,EAAC,QAAQ;8BACbyK,IAAI,EAAC,MAAM;8BACXC,GAAG,EAAC,GAAG;8BACPR,KAAK,EAAEtR,gBAAiB;8BACxBuR,QAAQ,EAAG3K,CAAC,IAAK3G,mBAAmB,CAAC2G,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;8BACrDE,SAAS,EAAE,CAAC,CAACzO,gBAAgB,CAAC/C,gBAAiB;8BAC/CiT,QAAQ;4BAAA;8BAAAlE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;8BAACtK,IAAI,EAAC,SAAS;8BAAAuH,QAAA,EAClC5L,gBAAgB,CAAC/C;4BAAgB;8BAAA+O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACb,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eAEb1P,OAAA,CAAClC,IAAI,CAAC6T,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1BnP,OAAA,CAAClC,IAAI,CAACsU,KAAK;8BAAAjD,QAAA,gBACTnP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAoB;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eACrC1P,OAAA,CAACrB,cAAc;gCACbsR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAElQ,OAAA,CAACpB,OAAO;kCAAAuQ,QAAA,EAAC;gCAA4C;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEzEnP,OAAA,CAACf,YAAY;kCAAC+P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACb1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;8BACXjK,IAAI,EAAC,QAAQ;8BACbyK,IAAI,EAAC,MAAM;8BACXC,GAAG,EAAC,GAAG;8BACPR,KAAK,EAAEpR,YAAa;8BACpBqR,QAAQ,EAAG3K,CAAC,IAAKzG,eAAe,CAACyG,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;8BACjDE,SAAS,EAAE,CAAC,CAACzO,gBAAgB,CAAC7C,YAAa;8BAC3C+S,QAAQ;4BAAA;8BAAAlE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;8BAACtK,IAAI,EAAC,SAAS;8BAAAuH,QAAA,EAClC5L,gBAAgB,CAAC7C;4BAAY;8BAAA6O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,EAEZlP,gBAAgB,IAAIE,YAAY,IAAIkE,UAAU,CAACpE,gBAAgB,CAAC,GAAG,CAAC,IAAIoE,UAAU,CAAClE,YAAY,CAAC,GAAG,CAAC,iBACnGV,OAAA;4BAAKgP,SAAS,EAAC,2BAA2B;4BAAAG,QAAA,gBACxCnP,OAAA;8BAAKgP,SAAS,EAAC,wDAAwD;8BAAAG,QAAA,gBACrEnP,OAAA;gCAAAmP,QAAA,eAAMnP,OAAA;kCAAAmP,QAAA,EAAQ;gCAAc;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC,eAC5C1P,OAAA,CAACzB,KAAK;gCAACmV,EAAE,EACPjQ,YAAY,GAAG,EAAE,GAAG,QAAQ,GAC5BA,YAAY,GAAG,EAAE,GAAG,SAAS,GAC7B,SACD;gCAAA0L,QAAA,gBACCnP,OAAA,CAACZ,YAAY;kCAAC4P,SAAS,EAAC;gCAAM;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EAChCjM,YAAY,EAAC,GAChB;8BAAA;gCAAA8L,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACN1P,OAAA;8BAAKgP,SAAS,EAAC;4BAAyB;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC3C1P,OAAA;8BAAKgP,SAAS,EAAC,qCAAqC;8BAAAG,QAAA,gBAClDnP,OAAA;gCAAAmP,QAAA,EAAO;8BAAG;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eAClB1P,OAAA;gCAAAmP,QAAA,EAAO;8BAAI;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eAEN1P,OAAA,CAAC9B,GAAG;0BAACsT,EAAE,EAAE,CAAE;0BAAArC,QAAA,eACTnP,OAAA,CAAClC,IAAI,CAAC6T,KAAK;4BAAC3C,SAAS,EAAC,MAAM;4BAAAG,QAAA,gBAC1BnP,OAAA,CAAClC,IAAI,CAACsU,KAAK;8BAAAjD,QAAA,gBACTnP,OAAA;gCAAAmP,QAAA,EAAQ;8BAAc;gCAAAI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,eAC/B1P,OAAA,CAACrB,cAAc;gCACbsR,SAAS,EAAC,KAAK;gCACfC,OAAO,eAAElQ,OAAA,CAACpB,OAAO;kCAAAuQ,QAAA,EAAC;gCAAyC;kCAAAI,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAS,CAAE;gCAAAP,QAAA,eAEtEnP,OAAA,CAACf,YAAY;kCAAC+P,SAAS,EAAC;gCAAiB;kCAAAO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACP,CAAC,eACb1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO;8BACX8B,EAAE,EAAC,UAAU;8BACbC,IAAI,EAAE,CAAE;8BACR9B,KAAK,EAAElR,YAAa;8BACpBmR,QAAQ,EAAG3K,CAAC,IAAKvG,eAAe,CAACuG,CAAC,CAACG,MAAM,CAACuK,KAAK,CAAE;8BACjDE,SAAS,EAAE,CAAC,CAACzO,gBAAgB,CAAC3C,YAAa;8BAC3CqR,WAAW,EAAC;4BAAkD;8BAAA1C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/D,CAAC,eACF1P,OAAA,CAAClC,IAAI,CAAC+T,OAAO,CAACK,QAAQ;8BAACtK,IAAI,EAAC,SAAS;8BAAAuH,QAAA,EAClC5L,gBAAgB,CAAC3C;4BAAY;8BAAA2O,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC,eACxB1P,OAAA,CAAClC,IAAI,CAAC+V,IAAI;8BAAC7E,SAAS,EAAC,YAAY;8BAAAG,QAAA,GAC9BvO,YAAY,GAAG,GAAG,GAAGA,YAAY,CAACyF,MAAM,GAAG,GAAG,EAAC,uBAClD;4BAAA;8BAAAkJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAW,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAEN1P,OAAA;wBAAKgP,SAAS,EAAC,mBAAmB;wBAAAG,QAAA,eAChCnP,OAAA,CAACjC,MAAM;0BAAC6J,IAAI,EAAC,QAAQ;0BAACoH,SAAS,EAAC,aAAa;0BAAClH,IAAI,EAAC,IAAI;0BAAAqH,QAAA,gBACrDnP,OAAA,CAACnB,OAAO;4BAACmQ,SAAS,EAAC;0BAAM;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,mBAE9B;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1P,OAAA,CAACwQ,iBAAiB;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGrB1P,OAAA,CAAC3B,KAAK;MAACoS,IAAI,EAAEtN,YAAa;MAACuN,MAAM,EAAE1F,aAAc;MAAC2F,QAAQ;MAAAxB,QAAA,gBACxDnP,OAAA,CAAC3B,KAAK,CAACuS,MAAM;QAACC,WAAW;QAAA1B,QAAA,eACvBnP,OAAA,CAAC3B,KAAK,CAACyS,KAAK;UAAA3B,QAAA,gBACVnP,OAAA,CAACN,SAAS;YAACsP,SAAS,EAAC;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf1P,OAAA,CAAC3B,KAAK,CAAC0S,IAAI;QAAA5B,QAAA,gBACTnP,OAAA;UAAAmP,QAAA,EAAG;QAAgE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvE1P,OAAA;UAAKgP,SAAS,EAAC,sBAAsB;UAAAG,QAAA,gBACnCnP,OAAA;YAAGgP,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAACnP,OAAA;cAAAmP,QAAA,EAAQ;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/L,WAAW,KAAKvB,cAAc,IAAIA,cAAc,CAAC8D,sBAAsB,GACpH9D,cAAc,CAAC8D,sBAAsB,CAACC,WAAW,GACjD,aAAa9F,EAAE,EAAE,CAAC;UAAA;YAAAkP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB1P,OAAA;YAAGgP,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAACnP,OAAA;cAAAmP,QAAA,EAAQ;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAClP,gBAAgB;UAAA;YAAA+O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClF1P,OAAA;YAAGgP,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAACnP,OAAA;cAAAmP,QAAA,EAAQ;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAChP,YAAY;UAAA;YAAA6O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E1P,OAAA;YAAGgP,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAACnP,OAAA;cAAAmP,QAAA,EAAQ;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,EAAC,GAAC;UAAA;YAAA8L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACb1P,OAAA,CAAC3B,KAAK,CAACiT,MAAM;QAAAnC,QAAA,gBACXnP,OAAA,CAACjC,MAAM;UAACuR,OAAO,EAAC,WAAW;UAACiC,OAAO,EAAEvG,aAAc;UAAAmE,QAAA,EAAC;QAEpD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1P,OAAA,CAACjC,MAAM;UACLuR,OAAO,EAAC,SAAS;UACjBiC,OAAO,EAAElF,qBAAsB;UAC/BkG,QAAQ,EAAElP,UAAW;UAAA8L,QAAA,EAEpB9L,UAAU,gBACTrD,OAAA,CAAAE,SAAA;YAAAiP,QAAA,gBACEnP,OAAA,CAAC7B,OAAO;cACNwV,EAAE,EAAC,MAAM;cACTvE,SAAS,EAAC,QAAQ;cAClBtH,IAAI,EAAC,IAAI;cACTuH,IAAI,EAAC,QAAQ;cACb,eAAY,MAAM;cAClBL,SAAS,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,iBAEJ;UAAA,eAAE,CAAC,gBAEH1P,OAAA,CAAAE,SAAA;YAAAiP,QAAA,gBACEnP,OAAA,CAACL,UAAU;cAACqP,SAAS,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEjC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACtP,EAAA,CAthDID,sBAAsB;EAAA,QACXxC,SAAS,EACPC,WAAW,EAwN0BgC,WAAW;AAAA;AAAAkU,EAAA,GA1N7D3T,sBAAsB;AAwhD5B,eAAeA,sBAAsB;AAAC,IAAA2T,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}