import React, { useState, useEffect } from "react";
import axios from "axios";
import RoleBasedNavBar from "../components/RoleBasedNavBar";
import Select from "react-select";
import { Row, Col, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, Badge } from "react-bootstrap";
import { FaInfoCircle, FaTshirt, FaCheck, FaExclamationTriangle, FaClipboardCheck, FaPlus, FaTrash } from "react-icons/fa";

const AddDailySewingRecord = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [productColors, setProductColors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768); // Track sidebar state

  // Array to store multiple color variants
  const [colorVariants, setColorVariants] = useState([{
    id: Date.now(),
    selectedColor: "",
    alreadySewn: null,
    xs: 0,
    s: 0,
    m: 0,
    l: 0,
    xl: 0,
    damageCount: 0,
    loading: false
  }]);

  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formValid, setFormValid] = useState(false);

  // Add resize event listener to update sidebar state
  useEffect(() => {
    const handleResize = () => {
      setIsSidebarOpen(window.innerWidth >= 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    axios
      .get("http://localhost:8000/api/cutting/cutting-records/")
      .then((res) => setProducts(res.data))
      .catch((err) => console.error("Error fetching products:", err));
  }, []);

  useEffect(() => {
    if (selectedProduct) {
      const product = products.find((p) => p.id === parseInt(selectedProduct));
      if (product?.details) {
        const options = product.details.map((detail) => {
          const totalCut =
            (detail.xs || 0) +
            (detail.s || 0) +
            (detail.m || 0) +
            (detail.l || 0) +
            (detail.xl || 0);

          // Get fabric name and color name for better identification
          const fabricName = detail.fabric_variant_data?.fabric_definition_data?.fabric_name || "Unknown Fabric";
          const colorName = detail.fabric_variant_data?.color_name || detail.fabric_variant_data?.color || "N/A";

          return {
            value: detail.id,
            label: `${fabricName} - ${colorName}`,
            fabricName: fabricName,
            colorName: colorName,
            color: detail.fabric_variant_data?.color || "#ffffff",
            totalCut,
            // Store individual size quantities for validation
            xs_cut: detail.xs || 0,
            s_cut: detail.s || 0,
            m_cut: detail.m || 0,
            l_cut: detail.l || 0,
            xl_cut: detail.xl || 0,
          };
        });
        setProductColors(options);
      } else {
        setProductColors([]);
      }
      // Reset all color variants when product changes
      setColorVariants([{
        id: Date.now(),
        selectedColor: "",
        alreadySewn: null,
        xs: 0,
        s: 0,
        m: 0,
        l: 0,
        xl: 0,
        damageCount: 0,
        loading: false
      }]);
    }
  }, [selectedProduct, products]);

  // Function to fetch already sewn quantities for a specific variant
  const fetchAlreadySewn = async (variantId, colorId) => {
    // Update the loading state for this specific variant
    setColorVariants(prev => prev.map(variant =>
      variant.id === variantId
        ? { ...variant, loading: true }
        : variant
    ));

    try {
      const res = await axios.get(`http://localhost:8000/api/sewing/already-sewn/${colorId}/`);
      setColorVariants(prev => prev.map(variant =>
        variant.id === variantId
          ? { ...variant, alreadySewn: res.data, loading: false }
          : variant
      ));
    } catch (err) {
      console.error("Error fetching already sewn quantities:", err);
      // If the endpoint doesn't exist yet, use a fallback of zeros
      setColorVariants(prev => prev.map(variant =>
        variant.id === variantId
          ? {
              ...variant,
              alreadySewn: { xs: 0, s: 0, m: 0, l: 0, xl: 0 },
              loading: false
            }
          : variant
      ));
    }
  };

  // Function to add a new color variant
  const addColorVariant = () => {
    setColorVariants(prev => [...prev, {
      id: Date.now(),
      selectedColor: "",
      alreadySewn: null,
      xs: 0,
      s: 0,
      m: 0,
      l: 0,
      xl: 0,
      damageCount: 0,
      loading: false
    }]);
  };

  // Function to remove a color variant
  const removeColorVariant = (variantId) => {
    if (colorVariants.length > 1) {
      setColorVariants(prev => prev.filter(variant => variant.id !== variantId));
    }
  };

  // Function to update a specific variant's field
  const updateVariant = (variantId, field, value) => {
    setColorVariants(prev => prev.map(variant =>
      variant.id === variantId
        ? { ...variant, [field]: value }
        : variant
    ));
  };

  // Function to handle color selection for a variant
  const handleColorSelection = (variantId, colorValue) => {
    updateVariant(variantId, 'selectedColor', colorValue);
    if (colorValue) {
      fetchAlreadySewn(variantId, colorValue);
    } else {
      updateVariant(variantId, 'alreadySewn', null);
    }
  };

  // Check if form is valid
  useEffect(() => {
    const hasProduct = !!selectedProduct;

    // Check if all variants are valid
    const allVariantsValid = colorVariants.every(variant => {
      const hasColor = !!variant.selectedColor;
      const hasSizes = parseInt(variant.xs || 0) > 0 ||
                      parseInt(variant.s || 0) > 0 ||
                      parseInt(variant.m || 0) > 0 ||
                      parseInt(variant.l || 0) > 0 ||
                      parseInt(variant.xl || 0) > 0;

      // Calculate total sewn for validation
      const totalSewnItems = parseInt(variant.xs || 0) + parseInt(variant.s || 0) +
                            parseInt(variant.m || 0) + parseInt(variant.l || 0) +
                            parseInt(variant.xl || 0);
      const isDamageValid = parseInt(variant.damageCount || 0) <= totalSewnItems;

      // Check if total sewn + damage exceeds available quantity
      let isTotalValid = true;
      if (variant.selectedColor && variant.alreadySewn) {
        const option = productColors.find(opt => opt.value === variant.selectedColor);
        if (option) {
          const totalAvailable = option.totalCut -
            (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) -
            (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) -
            (variant.alreadySewn.xl || 0) - (variant.alreadySewn.damage_count || 0);
          isTotalValid = (totalSewnItems + parseInt(variant.damageCount || 0)) <= totalAvailable;
        }
      }

      return hasColor && hasSizes && isDamageValid && isTotalValid;
    });

    setFormValid(hasProduct && allVariantsValid && colorVariants.length > 0);
  }, [selectedProduct, colorVariants, productColors]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage("");
    setIsSubmitting(true);

    if (!selectedProduct) {
      setMessage("Please select a Product.");
      setIsSubmitting(false);
      return;
    }

    // Validate all variants
    for (let i = 0; i < colorVariants.length; i++) {
      const variant = colorVariants[i];

      if (!variant.selectedColor) {
        setMessage(`Please select a color for variant ${i + 1}.`);
        setIsSubmitting(false);
        return;
      }

      const selectedOption = productColors.find(
        (opt) => opt.value === variant.selectedColor
      );

      if (!selectedOption) {
        setMessage(`Selected color details not found for variant ${i + 1}.`);
        setIsSubmitting(false);
        return;
      }

      // Validate each size individually for this variant
      const parsedXs = parseInt(variant.xs || 0);
      const parsedS = parseInt(variant.s || 0);
      const parsedM = parseInt(variant.m || 0);
      const parsedL = parseInt(variant.l || 0);
      const parsedXl = parseInt(variant.xl || 0);
      const parsedDamage = parseInt(variant.damageCount || 0);

      // Check for negative values
      if (parsedXs < 0 || parsedS < 0 || parsedM < 0 || parsedL < 0 || parsedXl < 0 || parsedDamage < 0) {
        setMessage(`All quantities must be non-negative values for variant ${i + 1}.`);
        setIsSubmitting(false);
        return;
      }

      // Validate that damage count doesn't exceed total sewn items
      const totalSewnItems = parsedXs + parsedS + parsedM + parsedL + parsedXl;
      if (parsedDamage > totalSewnItems) {
        setMessage(`Damage count (${parsedDamage}) cannot exceed the total number of sewn items (${totalSewnItems}) for variant ${i + 1}.`);
        setIsSubmitting(false);
        return;
      }

      // Check individual size limits with already sewn quantities
      if (!variant.alreadySewn) {
        setMessage(`Unable to validate quantities for variant ${i + 1}. Please try again.`);
        setIsSubmitting(false);
        return;
      }

      const alreadySewnXs = variant.alreadySewn.xs || 0;
      const alreadySewnS = variant.alreadySewn.s || 0;
      const alreadySewnM = variant.alreadySewn.m || 0;
      const alreadySewnL = variant.alreadySewn.l || 0;
      const alreadySewnXl = variant.alreadySewn.xl || 0;

      if (parsedXs + alreadySewnXs > selectedOption.xs_cut) {
        setMessage(`XS quantity (${parsedXs}) exceeds the available quantity (${selectedOption.xs_cut - alreadySewnXs}) for variant ${i + 1}. Already sewn: ${alreadySewnXs}`);
        setIsSubmitting(false);
        return;
      }
      if (parsedS + alreadySewnS > selectedOption.s_cut) {
        setMessage(`S quantity (${parsedS}) exceeds the available quantity (${selectedOption.s_cut - alreadySewnS}) for variant ${i + 1}. Already sewn: ${alreadySewnS}`);
        setIsSubmitting(false);
        return;
      }
      if (parsedM + alreadySewnM > selectedOption.m_cut) {
        setMessage(`M quantity (${parsedM}) exceeds the available quantity (${selectedOption.m_cut - alreadySewnM}) for variant ${i + 1}. Already sewn: ${alreadySewnM}`);
        setIsSubmitting(false);
        return;
      }
      if (parsedL + alreadySewnL > selectedOption.l_cut) {
        setMessage(`L quantity (${parsedL}) exceeds the available quantity (${selectedOption.l_cut - alreadySewnL}) for variant ${i + 1}. Already sewn: ${alreadySewnL}`);
        setIsSubmitting(false);
        return;
      }
      if (parsedXl + alreadySewnXl > selectedOption.xl_cut) {
        setMessage(`XL quantity (${parsedXl}) exceeds the available quantity (${selectedOption.xl_cut - alreadySewnXl}) for variant ${i + 1}. Already sewn: ${alreadySewnXl}`);
        setIsSubmitting(false);
        return;
      }

      const newDailyTotal = parsedXs + parsedS + parsedM + parsedL + parsedXl;
      const alreadySewnTotal = alreadySewnXs + alreadySewnS + alreadySewnM + alreadySewnL + alreadySewnXl;

      if (newDailyTotal + alreadySewnTotal > selectedOption.totalCut) {
        setMessage(`The total sewing count (${newDailyTotal}) exceeds the available quantity (${selectedOption.totalCut - alreadySewnTotal}) for variant ${i + 1}. Already sewn: ${alreadySewnTotal}`);
        setIsSubmitting(false);
        return;
      }

      // Validate that daily sewing count + damage count doesn't exceed available fabric variant count
      const totalAlreadyDamage = variant.alreadySewn.damage_count || 0;
      const totalAvailable = selectedOption.totalCut - alreadySewnTotal - totalAlreadyDamage;

      // Check if daily sewing + damage exceeds available
      if (newDailyTotal + parsedDamage > totalAvailable) {
        setMessage(`The total of sewn items (${newDailyTotal}) plus damage count (${parsedDamage}) exceeds the available quantity (${totalAvailable}) for variant ${i + 1}.`);
        setIsSubmitting(false);
        return;
      }
    }

    // Submit all variants
    try {
      const promises = colorVariants.map(variant => {
        const payload = {
          cutting_record_fabric: variant.selectedColor,
          xs: parseInt(variant.xs || 0),
          s: parseInt(variant.s || 0),
          m: parseInt(variant.m || 0),
          l: parseInt(variant.l || 0),
          xl: parseInt(variant.xl || 0),
          damage_count: parseInt(variant.damageCount || 0),
        };
        return axios.post("http://localhost:8000/api/sewing/daily-records/", payload);
      });

      await Promise.all(promises);

      setMessage(`✅ Daily sewing records added successfully for ${colorVariants.length} color variant${colorVariants.length > 1 ? 's' : ''}!`);
      setSelectedProduct("");
      setProductColors([]);
      setColorVariants([{
        id: Date.now(),
        selectedColor: "",
        alreadySewn: null,
        xs: 0,
        s: 0,
        m: 0,
        l: 0,
        xl: 0,
        damageCount: 0,
        loading: false
      }]);
      setIsSubmitting(false);
    } catch (err) {
      console.error("Error adding daily sewing records:", err);
      let errorMessage = "Error adding daily sewing records.";
      if (err.response?.data) {
        errorMessage =
          typeof err.response.data === "object"
            ? Object.values(err.response.data).flat().join("\n")
            : err.response.data;
      }
      setMessage(errorMessage);
      setIsSubmitting(false);
    }
  };

  const ColourOption = ({ data, innerRef, innerProps }) => (
    <div
      ref={innerRef}
      {...innerProps}
      style={{ display: "flex", alignItems: "center", padding: "8px" }}
    >
      <div
        style={{
          width: 20,
          height: 20,
          backgroundColor: data.color,
          marginRight: 10,
          border: "1px solid #ccc",
          borderRadius: "4px"
        }}
      />
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span style={{ fontWeight: "500", fontSize: "14px" }}>
          {data.fabricName}
        </span>
        <span style={{ fontSize: "12px", color: "#666" }}>
          {data.colorName}
        </span>
      </div>
    </div>
  );

  // Calculate remaining quantities for a specific variant
  const getRemainingQuantities = (variant) => {
    if (!variant.selectedColor || !variant.alreadySewn) return null;

    const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);
    if (!selectedOption) return null;

    // Calculate total available after accounting for already sewn and damage
    const totalCut = selectedOption.totalCut;
    const totalAlreadySewn = (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +
                            (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +
                            (variant.alreadySewn.xl || 0);
    const totalAlreadyDamaged = variant.alreadySewn.damage_count || 0;
    const totalAvailable = totalCut - totalAlreadySewn - totalAlreadyDamaged;

    // Calculate remaining per size
    return {
      xs: selectedOption.xs_cut - (variant.alreadySewn.xs || 0) - parseInt(variant.xs || 0),
      s: selectedOption.s_cut - (variant.alreadySewn.s || 0) - parseInt(variant.s || 0),
      m: selectedOption.m_cut - (variant.alreadySewn.m || 0) - parseInt(variant.m || 0),
      l: selectedOption.l_cut - (variant.alreadySewn.l || 0) - parseInt(variant.l || 0),
      xl: selectedOption.xl_cut - (variant.alreadySewn.xl || 0) - parseInt(variant.xl || 0),
      total: totalAvailable - parseInt(variant.xs || 0) - parseInt(variant.s || 0) -
             parseInt(variant.m || 0) - parseInt(variant.l || 0) - parseInt(variant.xl || 0) -
             parseInt(variant.damageCount || 0)
    };
  };

  // Helper functions for variant validation
  const getVariantTotalSewn = (variant) => {
    return parseInt(variant.xs || 0) + parseInt(variant.s || 0) + parseInt(variant.m || 0) +
           parseInt(variant.l || 0) + parseInt(variant.xl || 0);
  };

  const isVariantDamageExceeded = (variant) => {
    return parseInt(variant.damageCount || 0) > getVariantTotalSewn(variant);
  };

  const isVariantTotalExceeded = (variant) => {
    const selectedColorOption = productColors.find(opt => opt.value === variant.selectedColor);
    return selectedColorOption && variant.alreadySewn ?
      (getVariantTotalSewn(variant) + parseInt(variant.damageCount || 0)) >
      (selectedColorOption.totalCut - (variant.alreadySewn.xs || 0) - (variant.alreadySewn.s || 0) -
       (variant.alreadySewn.m || 0) - (variant.alreadySewn.l || 0) - (variant.alreadySewn.xl || 0) -
       (variant.alreadySewn.damage_count || 0))
      : false;
  };

  return (
    <>
      <RoleBasedNavBar />
      <div
        style={{
          marginLeft: isSidebarOpen ? "240px" : "70px",
          width: `calc(100% - ${isSidebarOpen ? "240px" : "70px"})`,
          transition: "all 0.3s ease",
          padding: "20px"
        }}
      >
        <h2 className="mb-4">
          <FaTshirt className="me-2" />
          Add Daily Sewing Record
        </h2>

        {message && message.startsWith("✅") && (
          <Alert
            variant="success"
            className="d-flex align-items-center"
          >
            <FaCheck className="me-2" size={20} />
            <div>{message}</div>
          </Alert>
        )}

        {message && !message.startsWith("✅") && (
          <Alert
            variant="danger"
            className="d-flex align-items-center"
          >
            <FaExclamationTriangle className="me-2" size={20} />
            <div>{message}</div>
          </Alert>
        )}

        <Card className="mb-4 shadow-sm" style={{ backgroundColor: "#D9EDFB", borderRadius: "10px" }}>
          <Card.Body>
            <Form noValidate onSubmit={handleSubmit}>
              <Row>
                <Col md={12}>
                  <Form.Group className="mb-3">
                    <Form.Label><strong>Product (Cutting Record)</strong></Form.Label>
                    <Form.Select
                      value={selectedProduct}
                      onChange={(e) => setSelectedProduct(e.target.value)}
                      className="form-control shadow-sm"
                      style={{
                        borderRadius: "8px",
                        padding: "10px",
                        transition: "all 0.2s ease"
                      }}
                    >
                      <option value="">Select Product</option>
                      {products.map((prod) => (
                        <option key={prod.id} value={prod.id}>
                          {prod.product_name ||
                            `${prod.fabric_definition_data?.fabric_name} cut on ${prod.cutting_date}`}
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Text className="text-muted">
                      Select the product from cutting records
                    </Form.Text>
                  </Form.Group>
                </Col>
              </Row>

              {/* Color Variants Section */}
              {selectedProduct && (
                <div className="mt-4">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h4 className="mb-0">Color Variants</h4>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={addColorVariant}
                      className="d-flex align-items-center"
                    >
                      <FaPlus className="me-1" />
                      More Variant
                    </Button>
                  </div>

                  {colorVariants.map((variant, index) => (
                    <Card key={variant.id} className="mb-3 border" style={{ backgroundColor: "#f8f9fa" }}>
                      <Card.Header className="d-flex justify-content-between align-items-center bg-light">
                        <h6 className="mb-0">Variant {index + 1}</h6>
                        {colorVariants.length > 1 && (
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => removeColorVariant(variant.id)}
                          >
                            <FaTrash />
                          </Button>
                        )}
                      </Card.Header>
                      <Card.Body>
                        <Row>
                          <Col md={12}>
                            <Form.Group className="mb-3">
                              <Form.Label><strong>Color</strong></Form.Label>
                              <Select
                                options={productColors}
                                components={{ Option: ColourOption }}
                                value={
                                  productColors.find((opt) => opt.value === variant.selectedColor) || null
                                }
                                onChange={(opt) => handleColorSelection(variant.id, opt?.value)}
                                placeholder="Select Color"
                                isDisabled={!selectedProduct}
                                styles={{
                                  control: (provided) => ({
                                    ...provided,
                                    borderColor: "#ddd",
                                    boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
                                    borderRadius: "8px",
                                    "&:hover": { borderColor: "#aaa" },
                                    padding: "5px",
                                    transition: "all 0.2s ease"
                                  }),
                                  option: (provided, state) => ({
                                    ...provided,
                                    backgroundColor: state.isSelected ? "#0d6efd" : state.isFocused ? "#e9ecef" : "white",
                                    color: state.isSelected ? "white" : "#333",
                                    cursor: "pointer"
                                  })
                                }}
                              />
                              <Form.Text className="text-muted">
                                Select the color variant for this entry
                              </Form.Text>
                            </Form.Group>
                          </Col>
                        </Row>

                        {variant.selectedColor && (
                          <Card className="mb-3 mt-3 border-0 shadow-sm">
                            <Card.Header className="bg-light">
                              <div className="d-flex align-items-center">
                                <FaClipboardCheck className="me-2 text-primary" />
                                <h6 className="mb-0">Available Quantities</h6>
                              </div>
                            </Card.Header>
                            <Card.Body>
                              {variant.loading ? (
                                <div className="text-center py-3">
                                  <Spinner animation="border" variant="primary" className="me-2" />
                                  <span className="text-muted">Loading quantities...</span>
                                </div>
                              ) : (
                                <>
                                  <div className="table-responsive">
                                    <table className="table table-hover table-sm">
                                      <thead className="table-light">
                                        <tr>
                                          <th className="text-center">Size</th>
                                          <th className="text-center">Cut</th>
                                          <th className="text-center">Already Sewn</th>
                                          <th className="text-center">Available</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {["XS", "S", "M", "L", "XL"].map((size) => {
                                          const selectedOption = productColors.find(
                                            (opt) => opt.value === variant.selectedColor
                                          );
                                          const sizeKey = size.toLowerCase();
                                          const cutKey = `${sizeKey}_cut`;
                                          const alreadySewnQty = variant.alreadySewn ? variant.alreadySewn[sizeKey] || 0 : 0;
                                          const availableQty = selectedOption ?
                                            Math.max(0, selectedOption[cutKey] - alreadySewnQty) : 0;

                                          return (
                                            <tr key={`avail-${variant.id}-${size}`}>
                                              <td className="text-center">
                                                <Badge bg="secondary" className="px-2 py-1" style={{ fontSize: "0.75rem" }}>{size}</Badge>
                                              </td>
                                              <td className="text-center">
                                                <Badge bg="secondary" pill className="px-2" style={{ fontSize: "0.75rem" }}>
                                                  {selectedOption ? selectedOption[cutKey] : 0}
                                                </Badge>
                                              </td>
                                              <td className="text-center">
                                                <Badge bg="info" pill className="px-2" style={{ fontSize: "0.75rem" }}>
                                                  {alreadySewnQty}
                                                </Badge>
                                              </td>
                                              <td className="text-center">
                                                <Badge
                                                  bg={availableQty > 0 ? 'success' : 'danger'}
                                                  pill
                                                  className="px-2"
                                                  style={{ fontSize: "0.75rem" }}
                                                >
                                                  {availableQty}
                                                </Badge>
                                              </td>
                                            </tr>
                                          );
                                        })}

                                        {/* Add a row for damage count */}
                                        <tr className="table-light">
                                          <td className="text-center">
                                            <Badge bg="warning" text="dark" className="px-2 py-1" style={{ fontSize: "0.75rem" }}>Damage</Badge>
                                          </td>
                                          <td className="text-center">-</td>
                                          <td className="text-center">
                                            <Badge bg="warning" text="dark" pill className="px-2" style={{ fontSize: "0.75rem" }}>
                                              {variant.alreadySewn ? variant.alreadySewn.damage_count || 0 : 0}
                                            </Badge>
                                          </td>
                                          <td className="text-center">-</td>
                                        </tr>

                                        {/* Add a row for total */}
                                        <tr className="table-primary">
                                          <td className="text-center">
                                            <strong style={{ fontSize: "0.8rem" }}>TOTAL</strong>
                                          </td>
                                          <td className="text-center">
                                            <Badge bg="primary" pill className="px-2" style={{ fontSize: "0.75rem" }}>
                                              {(() => {
                                                const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);
                                                return selectedOption ? selectedOption.totalCut : 0;
                                              })()}
                                            </Badge>
                                          </td>
                                          <td className="text-center">
                                            <Badge bg="primary" pill className="px-2" style={{ fontSize: "0.75rem" }}>
                                              {variant.alreadySewn ?
                                                (variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +
                                                (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +
                                                (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0) : 0}
                                            </Badge>
                                          </td>
                                          <td className="text-center">
                                            {(() => {
                                              const selectedOption = productColors.find(opt => opt.value === variant.selectedColor);
                                              const remainingQuantities = getRemainingQuantities(variant);
                                              return selectedOption && variant.alreadySewn && (
                                                <Badge
                                                  bg={remainingQuantities && remainingQuantities.total >= 0 ? 'success' : 'danger'}
                                                  pill
                                                  className="px-2"
                                                  style={{ fontSize: "0.75rem" }}
                                                >
                                                  {Math.max(0, selectedOption.totalCut -
                                                    ((variant.alreadySewn.xs || 0) + (variant.alreadySewn.s || 0) +
                                                    (variant.alreadySewn.m || 0) + (variant.alreadySewn.l || 0) +
                                                    (variant.alreadySewn.xl || 0) + (variant.alreadySewn.damage_count || 0)))}
                                                </Badge>
                                              );
                                            })()}
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>

                                  <div className="mt-2 p-2 bg-light rounded border">
                                    <small className="text-muted d-block mb-1" style={{ fontSize: "0.7rem" }}>
                                      <FaInfoCircle className="me-1" />
                                      Available quantities for sewing in each size.
                                    </small>
                                    <small className="text-muted d-block" style={{ fontSize: "0.7rem" }}>
                                      <strong>Important:</strong> Total sewn + damage cannot exceed available quantity.
                                    </small>
                                  </div>
                                </>
                              )}
                            </Card.Body>
                          </Card>
                        )}

                        <h6 className="mt-3 mb-2">Size Quantities</h6>
                        <Row className="mb-3">
                          {["XS", "S", "M", "L", "XL"].map((size) => {
                            const sizeKey = size.toLowerCase();
                            const remainingQuantities = getRemainingQuantities(variant);
                            const isExceeded = remainingQuantities && remainingQuantities[sizeKey] < 0;

                            return (
                              <Col key={`${variant.id}-${size}`} xs={6} sm={4} md={2} className="mb-2">
                                <Form.Group>
                                  <Form.Label className="text-center d-block" style={{ fontSize: "0.8rem" }}>{size}</Form.Label>
                                  <Form.Control
                                    type="number"
                                    min="0"
                                    size="sm"
                                    value={variant[sizeKey]}
                                    onChange={(e) => {
                                      // Ensure value is not negative
                                      const val = Math.max(0, parseInt(e.target.value || 0));
                                      updateVariant(variant.id, sizeKey, val);
                                    }}
                                    className={`text-center ${isExceeded ? 'border-danger' : ''}`}
                                    disabled={!variant.selectedColor}
                                  />
                                  {isExceeded && (
                                    <div className="text-danger small mt-1 text-center" style={{ fontSize: "0.7rem" }}>
                                      <FaExclamationTriangle className="me-1" size={10} />
                                      Exceeds limit
                                    </div>
                                  )}
                                </Form.Group>
                              </Col>
                            );
                          })}

                          <Col xs={6} sm={4} md={2} className="mb-2">
                            <Form.Group>
                              <Form.Label className="text-center d-block" style={{ fontSize: "0.8rem" }}>Damage</Form.Label>
                              <Form.Control
                                type="number"
                                min="0"
                                size="sm"
                                value={variant.damageCount}
                                onChange={(e) => {
                                  // Ensure value is not negative
                                  const val = Math.max(0, parseInt(e.target.value || 0));
                                  updateVariant(variant.id, 'damageCount', val);
                                }}
                                className={`text-center ${isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? 'border-danger' : ''}`}
                                disabled={!variant.selectedColor}
                              />
                              {isVariantDamageExceeded(variant) && (
                                <div className="text-danger small mt-1 text-center" style={{ fontSize: "0.7rem" }}>
                                  <FaExclamationTriangle className="me-1" size={10} />
                                  Exceeds total sewn
                                </div>
                              )}
                              {!isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && (
                                <div className="text-danger small mt-1 text-center" style={{ fontSize: "0.7rem" }}>
                                  <FaExclamationTriangle className="me-1" size={10} />
                                  Exceeds available
                                </div>
                              )}
                            </Form.Group>
                          </Col>
                        </Row>

                        {/* Variant Summary */}
                        {getVariantTotalSewn(variant) > 0 && (
                          <Card className="border-0 mb-3" style={{ backgroundColor: "#e8f4fe" }}>
                            <Card.Body className="py-2">
                              <div className="d-flex flex-column">
                                <div className="d-flex align-items-center mb-2">
                                  <strong className="me-2" style={{ fontSize: "0.8rem" }}>Total Quantities:</strong>
                                  <Badge bg="primary" className="me-1" style={{ fontSize: "0.7rem" }}>XS: {variant.xs}</Badge>
                                  <Badge bg="primary" className="me-1" style={{ fontSize: "0.7rem" }}>S: {variant.s}</Badge>
                                  <Badge bg="primary" className="me-1" style={{ fontSize: "0.7rem" }}>M: {variant.m}</Badge>
                                  <Badge bg="primary" className="me-1" style={{ fontSize: "0.7rem" }}>L: {variant.l}</Badge>
                                  <Badge bg="primary" className="me-1" style={{ fontSize: "0.7rem" }}>XL: {variant.xl}</Badge>
                                  <Badge bg="success" className="ms-2" style={{ fontSize: "0.7rem" }}>Total: {getVariantTotalSewn(variant)}</Badge>
                                </div>
                                {variant.damageCount > 0 && (
                                  <div className="d-flex align-items-center">
                                    <strong className="me-2" style={{ fontSize: "0.8rem" }}>Damage Count:</strong>
                                    <Badge bg={isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? "danger" : "warning"}
                                           text={isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant) ? "white" : "dark"}
                                           style={{ fontSize: "0.7rem" }}>
                                      {variant.damageCount} {(isVariantDamageExceeded(variant) || isVariantTotalExceeded(variant)) && <FaExclamationTriangle className="ms-1" size={10} />}
                                    </Badge>
                                    <strong className="mx-2" style={{ fontSize: "0.8rem" }}>Good Items:</strong>
                                    <Badge bg="success" style={{ fontSize: "0.7rem" }}>{Math.max(0, getVariantTotalSewn(variant) - variant.damageCount)}</Badge>
                                    {isVariantDamageExceeded(variant) && (
                                      <span className="text-danger ms-2 small" style={{ fontSize: "0.7rem" }}>
                                        <FaExclamationTriangle className="me-1" />
                                        Damage count cannot exceed total sewn items
                                      </span>
                                    )}
                                    {!isVariantDamageExceeded(variant) && isVariantTotalExceeded(variant) && (
                                      <span className="text-danger ms-2 small" style={{ fontSize: "0.7rem" }}>
                                        <FaExclamationTriangle className="me-1" />
                                        Total sewn + damage exceeds available quantity
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>
                            </Card.Body>
                          </Card>
                        )}
                      </Card.Body>
                    </Card>
                  ))}
                </div>
              )}

              <div className="d-flex justify-content-center mt-4">
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  disabled={!formValid || isSubmitting}
                  className="px-5"
                >
                  {isSubmitting ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
                      Submitting...
                    </>
                  ) : (
                    `Submit Daily Sewing Records (${colorVariants.length} variant${colorVariants.length > 1 ? 's' : ''})`
                  )}
                </Button>
              </div>

              {!formValid && (
                <div className="text-center mt-3">
                  <small className="text-muted">
                    <FaInfoCircle className="me-1" />
                    {!selectedProduct ? "Please select a product" :
                     colorVariants.some(v => !v.selectedColor) ? "Please select colors for all variants" :
                     colorVariants.every(v => getVariantTotalSewn(v) === 0) ? "Please enter at least one size quantity in any variant" :
                     colorVariants.some(v => isVariantDamageExceeded(v)) ? "Some variants have damage count exceeding total sewn items" :
                     colorVariants.some(v => isVariantTotalExceeded(v)) ? "Some variants exceed available quantities" :
                     "Please check your inputs"}
                  </small>
                </div>
              )}
            </Form>
          </Card.Body>
        </Card>
      </div>
    </>
  );
};

export default AddDailySewingRecord;
