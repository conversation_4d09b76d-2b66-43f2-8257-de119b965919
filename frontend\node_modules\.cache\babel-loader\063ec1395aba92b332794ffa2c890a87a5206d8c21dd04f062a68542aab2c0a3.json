{"ast": null, "code": "var _jsxFileName = \"D:\\\\Pri Fashion Software\\\\Pri_Fashion_\\\\frontend\\\\src\\\\pages\\\\CuttingRecordDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\nimport { Container, Row, Col, Card, Table, Badge, Spinner, Alert, Button, ListGroup, Modal } from 'react-bootstrap';\nimport { FaArrowLeft, FaCalendarAlt, FaCut, FaTshirt, FaInfoCircle, FaRulerHorizontal, FaClipboardList, FaMoneyBillWave, FaDownload, FaFilePdf, FaCheck, FaTimes } from 'react-icons/fa';\nimport { jsPDF } from 'jspdf';\nimport autoTable from 'jspdf-autotable';\n\n// We'll use a different approach for the logo\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CuttingRecordDetail = () => {\n  _s();\n  var _cuttingRecord$fabric, _cuttingRecord$detail3;\n  const {\n    recordId\n  } = useParams();\n  const navigate = useNavigate();\n  const [cuttingRecord, setCuttingRecord] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [returnPath, setReturnPath] = useState('');\n  const [showPdfModal, setShowPdfModal] = useState(false);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Get the return path from localStorage if available\n  useEffect(() => {\n    const savedReturnPath = localStorage.getItem('cuttingRecordReturnPath');\n    if (savedReturnPath) {\n      setReturnPath(savedReturnPath);\n    } else {\n      // Default to the cutting records list\n      setReturnPath('/viewcutting');\n    }\n\n    // Clean up the localStorage when component unmounts\n    return () => {\n      localStorage.removeItem('cuttingRecordReturnPath');\n    };\n  }, []);\n\n  // Fetch cutting record data\n  useEffect(() => {\n    setLoading(true);\n    axios.get(`http://localhost:8000/api/cutting/records/${recordId}/`).then(response => {\n      setCuttingRecord(response.data);\n      setLoading(false);\n    }).catch(error => {\n      console.error(\"Error fetching cutting record details:\", error);\n      setError(\"Failed to load cutting record details.\");\n      setLoading(false);\n    });\n  }, [recordId]);\n\n  // Format date for display\n  const formatDate = dateString => {\n    const options = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    return new Date(dateString).toLocaleDateString(undefined, options);\n  };\n\n  // Calculate total pieces for a detail\n  const calculateTotalPieces = detail => {\n    return detail.xs + detail.s + detail.m + detail.l + detail.xl;\n  };\n\n  // Calculate total pieces for all details\n  const calculateTotalAllPieces = () => {\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\n    return cuttingRecord.details.reduce((total, detail) => {\n      return total + calculateTotalPieces(detail);\n    }, 0);\n  };\n\n  // Calculate total yard usage\n  const calculateTotalYardUsage = () => {\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\n    return cuttingRecord.details.reduce((total, detail) => {\n      return total + parseFloat(detail.yard_usage);\n    }, 0).toFixed(2);\n  };\n\n  // Calculate fabric cutting value (cost)\n  const calculateCuttingValue = detail => {\n    if (!detail || !detail.fabric_variant_data) return 0;\n    const yardUsage = parseFloat(detail.yard_usage);\n    const pricePerYard = parseFloat(detail.fabric_variant_data.price_per_yard);\n    return (yardUsage * pricePerYard).toFixed(2);\n  };\n\n  // Calculate total cutting value\n  const calculateTotalCuttingValue = () => {\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\n    return cuttingRecord.details.reduce((total, detail) => {\n      if (!detail.fabric_variant_data) return total;\n      const value = parseFloat(detail.yard_usage) * parseFloat(detail.fabric_variant_data.price_per_yard);\n      return total + value;\n    }, 0).toFixed(2);\n  };\n\n  // Handle back button click\n  const handleBack = () => {\n    navigate(returnPath);\n  };\n\n  // Open PDF confirmation modal\n  const openPdfModal = () => {\n    setShowPdfModal(true);\n  };\n\n  // Close PDF confirmation modal\n  const closePdfModal = () => {\n    setShowPdfModal(false);\n  };\n\n  // Generate PDF report\n  const generatePDF = () => {\n    if (!cuttingRecord) return;\n    try {\n      var _cuttingRecord$detail, _cuttingRecord$detail2;\n      const productName = cuttingRecord.product_name || \"N/A\";\n      // Get fabric names from details\n      const fabricNames = new Set();\n      (_cuttingRecord$detail = cuttingRecord.details) === null || _cuttingRecord$detail === void 0 ? void 0 : _cuttingRecord$detail.forEach(detail => {\n        var _detail$fabric_varian, _detail$fabric_varian2;\n        if ((_detail$fabric_varian = detail.fabric_variant_data) !== null && _detail$fabric_varian !== void 0 && (_detail$fabric_varian2 = _detail$fabric_varian.fabric_definition_data) !== null && _detail$fabric_varian2 !== void 0 && _detail$fabric_varian2.fabric_name) {\n          fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\n        }\n      });\n      const fabricName = Array.from(fabricNames).join(', ') || \"N/A\";\n\n      // Create PDF document with orientation and unit specifications\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Add the actual logo from the public directory\n      try {\n        // Get the base URL for the current environment\n        const baseUrl = window.location.origin;\n\n        // Add the logo to the PDF\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\n      } catch (logoError) {\n        console.warn(\"Could not add logo to PDF:\", logoError);\n\n        // Fallback to a simple placeholder if the logo can't be loaded\n        doc.setFillColor(41, 128, 185); // Primary blue color\n        doc.rect(14, 10, 20, 20, 'F');\n\n        // Add \"PF\" text as a simple logo\n        doc.setFontSize(14);\n        doc.setTextColor(255, 255, 255);\n        doc.text(\"PF\", 24, 22, {\n          align: 'center'\n        });\n      }\n\n      // Reset text color for the rest of the document\n      doc.setTextColor(0, 0, 0);\n\n      // Add title and company info\n      doc.setFontSize(20);\n      doc.setTextColor(0, 0, 0);\n      doc.text(\"Cutting Record Report\", 105, 20, {\n        align: 'center'\n      });\n      doc.setFontSize(12);\n      doc.text(\"Pri Fashion - Garment Management System\", 105, 28, {\n        align: 'center'\n      });\n      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 34, {\n        align: 'center'\n      });\n\n      // Add cutting record details\n      doc.setFontSize(14);\n      doc.text(\"Cutting Record Details\", 14, 45);\n      doc.setFontSize(10);\n      const details = [[\"Product Name:\", productName], [\"Fabric Name:\", fabricName], [\"Cutting Date:\", cuttingRecord.cutting_date], [\"Total Quantity:\", calculateTotalAllPieces().toString()], [\"Total Yard Usage:\", calculateTotalYardUsage().toString()], [\"Color Variants Used:\", ((_cuttingRecord$detail2 = cuttingRecord.details) === null || _cuttingRecord$detail2 === void 0 ? void 0 : _cuttingRecord$detail2.length.toString()) || \"0\"], [\"Total Fabric Cost:\", `Rs. ${calculateTotalCuttingValue()}`]];\n\n      // First table\n      let finalY = 50;\n      autoTable(doc, {\n        startY: finalY,\n        head: [[\"Property\", \"Value\"]],\n        body: details,\n        theme: 'grid',\n        headStyles: {\n          fillColor: [41, 128, 185],\n          textColor: 255\n        },\n        styles: {\n          fontSize: 10\n        }\n      });\n\n      // Get the final Y position after the first table\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 10;\n\n      // Add color usage details\n      doc.setFontSize(14);\n      doc.text(\"Color Usage Details\", 14, finalY);\n      if (cuttingRecord.details && cuttingRecord.details.length > 0) {\n        const colorDetails = cuttingRecord.details.map(detail => {\n          var _detail$fabric_varian3, _detail$fabric_varian4, _detail$fabric_varian5;\n          // Get the color code for display\n          const colorCode = ((_detail$fabric_varian3 = detail.fabric_variant_data) === null || _detail$fabric_varian3 === void 0 ? void 0 : _detail$fabric_varian3.color) || \"#CCCCCC\";\n          const colorName = ((_detail$fabric_varian4 = detail.fabric_variant_data) === null || _detail$fabric_varian4 === void 0 ? void 0 : _detail$fabric_varian4.color_name) || \"N/A\";\n          return [colorName, colorCode,\n          // Add color code as a separate column\n          `${parseFloat(detail.yard_usage).toFixed(2)} yards`, `Rs. ${(_detail$fabric_varian5 = detail.fabric_variant_data) === null || _detail$fabric_varian5 === void 0 ? void 0 : _detail$fabric_varian5.price_per_yard.toFixed(2)}`, `Rs. ${calculateCuttingValue(detail)}`, detail.xs || 0, detail.s || 0, detail.m || 0, detail.l || 0, detail.xl || 0, calculateTotalPieces(detail)];\n        });\n\n        // Second table with color swatches\n        autoTable(doc, {\n          startY: finalY + 5,\n          head: [[\"Color Name\", \"Color\", \"Yard Usage\", \"Price/Yard\", \"Fabric Cost\", \"XS\", \"S\", \"M\", \"L\", \"XL\", \"Total\"]],\n          body: colorDetails,\n          theme: 'grid',\n          headStyles: {\n            fillColor: [41, 128, 185],\n            textColor: 255\n          },\n          styles: {\n            fontSize: 9\n          },\n          columnStyles: {\n            0: {\n              cellWidth: 25\n            },\n            1: {\n              cellWidth: 15\n            },\n            // Color swatch column\n            2: {\n              cellWidth: 20\n            },\n            3: {\n              cellWidth: 20\n            },\n            4: {\n              cellWidth: 20\n            }\n          },\n          // Add color swatches to the color column\n          didDrawCell: data => {\n            if (data.section === 'body' && data.column.index === 1) {\n              const colorHex = data.cell.raw;\n              try {\n                // Convert hex color to RGB\n                const r = parseInt(colorHex.substring(1, 3), 16) || 0;\n                const g = parseInt(colorHex.substring(3, 5), 16) || 0;\n                const b = parseInt(colorHex.substring(5, 7), 16) || 0;\n\n                // Set fill color using RGB values\n                doc.setFillColor(r, g, b);\n\n                // Draw a color rectangle\n                doc.rect(data.cell.x + 2, data.cell.y + 2, data.cell.width - 4, data.cell.height - 4, 'F');\n\n                // Add a border around the color swatch\n                doc.setDrawColor(200, 200, 200);\n                doc.rect(data.cell.x + 2, data.cell.y + 2, data.cell.width - 4, data.cell.height - 4, 'S');\n              } catch (error) {\n                console.warn(\"Error drawing color swatch:\", error);\n                // Fallback to a gray color if there's an error\n                doc.setFillColor(200, 200, 200);\n                doc.rect(data.cell.x + 2, data.cell.y + 2, data.cell.width - 4, data.cell.height - 4, 'F');\n              }\n            }\n          }\n        });\n      } else {\n        doc.text(\"No color details available\", 14, finalY + 5);\n      }\n\n      // Get the final Y position after the second table\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 15;\n\n      // Add signature fields\n      doc.setFontSize(12);\n      doc.text(\"Signatures:\", 14, finalY);\n\n      // Draw signature lines\n      finalY += 8;\n\n      // Owner signature\n      doc.line(14, finalY + 15, 80, finalY + 15); // Signature line\n      doc.setFontSize(10);\n      doc.text(\"Owner Signature\", 14, finalY + 20);\n      doc.text(\"Date: ________________\", 14, finalY + 25);\n\n      // Cutter signature\n      doc.line(120, finalY + 15, 186, finalY + 15); // Signature line\n      doc.text(\"Cutter Signature\", 120, finalY + 20);\n      doc.text(\"Date: ________________\", 120, finalY + 25);\n\n      // Add footer\n      const pageCount = doc.internal.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setFontSize(8);\n        doc.text(`Page ${i} of ${pageCount} - Pri Fashion Garment Management System`, 105, doc.internal.pageSize.height - 10, {\n          align: 'center'\n        });\n      }\n\n      // Save the PDF with a clean filename\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\n      doc.save(`Cutting_Record_${cuttingRecord.id}_${cleanProductName}.pdf`);\n\n      // Close the modal\n      closePdfModal();\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      alert(\"Failed to generate PDF. Please try again: \" + error.message);\n      closePdfModal();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: handleBack,\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), \" Back\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), !loading && cuttingRecord && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-success\",\n          onClick: openPdfModal,\n          children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this), \" Download PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"text-center\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 19\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center my-5\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          role: \"status\",\n          variant: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2\",\n          children: \"Loading cutting record details...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this) : cuttingRecord && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4 shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaCut, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), \"Cutting Record Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: cuttingRecord.product_name || \"Unnamed Cutting Record\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Fabric:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-primary\",\n                    children: (_cuttingRecord$fabric = cuttingRecord.fabric_definition_data) === null || _cuttingRecord$fabric === void 0 ? void 0 : _cuttingRecord$fabric.fabric_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Cutting Date:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                      className: \"me-1 text-secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this), formatDate(cuttingRecord.cutting_date)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), cuttingRecord.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Description:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 25\n                  }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: cuttingRecord.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 bg-light\",\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"mb-3\",\n                      children: \"Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(ListGroup, {\n                      variant: \"flush\",\n                      children: [/*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(FaRulerHorizontal, {\n                            className: \"me-2 text-secondary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 433,\n                            columnNumber: 31\n                          }, this), \"Total Yard Usage\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"info\",\n                          pill: true,\n                          children: [calculateTotalYardUsage(), \" yards\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 436,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(FaTshirt, {\n                            className: \"me-2 text-secondary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 442,\n                            columnNumber: 31\n                          }, this), \"Total Pieces\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          pill: true,\n                          children: [calculateTotalAllPieces(), \" pcs\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(FaClipboardList, {\n                            className: \"me-2 text-secondary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 451,\n                            columnNumber: 31\n                          }, this), \"Color Variants Used\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 450,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"primary\",\n                          pill: true,\n                          children: ((_cuttingRecord$detail3 = cuttingRecord.details) === null || _cuttingRecord$detail3 === void 0 ? void 0 : _cuttingRecord$detail3.length) || 0\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 449,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(FaMoneyBillWave, {\n                            className: \"me-2 text-secondary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 460,\n                            columnNumber: 31\n                          }, this), \"Total Fabric Cost\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"warning\",\n                          text: \"dark\",\n                          pill: true,\n                          children: [\"Rs. \", calculateTotalCuttingValue()]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 463,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), \"Cutting Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-0\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              hover: true,\n              responsive: true,\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-light\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Yard Usage\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Price per Yard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Fabric Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"XS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"S\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"M\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"L\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"XL\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Total Pieces\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: cuttingRecord.details.map(detail => {\n                  var _detail$fabric_varian6, _detail$fabric_varian7, _detail$fabric_varian8;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '20px',\n                            height: '20px',\n                            backgroundColor: ((_detail$fabric_varian6 = detail.fabric_variant_data) === null || _detail$fabric_varian6 === void 0 ? void 0 : _detail$fabric_varian6.color) || '#ccc',\n                            borderRadius: '4px',\n                            border: '1px solid #dee2e6',\n                            marginRight: '10px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 506,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: ((_detail$fabric_varian7 = detail.fabric_variant_data) === null || _detail$fabric_varian7 === void 0 ? void 0 : _detail$fabric_varian7.color_name) || 'Unknown'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 516,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 505,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"info\",\n                        pill: true,\n                        children: [parseFloat(detail.yard_usage).toFixed(2), \" yards\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 520,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"secondary\",\n                        pill: true,\n                        children: [\"Rs. \", (_detail$fabric_varian8 = detail.fabric_variant_data) === null || _detail$fabric_varian8 === void 0 ? void 0 : _detail$fabric_varian8.price_per_yard.toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"warning\",\n                        text: \"dark\",\n                        pill: true,\n                        children: [\"Rs. \", calculateCuttingValue(detail)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: detail.xs > 0 ? detail.xs : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: detail.s > 0 ? detail.s : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: detail.m > 0 ? detail.m : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: detail.l > 0 ? detail.l : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: detail.xl > 0 ? detail.xl : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"success\",\n                        pill: true,\n                        children: [calculateTotalPieces(detail), \" pcs\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 25\n                    }, this)]\n                  }, detail.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tfoot\", {\n                className: \"bg-light\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: \"Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: [calculateTotalYardUsage(), \" yards\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: [\"Rs. \", calculateTotalCuttingValue()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: cuttingRecord.details.reduce((sum, detail) => sum + detail.xs, 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: cuttingRecord.details.reduce((sum, detail) => sum + detail.s, 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: cuttingRecord.details.reduce((sum, detail) => sum + detail.m, 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: cuttingRecord.details.reduce((sum, detail) => sum + detail.l, 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: cuttingRecord.details.reduce((sum, detail) => sum + detail.xl, 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"fw-bold\",\n                    children: [calculateTotalAllPieces(), \" pcs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: closePdfModal,\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"text-danger me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), \"Generate PDF Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Are you sure you want to generate a PDF report for this cutting record?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this), cuttingRecord && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-light p-3 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Product:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 35\n            }, this), \" \", cuttingRecord.product_name || \"N/A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Fabrics:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 35\n            }, this), \" \", (_cuttingRecord$detail4 => {\n              const fabricNames = new Set();\n              (_cuttingRecord$detail4 = cuttingRecord.details) === null || _cuttingRecord$detail4 === void 0 ? void 0 : _cuttingRecord$detail4.forEach(detail => {\n                var _detail$fabric_varian9, _detail$fabric_varian10;\n                if ((_detail$fabric_varian9 = detail.fabric_variant_data) !== null && _detail$fabric_varian9 !== void 0 && (_detail$fabric_varian10 = _detail$fabric_varian9.fabric_definition_data) !== null && _detail$fabric_varian10 !== void 0 && _detail$fabric_varian10.fabric_name) {\n                  fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\n                }\n              });\n              return Array.from(fabricNames).join(', ') || \"N/A\";\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 35\n            }, this), \" \", cuttingRecord.cutting_date]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Cost:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 35\n            }, this), \" Rs. \", calculateTotalCuttingValue()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: closePdfModal,\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this), \" Cancel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: generatePDF,\n          children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), \" Generate PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CuttingRecordDetail, \"7aFh8JlI5ZMItiHGxGz1AJ52ZLg=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = CuttingRecordDetail;\nexport default CuttingRecordDetail;\nvar _c;\n$RefreshReg$(_c, \"CuttingRecordDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useParams", "useNavigate", "RoleBasedNavBar", "Container", "Row", "Col", "Card", "Table", "Badge", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "ListGroup", "Modal", "FaArrowLeft", "FaCalendarAlt", "FaCut", "FaTshirt", "FaInfoCircle", "FaRulerHorizontal", "FaClipboardList", "FaMoneyBillWave", "FaDownload", "FaFilePdf", "FaCheck", "FaTimes", "jsPDF", "autoTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CuttingRecordDetail", "_s", "_cuttingRecord$fabric", "_cuttingRecord$detail3", "recordId", "navigate", "cutting<PERSON><PERSON>ord", "setCuttingRecord", "loading", "setLoading", "error", "setError", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "returnPath", "setReturnPath", "showPdfModal", "setShowPdfModal", "handleResize", "addEventListener", "removeEventListener", "savedReturnPath", "localStorage", "getItem", "removeItem", "get", "then", "response", "data", "catch", "console", "formatDate", "dateString", "options", "year", "month", "day", "Date", "toLocaleDateString", "undefined", "calculateTotalPieces", "detail", "xs", "s", "m", "l", "xl", "calculateTotalAllPieces", "details", "reduce", "total", "calculateTotalYardUsage", "parseFloat", "yard_usage", "toFixed", "calculateCuttingValue", "fabric_variant_data", "yardUsage", "pricePerYard", "price_per_yard", "calculateTotalCuttingValue", "value", "handleBack", "openPdfModal", "closePdfModal", "generatePDF", "_cuttingRecord$detail", "_cuttingRecord$detail2", "productName", "product_name", "fabricNames", "Set", "for<PERSON>ach", "_detail$fabric_varian", "_detail$fabric_varian2", "fabric_definition_data", "fabric_name", "add", "fabricName", "Array", "from", "join", "doc", "orientation", "unit", "format", "baseUrl", "location", "origin", "addImage", "logoError", "warn", "setFillColor", "rect", "setFontSize", "setTextColor", "text", "align", "cutting_date", "toString", "length", "finalY", "startY", "head", "body", "theme", "headStyles", "fillColor", "textColor", "styles", "fontSize", "lastAutoTable", "previousAutoTable", "colorDetails", "map", "_detail$fabric_varian3", "_detail$fabric_varian4", "_detail$fabric_varian5", "colorCode", "color", "colorName", "color_name", "columnStyles", "cellWidth", "didDrawCell", "section", "column", "index", "colorHex", "cell", "raw", "r", "parseInt", "substring", "g", "b", "x", "y", "width", "height", "setDrawColor", "line", "pageCount", "internal", "getNumberOfPages", "i", "setPage", "pageSize", "cleanProductName", "replace", "save", "id", "alert", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fluid", "style", "marginLeft", "transition", "padding", "className", "variant", "onClick", "animation", "role", "Header", "Body", "md", "description", "<PERSON><PERSON>", "bg", "pill", "hover", "responsive", "_detail$fabric_varian6", "_detail$fabric_varian7", "_detail$fabric_varian8", "backgroundColor", "borderRadius", "border", "marginRight", "sum", "show", "onHide", "centered", "closeButton", "Title", "_cuttingRecord$detail4", "_detail$fabric_varian9", "_detail$fabric_varian10", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/Pri Fashion Software/Pri_Fashion_/frontend/src/pages/CuttingRecordDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport RoleBasedNavBar from '../components/RoleBasedNavBar';\r\nimport {\r\n  Container, Row, Col, Card, Table, Badge, Spinner,\r\n  Alert, Button, ListGroup, Modal\r\n} from 'react-bootstrap';\r\nimport {\r\n  FaArrowLeft, FaCalendarAlt, FaCut, FaTshirt,\r\n  FaInfoCircle, FaRulerHorizontal, FaClipboardList,\r\n  FaMoneyBillWave, FaDownload, FaFilePdf, FaCheck, FaTimes\r\n} from 'react-icons/fa';\r\nimport { jsPDF } from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\n\r\n// We'll use a different approach for the logo\r\n\r\nconst CuttingRecordDetail = () => {\r\n  const { recordId } = useParams();\r\n  const navigate = useNavigate();\r\n  const [cuttingRecord, setCuttingRecord] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [returnPath, setReturnPath] = useState('');\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Get the return path from localStorage if available\r\n  useEffect(() => {\r\n    const savedReturnPath = localStorage.getItem('cuttingRecordReturnPath');\r\n    if (savedReturnPath) {\r\n      setReturnPath(savedReturnPath);\r\n    } else {\r\n      // Default to the cutting records list\r\n      setReturnPath('/viewcutting');\r\n    }\r\n\r\n    // Clean up the localStorage when component unmounts\r\n    return () => {\r\n      localStorage.removeItem('cuttingRecordReturnPath');\r\n    };\r\n  }, []);\r\n\r\n  // Fetch cutting record data\r\n  useEffect(() => {\r\n    setLoading(true);\r\n    axios\r\n      .get(`http://localhost:8000/api/cutting/records/${recordId}/`)\r\n      .then((response) => {\r\n        setCuttingRecord(response.data);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"Error fetching cutting record details:\", error);\r\n        setError(\"Failed to load cutting record details.\");\r\n        setLoading(false);\r\n      });\r\n  }, [recordId]);\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    const options = { year: 'numeric', month: 'short', day: 'numeric' };\r\n    return new Date(dateString).toLocaleDateString(undefined, options);\r\n  };\r\n\r\n  // Calculate total pieces for a detail\r\n  const calculateTotalPieces = (detail) => {\r\n    return detail.xs + detail.s + detail.m + detail.l + detail.xl;\r\n  };\r\n\r\n  // Calculate total pieces for all details\r\n  const calculateTotalAllPieces = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      return total + calculateTotalPieces(detail);\r\n    }, 0);\r\n  };\r\n\r\n  // Calculate total yard usage\r\n  const calculateTotalYardUsage = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      return total + parseFloat(detail.yard_usage);\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Calculate fabric cutting value (cost)\r\n  const calculateCuttingValue = (detail) => {\r\n    if (!detail || !detail.fabric_variant_data) return 0;\r\n    const yardUsage = parseFloat(detail.yard_usage);\r\n    const pricePerYard = parseFloat(detail.fabric_variant_data.price_per_yard);\r\n    return (yardUsage * pricePerYard).toFixed(2);\r\n  };\r\n\r\n  // Calculate total cutting value\r\n  const calculateTotalCuttingValue = () => {\r\n    if (!cuttingRecord || !cuttingRecord.details) return 0;\r\n    return cuttingRecord.details.reduce((total, detail) => {\r\n      if (!detail.fabric_variant_data) return total;\r\n      const value = parseFloat(detail.yard_usage) * parseFloat(detail.fabric_variant_data.price_per_yard);\r\n      return total + value;\r\n    }, 0).toFixed(2);\r\n  };\r\n\r\n  // Handle back button click\r\n  const handleBack = () => {\r\n    navigate(returnPath);\r\n  };\r\n\r\n  // Open PDF confirmation modal\r\n  const openPdfModal = () => {\r\n    setShowPdfModal(true);\r\n  };\r\n\r\n  // Close PDF confirmation modal\r\n  const closePdfModal = () => {\r\n    setShowPdfModal(false);\r\n  };\r\n\r\n  // Generate PDF report\r\n  const generatePDF = () => {\r\n    if (!cuttingRecord) return;\r\n\r\n    try {\r\n      const productName = cuttingRecord.product_name || \"N/A\";\r\n      // Get fabric names from details\r\n      const fabricNames = new Set();\r\n      cuttingRecord.details?.forEach(detail => {\r\n        if (detail.fabric_variant_data?.fabric_definition_data?.fabric_name) {\r\n          fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\r\n        }\r\n      });\r\n      const fabricName = Array.from(fabricNames).join(', ') || \"N/A\";\r\n\r\n      // Create PDF document with orientation and unit specifications\r\n      const doc = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Add the actual logo from the public directory\r\n      try {\r\n        // Get the base URL for the current environment\r\n        const baseUrl = window.location.origin;\r\n\r\n        // Add the logo to the PDF\r\n        doc.addImage(`${baseUrl}/logo.png`, 'PNG', 14, 10, 20, 20);\r\n      } catch (logoError) {\r\n        console.warn(\"Could not add logo to PDF:\", logoError);\r\n\r\n        // Fallback to a simple placeholder if the logo can't be loaded\r\n        doc.setFillColor(41, 128, 185); // Primary blue color\r\n        doc.rect(14, 10, 20, 20, 'F');\r\n\r\n        // Add \"PF\" text as a simple logo\r\n        doc.setFontSize(14);\r\n        doc.setTextColor(255, 255, 255);\r\n        doc.text(\"PF\", 24, 22, { align: 'center' });\r\n      }\r\n\r\n      // Reset text color for the rest of the document\r\n      doc.setTextColor(0, 0, 0);\r\n\r\n      // Add title and company info\r\n      doc.setFontSize(20);\r\n      doc.setTextColor(0, 0, 0);\r\n      doc.text(\"Cutting Record Report\", 105, 20, { align: 'center' });\r\n\r\n      doc.setFontSize(12);\r\n      doc.text(\"Pri Fashion - Garment Management System\", 105, 28, { align: 'center' });\r\n      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 34, { align: 'center' });\r\n\r\n      // Add cutting record details\r\n      doc.setFontSize(14);\r\n      doc.text(\"Cutting Record Details\", 14, 45);\r\n\r\n      doc.setFontSize(10);\r\n      const details = [\r\n        [\"Product Name:\", productName],\r\n        [\"Fabric Name:\", fabricName],\r\n        [\"Cutting Date:\", cuttingRecord.cutting_date],\r\n        [\"Total Quantity:\", calculateTotalAllPieces().toString()],\r\n        [\"Total Yard Usage:\", calculateTotalYardUsage().toString()],\r\n        [\"Color Variants Used:\", cuttingRecord.details?.length.toString() || \"0\"],\r\n        [\"Total Fabric Cost:\", `Rs. ${calculateTotalCuttingValue()}`]\r\n      ];\r\n\r\n      // First table\r\n      let finalY = 50;\r\n      autoTable(doc, {\r\n        startY: finalY,\r\n        head: [[\"Property\", \"Value\"]],\r\n        body: details,\r\n        theme: 'grid',\r\n        headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n        styles: { fontSize: 10 }\r\n      });\r\n\r\n      // Get the final Y position after the first table\r\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 10;\r\n\r\n      // Add color usage details\r\n      doc.setFontSize(14);\r\n      doc.text(\"Color Usage Details\", 14, finalY);\r\n\r\n      if (cuttingRecord.details && cuttingRecord.details.length > 0) {\r\n        const colorDetails = cuttingRecord.details.map(detail => {\r\n          // Get the color code for display\r\n          const colorCode = detail.fabric_variant_data?.color || \"#CCCCCC\";\r\n          const colorName = detail.fabric_variant_data?.color_name || \"N/A\";\r\n\r\n          return [\r\n            colorName,\r\n            colorCode, // Add color code as a separate column\r\n            `${parseFloat(detail.yard_usage).toFixed(2)} yards`,\r\n            `Rs. ${detail.fabric_variant_data?.price_per_yard.toFixed(2)}`,\r\n            `Rs. ${calculateCuttingValue(detail)}`,\r\n            detail.xs || 0,\r\n            detail.s || 0,\r\n            detail.m || 0,\r\n            detail.l || 0,\r\n            detail.xl || 0,\r\n            calculateTotalPieces(detail)\r\n          ];\r\n        });\r\n\r\n        // Second table with color swatches\r\n        autoTable(doc, {\r\n          startY: finalY + 5,\r\n          head: [[\"Color Name\", \"Color\", \"Yard Usage\", \"Price/Yard\", \"Fabric Cost\", \"XS\", \"S\", \"M\", \"L\", \"XL\", \"Total\"]],\r\n          body: colorDetails,\r\n          theme: 'grid',\r\n          headStyles: { fillColor: [41, 128, 185], textColor: 255 },\r\n          styles: { fontSize: 9 },\r\n          columnStyles: {\r\n            0: { cellWidth: 25 },\r\n            1: { cellWidth: 15 }, // Color swatch column\r\n            2: { cellWidth: 20 },\r\n            3: { cellWidth: 20 },\r\n            4: { cellWidth: 20 },\r\n          },\r\n          // Add color swatches to the color column\r\n          didDrawCell: (data) => {\r\n            if (data.section === 'body' && data.column.index === 1) {\r\n              const colorHex = data.cell.raw;\r\n\r\n              try {\r\n                // Convert hex color to RGB\r\n                const r = parseInt(colorHex.substring(1, 3), 16) || 0;\r\n                const g = parseInt(colorHex.substring(3, 5), 16) || 0;\r\n                const b = parseInt(colorHex.substring(5, 7), 16) || 0;\r\n\r\n                // Set fill color using RGB values\r\n                doc.setFillColor(r, g, b);\r\n\r\n                // Draw a color rectangle\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'F'\r\n                );\r\n\r\n                // Add a border around the color swatch\r\n                doc.setDrawColor(200, 200, 200);\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'S'\r\n                );\r\n              } catch (error) {\r\n                console.warn(\"Error drawing color swatch:\", error);\r\n                // Fallback to a gray color if there's an error\r\n                doc.setFillColor(200, 200, 200);\r\n                doc.rect(\r\n                  data.cell.x + 2,\r\n                  data.cell.y + 2,\r\n                  data.cell.width - 4,\r\n                  data.cell.height - 4,\r\n                  'F'\r\n                );\r\n              }\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        doc.text(\"No color details available\", 14, finalY + 5);\r\n      }\r\n\r\n      // Get the final Y position after the second table\r\n      finalY = (doc.lastAutoTable || doc.previousAutoTable).finalY + 15;\r\n\r\n      // Add signature fields\r\n      doc.setFontSize(12);\r\n      doc.text(\"Signatures:\", 14, finalY);\r\n\r\n      // Draw signature lines\r\n      finalY += 8;\r\n\r\n      // Owner signature\r\n      doc.line(14, finalY + 15, 80, finalY + 15); // Signature line\r\n      doc.setFontSize(10);\r\n      doc.text(\"Owner Signature\", 14, finalY + 20);\r\n      doc.text(\"Date: ________________\", 14, finalY + 25);\r\n\r\n      // Cutter signature\r\n      doc.line(120, finalY + 15, 186, finalY + 15); // Signature line\r\n      doc.text(\"Cutter Signature\", 120, finalY + 20);\r\n      doc.text(\"Date: ________________\", 120, finalY + 25);\r\n\r\n      // Add footer\r\n      const pageCount = doc.internal.getNumberOfPages();\r\n      for (let i = 1; i <= pageCount; i++) {\r\n        doc.setPage(i);\r\n        doc.setFontSize(8);\r\n        doc.text(\r\n          `Page ${i} of ${pageCount} - Pri Fashion Garment Management System`,\r\n          105,\r\n          doc.internal.pageSize.height - 10,\r\n          { align: 'center' }\r\n        );\r\n      }\r\n\r\n      // Save the PDF with a clean filename\r\n      const cleanProductName = productName.replace(/[^a-zA-Z0-9]/g, '_');\r\n      doc.save(`Cutting_Record_${cuttingRecord.id}_${cleanProductName}.pdf`);\r\n\r\n      // Close the modal\r\n      closePdfModal();\r\n    } catch (error) {\r\n      console.error(\"Error generating PDF:\", error);\r\n      alert(\"Failed to generate PDF. Please try again: \" + error.message);\r\n      closePdfModal();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <Container fluid\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        {/* Back button and PDF button */}\r\n        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <Button\r\n            variant=\"outline-secondary\"\r\n            onClick={handleBack}\r\n          >\r\n            <FaArrowLeft className=\"me-2\" /> Back\r\n          </Button>\r\n          {!loading && cuttingRecord && (\r\n            <Button\r\n              variant=\"outline-success\"\r\n              onClick={openPdfModal}\r\n            >\r\n              <FaDownload className=\"me-2\" /> Download PDF\r\n            </Button>\r\n          )}\r\n        </div>\r\n\r\n        {error && <Alert variant=\"danger\" className=\"text-center\">{error}</Alert>}\r\n\r\n        {loading ? (\r\n          <div className=\"text-center my-5\">\r\n            <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n              <span className=\"visually-hidden\">Loading...</span>\r\n            </Spinner>\r\n            <p className=\"mt-2\">Loading cutting record details...</p>\r\n          </div>\r\n        ) : cuttingRecord && (\r\n          <>\r\n            {/* Cutting Record Header */}\r\n            <Card className=\"mb-4 shadow-sm\">\r\n              <Card.Header className=\"bg-primary text-white\">\r\n                <h4 className=\"mb-0\">\r\n                  <FaCut className=\"me-2\" />\r\n                  Cutting Record Details\r\n                </h4>\r\n              </Card.Header>\r\n              <Card.Body>\r\n                <Row>\r\n                  <Col md={6}>\r\n                    <h5 className=\"mb-3\">\r\n                      {cuttingRecord.product_name || \"Unnamed Cutting Record\"}\r\n                    </h5>\r\n                    <p className=\"mb-2\">\r\n                      <strong>Fabric:</strong>{' '}\r\n                      <span className=\"text-primary\">\r\n                        {cuttingRecord.fabric_definition_data?.fabric_name}\r\n                      </span>\r\n                    </p>\r\n                    <p className=\"mb-2\">\r\n                      <strong>Cutting Date:</strong>{' '}\r\n                      <span>\r\n                        <FaCalendarAlt className=\"me-1 text-secondary\" />\r\n                        {formatDate(cuttingRecord.cutting_date)}\r\n                      </span>\r\n                    </p>\r\n                    {cuttingRecord.description && (\r\n                      <p className=\"mb-2\">\r\n                        <strong>Description:</strong>{' '}\r\n                        <span>{cuttingRecord.description}</span>\r\n                      </p>\r\n                    )}\r\n                  </Col>\r\n                  <Col md={6}>\r\n                    <Card className=\"h-100 bg-light\">\r\n                      <Card.Body>\r\n                        <h5 className=\"mb-3\">Summary</h5>\r\n                        <ListGroup variant=\"flush\">\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaRulerHorizontal className=\"me-2 text-secondary\" />\r\n                              Total Yard Usage\r\n                            </div>\r\n                            <Badge bg=\"info\" pill>\r\n                              {calculateTotalYardUsage()} yards\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaTshirt className=\"me-2 text-secondary\" />\r\n                              Total Pieces\r\n                            </div>\r\n                            <Badge bg=\"success\" pill>\r\n                              {calculateTotalAllPieces()} pcs\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaClipboardList className=\"me-2 text-secondary\" />\r\n                              Color Variants Used\r\n                            </div>\r\n                            <Badge bg=\"primary\" pill>\r\n                              {cuttingRecord.details?.length || 0}\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                          <ListGroup.Item className=\"d-flex justify-content-between align-items-center\">\r\n                            <div>\r\n                              <FaMoneyBillWave className=\"me-2 text-secondary\" />\r\n                              Total Fabric Cost\r\n                            </div>\r\n                            <Badge bg=\"warning\" text=\"dark\" pill>\r\n                              Rs. {calculateTotalCuttingValue()}\r\n                            </Badge>\r\n                          </ListGroup.Item>\r\n                        </ListGroup>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  </Col>\r\n                </Row>\r\n              </Card.Body>\r\n            </Card>\r\n\r\n\r\n\r\n            {/* Cutting Details Table */}\r\n            <Card className=\"shadow-sm\">\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h4 className=\"mb-0\">\r\n                  <FaInfoCircle className=\"me-2\" />\r\n                  Cutting Details\r\n                </h4>\r\n              </Card.Header>\r\n              <Card.Body className=\"p-0\">\r\n                <Table hover responsive className=\"mb-0\">\r\n                  <thead className=\"bg-light\">\r\n                    <tr>\r\n                      <th>Color</th>\r\n                      <th>Yard Usage</th>\r\n                      <th>Price per Yard</th>\r\n                      <th>Fabric Cost</th>\r\n                      <th>XS</th>\r\n                      <th>S</th>\r\n                      <th>M</th>\r\n                      <th>L</th>\r\n                      <th>XL</th>\r\n                      <th>Total Pieces</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {cuttingRecord.details.map((detail) => (\r\n                      <tr key={detail.id}>\r\n                        <td>\r\n                          <div className=\"d-flex align-items-center\">\r\n                            <div\r\n                              style={{\r\n                                width: '20px',\r\n                                height: '20px',\r\n                                backgroundColor: detail.fabric_variant_data?.color || '#ccc',\r\n                                borderRadius: '4px',\r\n                                border: '1px solid #dee2e6',\r\n                                marginRight: '10px'\r\n                              }}\r\n                            />\r\n                            <span>{detail.fabric_variant_data?.color_name || 'Unknown'}</span>\r\n                          </div>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"info\" pill>\r\n                            {parseFloat(detail.yard_usage).toFixed(2)} yards\r\n                          </Badge>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"secondary\" pill>\r\n                            Rs. {detail.fabric_variant_data?.price_per_yard.toFixed(2)}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>\r\n                          <Badge bg=\"warning\" text=\"dark\" pill>\r\n                            Rs. {calculateCuttingValue(detail)}\r\n                          </Badge>\r\n                        </td>\r\n                        <td>{detail.xs > 0 ? detail.xs : '-'}</td>\r\n                        <td>{detail.s > 0 ? detail.s : '-'}</td>\r\n                        <td>{detail.m > 0 ? detail.m : '-'}</td>\r\n                        <td>{detail.l > 0 ? detail.l : '-'}</td>\r\n                        <td>{detail.xl > 0 ? detail.xl : '-'}</td>\r\n                        <td>\r\n                          <Badge bg=\"success\" pill>\r\n                            {calculateTotalPieces(detail)} pcs\r\n                          </Badge>\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </tbody>\r\n                  <tfoot className=\"bg-light\">\r\n                    <tr>\r\n                      <td className=\"fw-bold\">Total</td>\r\n                      <td className=\"fw-bold\">{calculateTotalYardUsage()} yards</td>\r\n                      <td className=\"fw-bold\">-</td>\r\n                      <td className=\"fw-bold\">Rs. {calculateTotalCuttingValue()}</td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.xs, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.s, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.m, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.l, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">\r\n                        {cuttingRecord.details.reduce((sum, detail) => sum + detail.xl, 0)}\r\n                      </td>\r\n                      <td className=\"fw-bold\">{calculateTotalAllPieces()} pcs</td>\r\n                    </tr>\r\n                  </tfoot>\r\n                </Table>\r\n              </Card.Body>\r\n            </Card>\r\n          </>\r\n        )}\r\n      </Container>\r\n\r\n      {/* PDF Confirmation Modal */}\r\n      <Modal show={showPdfModal} onHide={closePdfModal} centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <FaFilePdf className=\"text-danger me-2\" />\r\n            Generate PDF Report\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Are you sure you want to generate a PDF report for this cutting record?</p>\r\n          {cuttingRecord && (\r\n            <div className=\"bg-light p-3 rounded\">\r\n              <p className=\"mb-1\"><strong>Product:</strong> {cuttingRecord.product_name || \"N/A\"}</p>\r\n              <p className=\"mb-1\"><strong>Fabrics:</strong> {(() => {\r\n                const fabricNames = new Set();\r\n                cuttingRecord.details?.forEach(detail => {\r\n                  if (detail.fabric_variant_data?.fabric_definition_data?.fabric_name) {\r\n                    fabricNames.add(detail.fabric_variant_data.fabric_definition_data.fabric_name);\r\n                  }\r\n                });\r\n                return Array.from(fabricNames).join(', ') || \"N/A\";\r\n              })()}</p>\r\n              <p className=\"mb-1\"><strong>Date:</strong> {cuttingRecord.cutting_date}</p>\r\n              <p className=\"mb-0\"><strong>Total Cost:</strong> Rs. {calculateTotalCuttingValue()}</p>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={closePdfModal}>\r\n            <FaTimes className=\"me-1\" /> Cancel\r\n          </Button>\r\n          <Button variant=\"success\" onClick={generatePDF}>\r\n            <FaCheck className=\"me-1\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default CuttingRecordDetail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SACEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAChDC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,QAC1B,iBAAiB;AACxB,SACEC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAC3CC,YAAY,EAAEC,iBAAiB,EAAEC,eAAe,EAChDC,eAAe,EAAEC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,QACnD,gBAAgB;AACvB,SAASC,KAAK,QAAQ,OAAO;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChC,MAAM;IAAEC;EAAS,CAAC,GAAGpC,SAAS,CAAC,CAAC;EAChC,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAACiD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsD,YAAY,GAAGA,CAAA,KAAM;MACzBP,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMN,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtD,SAAS,CAAC,MAAM;IACd,MAAMyD,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC;IACvE,IAAIF,eAAe,EAAE;MACnBN,aAAa,CAACM,eAAe,CAAC;IAChC,CAAC,MAAM;MACL;MACAN,aAAa,CAAC,cAAc,CAAC;IAC/B;;IAEA;IACA,OAAO,MAAM;MACXO,YAAY,CAACE,UAAU,CAAC,yBAAyB,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5D,SAAS,CAAC,MAAM;IACd2C,UAAU,CAAC,IAAI,CAAC;IAChB1C,KAAK,CACF4D,GAAG,CAAC,6CAA6CvB,QAAQ,GAAG,CAAC,CAC7DwB,IAAI,CAAEC,QAAQ,IAAK;MAClBtB,gBAAgB,CAACsB,QAAQ,CAACC,IAAI,CAAC;MAC/BrB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDsB,KAAK,CAAErB,KAAK,IAAK;MAChBsB,OAAO,CAACtB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DC,QAAQ,CAAC,wCAAwC,CAAC;MAClDF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM6B,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,OAAO,GAAG;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAU,CAAC;IACnE,OAAO,IAAIC,IAAI,CAACL,UAAU,CAAC,CAACM,kBAAkB,CAACC,SAAS,EAAEN,OAAO,CAAC;EACpE,CAAC;;EAED;EACA,MAAMO,oBAAoB,GAAIC,MAAM,IAAK;IACvC,OAAOA,MAAM,CAACC,EAAE,GAAGD,MAAM,CAACE,CAAC,GAAGF,MAAM,CAACG,CAAC,GAAGH,MAAM,CAACI,CAAC,GAAGJ,MAAM,CAACK,EAAE;EAC/D,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC3C,aAAa,IAAI,CAACA,aAAa,CAAC4C,OAAO,EAAE,OAAO,CAAC;IACtD,OAAO5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAET,MAAM,KAAK;MACrD,OAAOS,KAAK,GAAGV,oBAAoB,CAACC,MAAM,CAAC;IAC7C,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAMU,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC/C,aAAa,IAAI,CAACA,aAAa,CAAC4C,OAAO,EAAE,OAAO,CAAC;IACtD,OAAO5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAET,MAAM,KAAK;MACrD,OAAOS,KAAK,GAAGE,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC;IAC9C,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAId,MAAM,IAAK;IACxC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACe,mBAAmB,EAAE,OAAO,CAAC;IACpD,MAAMC,SAAS,GAAGL,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC;IAC/C,MAAMK,YAAY,GAAGN,UAAU,CAACX,MAAM,CAACe,mBAAmB,CAACG,cAAc,CAAC;IAC1E,OAAO,CAACF,SAAS,GAAGC,YAAY,EAAEJ,OAAO,CAAC,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAACxD,aAAa,IAAI,CAACA,aAAa,CAAC4C,OAAO,EAAE,OAAO,CAAC;IACtD,OAAO5C,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACC,KAAK,EAAET,MAAM,KAAK;MACrD,IAAI,CAACA,MAAM,CAACe,mBAAmB,EAAE,OAAON,KAAK;MAC7C,MAAMW,KAAK,GAAGT,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,GAAGD,UAAU,CAACX,MAAM,CAACe,mBAAmB,CAACG,cAAc,CAAC;MACnG,OAAOT,KAAK,GAAGW,KAAK;IACtB,CAAC,EAAE,CAAC,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvB3D,QAAQ,CAACW,UAAU,CAAC;EACtB,CAAC;;EAED;EACA,MAAMiD,YAAY,GAAGA,CAAA,KAAM;IACzB9C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM+C,aAAa,GAAGA,CAAA,KAAM;IAC1B/C,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMgD,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC7D,aAAa,EAAE;IAEpB,IAAI;MAAA,IAAA8D,qBAAA,EAAAC,sBAAA;MACF,MAAMC,WAAW,GAAGhE,aAAa,CAACiE,YAAY,IAAI,KAAK;MACvD;MACA,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC7B,CAAAL,qBAAA,GAAA9D,aAAa,CAAC4C,OAAO,cAAAkB,qBAAA,uBAArBA,qBAAA,CAAuBM,OAAO,CAAC/B,MAAM,IAAI;QAAA,IAAAgC,qBAAA,EAAAC,sBAAA;QACvC,KAAAD,qBAAA,GAAIhC,MAAM,CAACe,mBAAmB,cAAAiB,qBAAA,gBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BE,sBAAsB,cAAAD,sBAAA,eAAlDA,sBAAA,CAAoDE,WAAW,EAAE;UACnEN,WAAW,CAACO,GAAG,CAACpC,MAAM,CAACe,mBAAmB,CAACmB,sBAAsB,CAACC,WAAW,CAAC;QAChF;MACF,CAAC,CAAC;MACF,MAAME,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK;;MAE9D;MACA,MAAMC,GAAG,GAAG,IAAI1F,KAAK,CAAC;QACpB2F,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,IAAI;QACF;QACA,MAAMC,OAAO,GAAG1E,MAAM,CAAC2E,QAAQ,CAACC,MAAM;;QAEtC;QACAN,GAAG,CAACO,QAAQ,CAAC,GAAGH,OAAO,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC5D,CAAC,CAAC,OAAOI,SAAS,EAAE;QAClB5D,OAAO,CAAC6D,IAAI,CAAC,4BAA4B,EAAED,SAAS,CAAC;;QAErD;QACAR,GAAG,CAACU,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAChCV,GAAG,CAACW,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;QAE7B;QACAX,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;QACnBZ,GAAG,CAACa,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/Bb,GAAG,CAACc,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;MAC7C;;MAEA;MACAf,GAAG,CAACa,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEzB;MACAb,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACa,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBb,GAAG,CAACc,IAAI,CAAC,uBAAuB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;MAE/Df,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACc,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;MACjFf,GAAG,CAACc,IAAI,CAAC,iBAAiB,IAAI3D,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;QAAE2D,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE1F;MACAf,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACc,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE,EAAE,CAAC;MAE1Cd,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnB,MAAM9C,OAAO,GAAG,CACd,CAAC,eAAe,EAAEoB,WAAW,CAAC,EAC9B,CAAC,cAAc,EAAEU,UAAU,CAAC,EAC5B,CAAC,eAAe,EAAE1E,aAAa,CAAC8F,YAAY,CAAC,EAC7C,CAAC,iBAAiB,EAAEnD,uBAAuB,CAAC,CAAC,CAACoD,QAAQ,CAAC,CAAC,CAAC,EACzD,CAAC,mBAAmB,EAAEhD,uBAAuB,CAAC,CAAC,CAACgD,QAAQ,CAAC,CAAC,CAAC,EAC3D,CAAC,sBAAsB,EAAE,EAAAhC,sBAAA,GAAA/D,aAAa,CAAC4C,OAAO,cAAAmB,sBAAA,uBAArBA,sBAAA,CAAuBiC,MAAM,CAACD,QAAQ,CAAC,CAAC,KAAI,GAAG,CAAC,EACzE,CAAC,oBAAoB,EAAE,OAAOvC,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAC9D;;MAED;MACA,IAAIyC,MAAM,GAAG,EAAE;MACf5G,SAAS,CAACyF,GAAG,EAAE;QACboB,MAAM,EAAED,MAAM;QACdE,IAAI,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC7BC,IAAI,EAAExD,OAAO;QACbyD,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE;QAAI,CAAC;QACzDC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG;MACzB,CAAC,CAAC;;MAEF;MACAT,MAAM,GAAG,CAACnB,GAAG,CAAC6B,aAAa,IAAI7B,GAAG,CAAC8B,iBAAiB,EAAEX,MAAM,GAAG,EAAE;;MAEjE;MACAnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACc,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAEK,MAAM,CAAC;MAE3C,IAAIjG,aAAa,CAAC4C,OAAO,IAAI5C,aAAa,CAAC4C,OAAO,CAACoD,MAAM,GAAG,CAAC,EAAE;QAC7D,MAAMa,YAAY,GAAG7G,aAAa,CAAC4C,OAAO,CAACkE,GAAG,CAACzE,MAAM,IAAI;UAAA,IAAA0E,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UACvD;UACA,MAAMC,SAAS,GAAG,EAAAH,sBAAA,GAAA1E,MAAM,CAACe,mBAAmB,cAAA2D,sBAAA,uBAA1BA,sBAAA,CAA4BI,KAAK,KAAI,SAAS;UAChE,MAAMC,SAAS,GAAG,EAAAJ,sBAAA,GAAA3E,MAAM,CAACe,mBAAmB,cAAA4D,sBAAA,uBAA1BA,sBAAA,CAA4BK,UAAU,KAAI,KAAK;UAEjE,OAAO,CACLD,SAAS,EACTF,SAAS;UAAE;UACX,GAAGlE,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,QAAQ,EACnD,QAAA+D,sBAAA,GAAO5E,MAAM,CAACe,mBAAmB,cAAA6D,sBAAA,uBAA1BA,sBAAA,CAA4B1D,cAAc,CAACL,OAAO,CAAC,CAAC,CAAC,EAAE,EAC9D,OAAOC,qBAAqB,CAACd,MAAM,CAAC,EAAE,EACtCA,MAAM,CAACC,EAAE,IAAI,CAAC,EACdD,MAAM,CAACE,CAAC,IAAI,CAAC,EACbF,MAAM,CAACG,CAAC,IAAI,CAAC,EACbH,MAAM,CAACI,CAAC,IAAI,CAAC,EACbJ,MAAM,CAACK,EAAE,IAAI,CAAC,EACdN,oBAAoB,CAACC,MAAM,CAAC,CAC7B;QACH,CAAC,CAAC;;QAEF;QACAhD,SAAS,CAACyF,GAAG,EAAE;UACboB,MAAM,EAAED,MAAM,GAAG,CAAC;UAClBE,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;UAC9GC,IAAI,EAAES,YAAY;UAClBR,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE;YAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;YAAEC,SAAS,EAAE;UAAI,CAAC;UACzDC,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAC;UACvBY,YAAY,EAAE;YACZ,CAAC,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAC;YACpB,CAAC,EAAE;cAAEA,SAAS,EAAE;YAAG,CAAC;YAAE;YACtB,CAAC,EAAE;cAAEA,SAAS,EAAE;YAAG,CAAC;YACpB,CAAC,EAAE;cAAEA,SAAS,EAAE;YAAG,CAAC;YACpB,CAAC,EAAE;cAAEA,SAAS,EAAE;YAAG;UACrB,CAAC;UACD;UACAC,WAAW,EAAGhG,IAAI,IAAK;YACrB,IAAIA,IAAI,CAACiG,OAAO,KAAK,MAAM,IAAIjG,IAAI,CAACkG,MAAM,CAACC,KAAK,KAAK,CAAC,EAAE;cACtD,MAAMC,QAAQ,GAAGpG,IAAI,CAACqG,IAAI,CAACC,GAAG;cAE9B,IAAI;gBACF;gBACA,MAAMC,CAAC,GAAGC,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAMC,CAAC,GAAGF,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;gBACrD,MAAME,CAAC,GAAGH,QAAQ,CAACJ,QAAQ,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;;gBAErD;gBACAnD,GAAG,CAACU,YAAY,CAACuC,CAAC,EAAEG,CAAC,EAAEC,CAAC,CAAC;;gBAEzB;gBACArD,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,GAAG,CAAC,EACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,GAAG,CAAC,EACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,GAAG,CAAC,EACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,GAAG,CAAC,EACpB,GACF,CAAC;;gBAED;gBACAzD,GAAG,CAAC0D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC/B1D,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,GAAG,CAAC,EACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,GAAG,CAAC,EACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,GAAG,CAAC,EACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,GAAG,CAAC,EACpB,GACF,CAAC;cACH,CAAC,CAAC,OAAOnI,KAAK,EAAE;gBACdsB,OAAO,CAAC6D,IAAI,CAAC,6BAA6B,EAAEnF,KAAK,CAAC;gBAClD;gBACA0E,GAAG,CAACU,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC/BV,GAAG,CAACW,IAAI,CACNjE,IAAI,CAACqG,IAAI,CAACO,CAAC,GAAG,CAAC,EACf5G,IAAI,CAACqG,IAAI,CAACQ,CAAC,GAAG,CAAC,EACf7G,IAAI,CAACqG,IAAI,CAACS,KAAK,GAAG,CAAC,EACnB9G,IAAI,CAACqG,IAAI,CAACU,MAAM,GAAG,CAAC,EACpB,GACF,CAAC;cACH;YACF;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLzD,GAAG,CAACc,IAAI,CAAC,4BAA4B,EAAE,EAAE,EAAEK,MAAM,GAAG,CAAC,CAAC;MACxD;;MAEA;MACAA,MAAM,GAAG,CAACnB,GAAG,CAAC6B,aAAa,IAAI7B,GAAG,CAAC8B,iBAAiB,EAAEX,MAAM,GAAG,EAAE;;MAEjE;MACAnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACc,IAAI,CAAC,aAAa,EAAE,EAAE,EAAEK,MAAM,CAAC;;MAEnC;MACAA,MAAM,IAAI,CAAC;;MAEX;MACAnB,GAAG,CAAC2D,IAAI,CAAC,EAAE,EAAExC,MAAM,GAAG,EAAE,EAAE,EAAE,EAAEA,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;MAC5CnB,GAAG,CAACY,WAAW,CAAC,EAAE,CAAC;MACnBZ,GAAG,CAACc,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAEK,MAAM,GAAG,EAAE,CAAC;MAC5CnB,GAAG,CAACc,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAEK,MAAM,GAAG,EAAE,CAAC;;MAEnD;MACAnB,GAAG,CAAC2D,IAAI,CAAC,GAAG,EAAExC,MAAM,GAAG,EAAE,EAAE,GAAG,EAAEA,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;MAC9CnB,GAAG,CAACc,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAEK,MAAM,GAAG,EAAE,CAAC;MAC9CnB,GAAG,CAACc,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAEK,MAAM,GAAG,EAAE,CAAC;;MAEpD;MACA,MAAMyC,SAAS,GAAG5D,GAAG,CAAC6D,QAAQ,CAACC,gBAAgB,CAAC,CAAC;MACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,SAAS,EAAEG,CAAC,EAAE,EAAE;QACnC/D,GAAG,CAACgE,OAAO,CAACD,CAAC,CAAC;QACd/D,GAAG,CAACY,WAAW,CAAC,CAAC,CAAC;QAClBZ,GAAG,CAACc,IAAI,CACN,QAAQiD,CAAC,OAAOH,SAAS,0CAA0C,EACnE,GAAG,EACH5D,GAAG,CAAC6D,QAAQ,CAACI,QAAQ,CAACR,MAAM,GAAG,EAAE,EACjC;UAAE1C,KAAK,EAAE;QAAS,CACpB,CAAC;MACH;;MAEA;MACA,MAAMmD,gBAAgB,GAAGhF,WAAW,CAACiF,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAClEnE,GAAG,CAACoE,IAAI,CAAC,kBAAkBlJ,aAAa,CAACmJ,EAAE,IAAIH,gBAAgB,MAAM,CAAC;;MAEtE;MACApF,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CgJ,KAAK,CAAC,4CAA4C,GAAGhJ,KAAK,CAACiJ,OAAO,CAAC;MACnEzF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,oBACErE,OAAA,CAAAE,SAAA;IAAA6J,QAAA,gBACE/J,OAAA,CAAC3B,eAAe;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBnK,OAAA,CAAC1B,SAAS;MAAC8L,KAAK;MACdC,KAAK,EAAE;QACLC,UAAU,EAAEvJ,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CgI,KAAK,EAAE,eAAehI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzDwJ,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE;MACX,CAAE;MAAAT,QAAA,gBAGF/J,OAAA;QAAKyK,SAAS,EAAC,wDAAwD;QAAAV,QAAA,gBACrE/J,OAAA,CAAClB,MAAM;UACL4L,OAAO,EAAC,mBAAmB;UAC3BC,OAAO,EAAExG,UAAW;UAAA4F,QAAA,gBAEpB/J,OAAA,CAACf,WAAW;YAACwL,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAClC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR,CAACxJ,OAAO,IAAIF,aAAa,iBACxBT,OAAA,CAAClB,MAAM;UACL4L,OAAO,EAAC,iBAAiB;UACzBC,OAAO,EAAEvG,YAAa;UAAA2F,QAAA,gBAEtB/J,OAAA,CAACP,UAAU;YAACgL,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBACjC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELtJ,KAAK,iBAAIb,OAAA,CAACnB,KAAK;QAAC6L,OAAO,EAAC,QAAQ;QAACD,SAAS,EAAC,aAAa;QAAAV,QAAA,EAAElJ;MAAK;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAExExJ,OAAO,gBACNX,OAAA;QAAKyK,SAAS,EAAC,kBAAkB;QAAAV,QAAA,gBAC/B/J,OAAA,CAACpB,OAAO;UAACgM,SAAS,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAACH,OAAO,EAAC,SAAS;UAAAX,QAAA,eACzD/J,OAAA;YAAMyK,SAAS,EAAC,iBAAiB;YAAAV,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACVnK,OAAA;UAAGyK,SAAS,EAAC,MAAM;UAAAV,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,GACJ1J,aAAa,iBACfT,OAAA,CAAAE,SAAA;QAAA6J,QAAA,gBAEE/J,OAAA,CAACvB,IAAI;UAACgM,SAAS,EAAC,gBAAgB;UAAAV,QAAA,gBAC9B/J,OAAA,CAACvB,IAAI,CAACqM,MAAM;YAACL,SAAS,EAAC,uBAAuB;YAAAV,QAAA,eAC5C/J,OAAA;cAAIyK,SAAS,EAAC,MAAM;cAAAV,QAAA,gBAClB/J,OAAA,CAACb,KAAK;gBAACsL,SAAS,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAE5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdnK,OAAA,CAACvB,IAAI,CAACsM,IAAI;YAAAhB,QAAA,eACR/J,OAAA,CAACzB,GAAG;cAAAwL,QAAA,gBACF/J,OAAA,CAACxB,GAAG;gBAACwM,EAAE,EAAE,CAAE;gBAAAjB,QAAA,gBACT/J,OAAA;kBAAIyK,SAAS,EAAC,MAAM;kBAAAV,QAAA,EACjBtJ,aAAa,CAACiE,YAAY,IAAI;gBAAwB;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACLnK,OAAA;kBAAGyK,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBACjB/J,OAAA;oBAAA+J,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAAC,GAAG,eAC5BnK,OAAA;oBAAMyK,SAAS,EAAC,cAAc;oBAAAV,QAAA,GAAA1J,qBAAA,GAC3BI,aAAa,CAACuE,sBAAsB,cAAA3E,qBAAA,uBAApCA,qBAAA,CAAsC4E;kBAAW;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACJnK,OAAA;kBAAGyK,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBACjB/J,OAAA;oBAAA+J,QAAA,EAAQ;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAAC,GAAG,eAClCnK,OAAA;oBAAA+J,QAAA,gBACE/J,OAAA,CAACd,aAAa;sBAACuL,SAAS,EAAC;oBAAqB;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAChD/H,UAAU,CAAC3B,aAAa,CAAC8F,YAAY,CAAC;kBAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACH1J,aAAa,CAACwK,WAAW,iBACxBjL,OAAA;kBAAGyK,SAAS,EAAC,MAAM;kBAAAV,QAAA,gBACjB/J,OAAA;oBAAA+J,QAAA,EAAQ;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAAC,GAAG,eACjCnK,OAAA;oBAAA+J,QAAA,EAAOtJ,aAAa,CAACwK;kBAAW;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNnK,OAAA,CAACxB,GAAG;gBAACwM,EAAE,EAAE,CAAE;gBAAAjB,QAAA,eACT/J,OAAA,CAACvB,IAAI;kBAACgM,SAAS,EAAC,gBAAgB;kBAAAV,QAAA,eAC9B/J,OAAA,CAACvB,IAAI,CAACsM,IAAI;oBAAAhB,QAAA,gBACR/J,OAAA;sBAAIyK,SAAS,EAAC,MAAM;sBAAAV,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjCnK,OAAA,CAACjB,SAAS;sBAAC2L,OAAO,EAAC,OAAO;sBAAAX,QAAA,gBACxB/J,OAAA,CAACjB,SAAS,CAACmM,IAAI;wBAACT,SAAS,EAAC,mDAAmD;wBAAAV,QAAA,gBAC3E/J,OAAA;0BAAA+J,QAAA,gBACE/J,OAAA,CAACV,iBAAiB;4BAACmL,SAAS,EAAC;0BAAqB;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,oBAEvD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNnK,OAAA,CAACrB,KAAK;0BAACwM,EAAE,EAAC,MAAM;0BAACC,IAAI;0BAAArB,QAAA,GAClBvG,uBAAuB,CAAC,CAAC,EAAC,QAC7B;wBAAA;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC,eACjBnK,OAAA,CAACjB,SAAS,CAACmM,IAAI;wBAACT,SAAS,EAAC,mDAAmD;wBAAAV,QAAA,gBAC3E/J,OAAA;0BAAA+J,QAAA,gBACE/J,OAAA,CAACZ,QAAQ;4BAACqL,SAAS,EAAC;0BAAqB;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAE9C;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNnK,OAAA,CAACrB,KAAK;0BAACwM,EAAE,EAAC,SAAS;0BAACC,IAAI;0BAAArB,QAAA,GACrB3G,uBAAuB,CAAC,CAAC,EAAC,MAC7B;wBAAA;0BAAA4G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC,eACjBnK,OAAA,CAACjB,SAAS,CAACmM,IAAI;wBAACT,SAAS,EAAC,mDAAmD;wBAAAV,QAAA,gBAC3E/J,OAAA;0BAAA+J,QAAA,gBACE/J,OAAA,CAACT,eAAe;4BAACkL,SAAS,EAAC;0BAAqB;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,uBAErD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNnK,OAAA,CAACrB,KAAK;0BAACwM,EAAE,EAAC,SAAS;0BAACC,IAAI;0BAAArB,QAAA,EACrB,EAAAzJ,sBAAA,GAAAG,aAAa,CAAC4C,OAAO,cAAA/C,sBAAA,uBAArBA,sBAAA,CAAuBmG,MAAM,KAAI;wBAAC;0BAAAuD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC,eACjBnK,OAAA,CAACjB,SAAS,CAACmM,IAAI;wBAACT,SAAS,EAAC,mDAAmD;wBAAAV,QAAA,gBAC3E/J,OAAA;0BAAA+J,QAAA,gBACE/J,OAAA,CAACR,eAAe;4BAACiL,SAAS,EAAC;0BAAqB;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,qBAErD;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNnK,OAAA,CAACrB,KAAK;0BAACwM,EAAE,EAAC,SAAS;0BAAC9E,IAAI,EAAC,MAAM;0BAAC+E,IAAI;0BAAArB,QAAA,GAAC,MAC/B,EAAC9F,0BAA0B,CAAC,CAAC;wBAAA;0BAAA+F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAKPnK,OAAA,CAACvB,IAAI;UAACgM,SAAS,EAAC,WAAW;UAAAV,QAAA,gBACzB/J,OAAA,CAACvB,IAAI,CAACqM,MAAM;YAACL,SAAS,EAAC,oBAAoB;YAAAV,QAAA,eACzC/J,OAAA;cAAIyK,SAAS,EAAC,MAAM;cAAAV,QAAA,gBAClB/J,OAAA,CAACX,YAAY;gBAACoL,SAAS,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdnK,OAAA,CAACvB,IAAI,CAACsM,IAAI;YAACN,SAAS,EAAC,KAAK;YAAAV,QAAA,eACxB/J,OAAA,CAACtB,KAAK;cAAC2M,KAAK;cAACC,UAAU;cAACb,SAAS,EAAC,MAAM;cAAAV,QAAA,gBACtC/J,OAAA;gBAAOyK,SAAS,EAAC,UAAU;gBAAAV,QAAA,eACzB/J,OAAA;kBAAA+J,QAAA,gBACE/J,OAAA;oBAAA+J,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACVnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACVnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACVnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACXnK,OAAA;oBAAA+J,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRnK,OAAA;gBAAA+J,QAAA,EACGtJ,aAAa,CAAC4C,OAAO,CAACkE,GAAG,CAAEzE,MAAM;kBAAA,IAAAyI,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBAAA,oBAChCzL,OAAA;oBAAA+J,QAAA,gBACE/J,OAAA;sBAAA+J,QAAA,eACE/J,OAAA;wBAAKyK,SAAS,EAAC,2BAA2B;wBAAAV,QAAA,gBACxC/J,OAAA;0BACEqK,KAAK,EAAE;4BACLtB,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACd0C,eAAe,EAAE,EAAAH,sBAAA,GAAAzI,MAAM,CAACe,mBAAmB,cAAA0H,sBAAA,uBAA1BA,sBAAA,CAA4B3D,KAAK,KAAI,MAAM;4BAC5D+D,YAAY,EAAE,KAAK;4BACnBC,MAAM,EAAE,mBAAmB;4BAC3BC,WAAW,EAAE;0BACf;wBAAE;0BAAA7B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFnK,OAAA;0BAAA+J,QAAA,EAAO,EAAAyB,sBAAA,GAAA1I,MAAM,CAACe,mBAAmB,cAAA2H,sBAAA,uBAA1BA,sBAAA,CAA4B1D,UAAU,KAAI;wBAAS;0BAAAkC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLnK,OAAA;sBAAA+J,QAAA,eACE/J,OAAA,CAACrB,KAAK;wBAACwM,EAAE,EAAC,MAAM;wBAACC,IAAI;wBAAArB,QAAA,GAClBtG,UAAU,CAACX,MAAM,CAACY,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,QAC5C;sBAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACLnK,OAAA;sBAAA+J,QAAA,eACE/J,OAAA,CAACrB,KAAK;wBAACwM,EAAE,EAAC,WAAW;wBAACC,IAAI;wBAAArB,QAAA,GAAC,MACrB,GAAA0B,sBAAA,GAAC3I,MAAM,CAACe,mBAAmB,cAAA4H,sBAAA,uBAA1BA,sBAAA,CAA4BzH,cAAc,CAACL,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAqG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACLnK,OAAA;sBAAA+J,QAAA,eACE/J,OAAA,CAACrB,KAAK;wBAACwM,EAAE,EAAC,SAAS;wBAAC9E,IAAI,EAAC,MAAM;wBAAC+E,IAAI;wBAAArB,QAAA,GAAC,MAC/B,EAACnG,qBAAqB,CAACd,MAAM,CAAC;sBAAA;wBAAAkH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACLnK,OAAA;sBAAA+J,QAAA,EAAKjH,MAAM,CAACC,EAAE,GAAG,CAAC,GAAGD,MAAM,CAACC,EAAE,GAAG;oBAAG;sBAAAiH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1CnK,OAAA;sBAAA+J,QAAA,EAAKjH,MAAM,CAACE,CAAC,GAAG,CAAC,GAAGF,MAAM,CAACE,CAAC,GAAG;oBAAG;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCnK,OAAA;sBAAA+J,QAAA,EAAKjH,MAAM,CAACG,CAAC,GAAG,CAAC,GAAGH,MAAM,CAACG,CAAC,GAAG;oBAAG;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCnK,OAAA;sBAAA+J,QAAA,EAAKjH,MAAM,CAACI,CAAC,GAAG,CAAC,GAAGJ,MAAM,CAACI,CAAC,GAAG;oBAAG;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxCnK,OAAA;sBAAA+J,QAAA,EAAKjH,MAAM,CAACK,EAAE,GAAG,CAAC,GAAGL,MAAM,CAACK,EAAE,GAAG;oBAAG;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1CnK,OAAA;sBAAA+J,QAAA,eACE/J,OAAA,CAACrB,KAAK;wBAACwM,EAAE,EAAC,SAAS;wBAACC,IAAI;wBAAArB,QAAA,GACrBlH,oBAAoB,CAACC,MAAM,CAAC,EAAC,MAChC;sBAAA;wBAAAkH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA,GAxCErH,MAAM,CAAC8G,EAAE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyCd,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACRnK,OAAA;gBAAOyK,SAAS,EAAC,UAAU;gBAAAV,QAAA,eACzB/J,OAAA;kBAAA+J,QAAA,gBACE/J,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClCnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,GAAEvG,uBAAuB,CAAC,CAAC,EAAC,QAAM;kBAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9BnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,GAAC,MAAI,EAAC9F,0BAA0B,CAAC,CAAC;kBAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/DnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACwI,GAAG,EAAEhJ,MAAM,KAAKgJ,GAAG,GAAGhJ,MAAM,CAACC,EAAE,EAAE,CAAC;kBAAC;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACLnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACwI,GAAG,EAAEhJ,MAAM,KAAKgJ,GAAG,GAAGhJ,MAAM,CAACE,CAAC,EAAE,CAAC;kBAAC;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACLnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACwI,GAAG,EAAEhJ,MAAM,KAAKgJ,GAAG,GAAGhJ,MAAM,CAACG,CAAC,EAAE,CAAC;kBAAC;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACLnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACwI,GAAG,EAAEhJ,MAAM,KAAKgJ,GAAG,GAAGhJ,MAAM,CAACI,CAAC,EAAE,CAAC;kBAAC;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACLnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,EACpBtJ,aAAa,CAAC4C,OAAO,CAACC,MAAM,CAAC,CAACwI,GAAG,EAAEhJ,MAAM,KAAKgJ,GAAG,GAAGhJ,MAAM,CAACK,EAAE,EAAE,CAAC;kBAAC;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACLnK,OAAA;oBAAIyK,SAAS,EAAC,SAAS;oBAAAV,QAAA,GAAE3G,uBAAuB,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,eACP,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGZnK,OAAA,CAAChB,KAAK;MAAC+M,IAAI,EAAE1K,YAAa;MAAC2K,MAAM,EAAE3H,aAAc;MAAC4H,QAAQ;MAAAlC,QAAA,gBACxD/J,OAAA,CAAChB,KAAK,CAAC8L,MAAM;QAACoB,WAAW;QAAAnC,QAAA,eACvB/J,OAAA,CAAChB,KAAK,CAACmN,KAAK;UAAApC,QAAA,gBACV/J,OAAA,CAACN,SAAS;YAAC+K,SAAS,EAAC;UAAkB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfnK,OAAA,CAAChB,KAAK,CAAC+L,IAAI;QAAAhB,QAAA,gBACT/J,OAAA;UAAA+J,QAAA,EAAG;QAAuE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC7E1J,aAAa,iBACZT,OAAA;UAAKyK,SAAS,EAAC,sBAAsB;UAAAV,QAAA,gBACnC/J,OAAA;YAAGyK,SAAS,EAAC,MAAM;YAAAV,QAAA,gBAAC/J,OAAA;cAAA+J,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1J,aAAa,CAACiE,YAAY,IAAI,KAAK;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFnK,OAAA;YAAGyK,SAAS,EAAC,MAAM;YAAAV,QAAA,gBAAC/J,OAAA;cAAA+J,QAAA,EAAQ;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,CAACiC,sBAAA,IAAM;cACpD,MAAMzH,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;cAC7B,CAAAwH,sBAAA,GAAA3L,aAAa,CAAC4C,OAAO,cAAA+I,sBAAA,uBAArBA,sBAAA,CAAuBvH,OAAO,CAAC/B,MAAM,IAAI;gBAAA,IAAAuJ,sBAAA,EAAAC,uBAAA;gBACvC,KAAAD,sBAAA,GAAIvJ,MAAM,CAACe,mBAAmB,cAAAwI,sBAAA,gBAAAC,uBAAA,GAA1BD,sBAAA,CAA4BrH,sBAAsB,cAAAsH,uBAAA,eAAlDA,uBAAA,CAAoDrH,WAAW,EAAE;kBACnEN,WAAW,CAACO,GAAG,CAACpC,MAAM,CAACe,mBAAmB,CAACmB,sBAAsB,CAACC,WAAW,CAAC;gBAChF;cACF,CAAC,CAAC;cACF,OAAOG,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK;YACpD,CAAC,EAAE,CAAC;UAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACTnK,OAAA;YAAGyK,SAAS,EAAC,MAAM;YAAAV,QAAA,gBAAC/J,OAAA;cAAA+J,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1J,aAAa,CAAC8F,YAAY;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EnK,OAAA;YAAGyK,SAAS,EAAC,MAAM;YAAAV,QAAA,gBAAC/J,OAAA;cAAA+J,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,SAAK,EAAClG,0BAA0B,CAAC,CAAC;UAAA;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbnK,OAAA,CAAChB,KAAK,CAACuN,MAAM;QAAAxC,QAAA,gBACX/J,OAAA,CAAClB,MAAM;UAAC4L,OAAO,EAAC,WAAW;UAACC,OAAO,EAAEtG,aAAc;UAAA0F,QAAA,gBACjD/J,OAAA,CAACJ,OAAO;YAAC6K,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAC9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnK,OAAA,CAAClB,MAAM;UAAC4L,OAAO,EAAC,SAAS;UAACC,OAAO,EAAErG,WAAY;UAAAyF,QAAA,gBAC7C/J,OAAA,CAACL,OAAO;YAAC8K,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAC9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC/J,EAAA,CArlBID,mBAAmB;EAAA,QACFhC,SAAS,EACbC,WAAW;AAAA;AAAAoO,EAAA,GAFxBrM,mBAAmB;AAulBzB,eAAeA,mBAAmB;AAAC,IAAAqM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}