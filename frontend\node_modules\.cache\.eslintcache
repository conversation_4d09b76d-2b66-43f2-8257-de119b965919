[{"D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\index.js": "1", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\reportWebVitals.js": "2", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\App.js": "3", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\axiosConfig.js": "4", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewCutting.js": "5", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddCutting.js": "6", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewApproveProduct.js": "7", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingSessions.js": "8", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewProductList.js": "9", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddPackingSession.js": "10", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventorySales.js": "11", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventory.js": "12", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddShop.js": "13", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\PackingReportChart.js": "14", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SellProductPage.js": "15", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\CreateOrder.js": "16", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrderListPage.js.js": "17", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OwnerOrdersPage.js": "18", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesTeamOrdersPage.js": "19", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ShopAnalysisDashboard.js": "20", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewShops.js": "21", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesReport.js": "22", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrderAnalysisPage.js": "23", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\Login.js": "24", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\Signup.js": "25", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OwnerDashboard.js": "26", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrdersDashboard.js": "27", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\InventoryDashboard.js": "28", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesDashboard.js": "29", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewSuppliers.js": "30", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabrics.js": "31", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddSupplier.js": "32", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\FabricInventoryDetail.js": "33", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabricVariants.js": "34", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductImageViewer.js": "35", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductView.js": "36", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewDailySewingHistory.js": "37", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\CuttingRecordDetail.js": "38", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddDailySewingRecord.js": "39", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\EditFabric.js": "40", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddFabric.js": "41", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ApproveFinishedProduct.js": "42", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ProtectedRoute.js": "43", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\auth.js": "44", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\RoleBasedNavBar.js": "45", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\SalesTeamNavBar.js": "46", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\InvoicePreviewModal.js": "47", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\OrderCoordinatorNavBar.js": "48", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\PaymentModal.js": "49", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\DeliveryModal.js": "50", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\RevertOrderModal.js": "51", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ShopDistrictAnalysis.js": "52", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\DashboardCard.js": "53", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\api.js": "54", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\InventoryManagerNavBar.js": "55", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\OwnerNavBar.js": "56", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ColorVariantSelector.js": "57", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\EditCutting.js": "58", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\firebase\\imageUpload.js": "59", "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\firebase\\config.js": "60"}, {"size": 706, "mtime": 1751484740456, "results": "61", "hashOfConfig": "62"}, {"size": 375, "mtime": 1751484740488, "results": "63", "hashOfConfig": "62"}, {"size": 10566, "mtime": 1752904426669, "results": "64", "hashOfConfig": "62"}, {"size": 1578, "mtime": 1751484851142, "results": "65", "hashOfConfig": "62"}, {"size": 33975, "mtime": 1752904404645, "results": "66", "hashOfConfig": "62"}, {"size": 37457, "mtime": 1752905945652, "results": "67", "hashOfConfig": "62"}, {"size": 99330, "mtime": 1751484740481, "results": "68", "hashOfConfig": "62"}, {"size": 8358, "mtime": 1751484740485, "results": "69", "hashOfConfig": "62"}, {"size": 33531, "mtime": 1751484740486, "results": "70", "hashOfConfig": "62"}, {"size": 14435, "mtime": 1751484740459, "results": "71", "hashOfConfig": "62"}, {"size": 111, "mtime": 1751484740485, "results": "72", "hashOfConfig": "62"}, {"size": 48259, "mtime": 1751484740484, "results": "73", "hashOfConfig": "62"}, {"size": 24066, "mtime": 1751484740460, "results": "74", "hashOfConfig": "62"}, {"size": 2458, "mtime": 1751484740473, "results": "75", "hashOfConfig": "62"}, {"size": 29017, "mtime": 1751484740478, "results": "76", "hashOfConfig": "62"}, {"size": 50544, "mtime": 1751484740463, "results": "77", "hashOfConfig": "62"}, {"size": 17459, "mtime": 1751484740468, "results": "78", "hashOfConfig": "62"}, {"size": 53281, "mtime": 1751484740472, "results": "79", "hashOfConfig": "62"}, {"size": 24077, "mtime": 1751484740477, "results": "80", "hashOfConfig": "62"}, {"size": 5738, "mtime": 1751484740479, "results": "81", "hashOfConfig": "62"}, {"size": 8673, "mtime": 1751484740487, "results": "82", "hashOfConfig": "62"}, {"size": 26457, "mtime": 1751484851139, "results": "83", "hashOfConfig": "62"}, {"size": 36946, "mtime": 1751484740468, "results": "84", "hashOfConfig": "62"}, {"size": 14310, "mtime": 1751484740467, "results": "85", "hashOfConfig": "62"}, {"size": 26301, "mtime": 1751484740479, "results": "86", "hashOfConfig": "62"}, {"size": 67507, "mtime": 1751570608573, "results": "87", "hashOfConfig": "62"}, {"size": 14637, "mtime": 1751484740469, "results": "88", "hashOfConfig": "62"}, {"size": 42225, "mtime": 1751484740467, "results": "89", "hashOfConfig": "62"}, {"size": 6098, "mtime": 1751484740473, "results": "90", "hashOfConfig": "62"}, {"size": 13919, "mtime": 1751484740487, "results": "91", "hashOfConfig": "62"}, {"size": 29956, "mtime": 1751484740484, "results": "92", "hashOfConfig": "62"}, {"size": 13849, "mtime": 1751484740461, "results": "93", "hashOfConfig": "62"}, {"size": 11398, "mtime": 1751484740466, "results": "94", "hashOfConfig": "62"}, {"size": 44234, "mtime": 1751484740483, "results": "95", "hashOfConfig": "62"}, {"size": 14826, "mtime": 1751484740475, "results": "96", "hashOfConfig": "62"}, {"size": 25701, "mtime": 1751484740476, "results": "97", "hashOfConfig": "62"}, {"size": 12824, "mtime": 1752839680723, "results": "98", "hashOfConfig": "62"}, {"size": 24094, "mtime": 1752906002212, "results": "99", "hashOfConfig": "62"}, {"size": 43002, "mtime": 1753436068195, "results": "100", "hashOfConfig": "62"}, {"size": 20927, "mtime": 1752837944800, "results": "101", "hashOfConfig": "62"}, {"size": 18502, "mtime": 1752837916360, "results": "102", "hashOfConfig": "62"}, {"size": 65576, "mtime": 1752843506645, "results": "103", "hashOfConfig": "62"}, {"size": 1939, "mtime": 1751484740451, "results": "104", "hashOfConfig": "62"}, {"size": 2550, "mtime": 1751484740490, "results": "105", "hashOfConfig": "62"}, {"size": 1843, "mtime": 1751484740452, "results": "106", "hashOfConfig": "62"}, {"size": 7639, "mtime": 1751484740452, "results": "107", "hashOfConfig": "62"}, {"size": 9995, "mtime": 1751484740447, "results": "108", "hashOfConfig": "62"}, {"size": 11014, "mtime": 1751484740448, "results": "109", "hashOfConfig": "62"}, {"size": 11059, "mtime": 1751484740449, "results": "110", "hashOfConfig": "62"}, {"size": 5758, "mtime": 1751484740446, "results": "111", "hashOfConfig": "62"}, {"size": 6549, "mtime": 1751484740451, "results": "112", "hashOfConfig": "62"}, {"size": 6283, "mtime": 1751484740453, "results": "113", "hashOfConfig": "62"}, {"size": 1611, "mtime": 1751484740446, "results": "114", "hashOfConfig": "62"}, {"size": 2374, "mtime": 1751484851141, "results": "115", "hashOfConfig": "62"}, {"size": 8076, "mtime": 1751484740446, "results": "116", "hashOfConfig": "62"}, {"size": 7986, "mtime": 1751484740448, "results": "117", "hashOfConfig": "62"}, {"size": 3658, "mtime": 1752838730558, "results": "118", "hashOfConfig": "62"}, {"size": 26312, "mtime": 1753415577587, "results": "119", "hashOfConfig": "62"}, {"size": 3230, "mtime": 1751484740455, "results": "120", "hashOfConfig": "62"}, {"size": 1337, "mtime": 1751484740454, "results": "121", "hashOfConfig": "62"}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1y57vs8", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\index.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\App.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\axiosConfig.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewCutting.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddCutting.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewApproveProduct.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingSessions.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewProductList.js", ["302"], ["303"], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddPackingSession.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventorySales.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventory.js", ["304", "305", "306"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddShop.js", ["307"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\PackingReportChart.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SellProductPage.js", ["308", "309", "310", "311", "312", "313"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\CreateOrder.js", ["314", "315", "316"], ["317"], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrderListPage.js.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OwnerOrdersPage.js", [], ["318"], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesTeamOrdersPage.js", ["319", "320"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ShopAnalysisDashboard.js", ["321"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewShops.js", ["322", "323", "324"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesReport.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrderAnalysisPage.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\Login.js", ["325"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\Signup.js", ["326", "327", "328"], ["329"], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OwnerDashboard.js", [], ["330"], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\OrdersDashboard.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\InventoryDashboard.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesDashboard.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewSuppliers.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabrics.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddSupplier.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\FabricInventoryDetail.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabricVariants.js", ["331", "332", "333", "334"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductImageViewer.js", ["335"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductView.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ViewDailySewingHistory.js", ["336"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\CuttingRecordDetail.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddDailySewingRecord.js", ["337", "338"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\EditFabric.js", ["339", "340"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\AddFabric.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\ApproveFinishedProduct.js", ["341", "342", "343", "344", "345"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\auth.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\RoleBasedNavBar.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\SalesTeamNavBar.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\InvoicePreviewModal.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\OrderCoordinatorNavBar.js", ["346"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\PaymentModal.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\DeliveryModal.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\RevertOrderModal.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ShopDistrictAnalysis.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\DashboardCard.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\utils\\api.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\InventoryManagerNavBar.js", ["347"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\OwnerNavBar.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\components\\ColorVariantSelector.js", [], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\pages\\EditCutting.js", ["348", "349", "350", "351", "352", "353"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\firebase\\imageUpload.js", ["354", "355"], [], "D:\\Pri Fashion Software\\Pri_Fashion_\\frontend\\src\\firebase\\config.js", [], [], {"ruleId": "356", "severity": 1, "message": "357", "line": 5, "column": 23, "nodeType": "358", "messageId": "359", "endLine": 5, "endColumn": 30}, {"ruleId": "360", "severity": 1, "message": "361", "line": 177, "column": 6, "nodeType": "362", "endLine": 177, "endColumn": 8, "suggestions": "363", "suppressions": "364"}, {"ruleId": "356", "severity": 1, "message": "365", "line": 31, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 31, "endColumn": 9}, {"ruleId": "356", "severity": 1, "message": "366", "line": 33, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 33, "endColumn": 12}, {"ruleId": "356", "severity": 1, "message": "367", "line": 39, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 39, "endColumn": 12}, {"ruleId": "356", "severity": 1, "message": "368", "line": 77, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 77, "endColumn": 25}, {"ruleId": "356", "severity": 1, "message": "369", "line": 3, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 17}, {"ruleId": "356", "severity": 1, "message": "370", "line": 3, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 26}, {"ruleId": "356", "severity": 1, "message": "371", "line": 3, "column": 28, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 42}, {"ruleId": "356", "severity": 1, "message": "372", "line": 10, "column": 25, "nodeType": "358", "messageId": "359", "endLine": 10, "endColumn": 41}, {"ruleId": "360", "severity": 1, "message": "373", "line": 71, "column": 6, "nodeType": "362", "endLine": 71, "endColumn": 28, "suggestions": "374"}, {"ruleId": "356", "severity": 1, "message": "375", "line": 111, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 111, "endColumn": 30}, {"ruleId": "356", "severity": 1, "message": "376", "line": 76, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 76, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "377", "line": 76, "column": 23, "nodeType": "358", "messageId": "359", "endLine": 76, "endColumn": 37}, {"ruleId": "356", "severity": 1, "message": "378", "line": 672, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 672, "endColumn": 20}, {"ruleId": "360", "severity": 1, "message": "379", "line": 230, "column": 6, "nodeType": "362", "endLine": 230, "endColumn": 8, "suggestions": "380", "suppressions": "381"}, {"ruleId": "356", "severity": 1, "message": "382", "line": 230, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 230, "endColumn": 28, "suppressions": "383"}, {"ruleId": "356", "severity": 1, "message": "384", "line": 2, "column": 63, "nodeType": "358", "messageId": "359", "endLine": 2, "endColumn": 76}, {"ruleId": "356", "severity": 1, "message": "385", "line": 2, "column": 78, "nodeType": "358", "messageId": "359", "endLine": 2, "endColumn": 88}, {"ruleId": "356", "severity": 1, "message": "386", "line": 3, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 29}, {"ruleId": "356", "severity": 1, "message": "387", "line": 3, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 24}, {"ruleId": "356", "severity": 1, "message": "388", "line": 3, "column": 26, "nodeType": "358", "messageId": "359", "endLine": 3, "endColumn": 29}, {"ruleId": "356", "severity": 1, "message": "389", "line": 4, "column": 62, "nodeType": "358", "messageId": "359", "endLine": 4, "endColumn": 68}, {"ruleId": "356", "severity": 1, "message": "370", "line": 11, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 11, "endColumn": 10}, {"ruleId": "356", "severity": 1, "message": "390", "line": 27, "column": 20, "nodeType": "358", "messageId": "359", "endLine": 27, "endColumn": 31}, {"ruleId": "356", "severity": 1, "message": "391", "line": 36, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 36, "endColumn": 27}, {"ruleId": "356", "severity": 1, "message": "392", "line": 36, "column": 29, "nodeType": "358", "messageId": "359", "endLine": 36, "endColumn": 49}, {"ruleId": "360", "severity": 1, "message": "393", "line": 101, "column": 6, "nodeType": "362", "endLine": 101, "endColumn": 16, "suggestions": "394", "suppressions": "395"}, {"ruleId": "360", "severity": 1, "message": "396", "line": 251, "column": 6, "nodeType": "362", "endLine": 251, "endColumn": 8, "suggestions": "397", "suppressions": "398"}, {"ruleId": "356", "severity": 1, "message": "399", "line": 5, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 5, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "400", "line": 9, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 9, "endColumn": 7}, {"ruleId": "356", "severity": 1, "message": "389", "line": 14, "column": 41, "nodeType": "358", "messageId": "359", "endLine": 14, "endColumn": 47}, {"ruleId": "356", "severity": 1, "message": "401", "line": 28, "column": 30, "nodeType": "358", "messageId": "359", "endLine": 28, "endColumn": 51}, {"ruleId": "356", "severity": 1, "message": "402", "line": 4, "column": 59, "nodeType": "358", "messageId": "359", "endLine": 4, "endColumn": 66}, {"ruleId": "356", "severity": 1, "message": "403", "line": 19, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 19, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "404", "line": 12, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 12, "endColumn": 17}, {"ruleId": "356", "severity": 1, "message": "405", "line": 12, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 12, "endColumn": 29}, {"ruleId": "356", "severity": 1, "message": "399", "line": 9, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 9, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "401", "line": 42, "column": 30, "nodeType": "358", "messageId": "359", "endLine": 42, "endColumn": 51}, {"ruleId": "356", "severity": 1, "message": "406", "line": 8, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 8, "endColumn": 15}, {"ruleId": "356", "severity": 1, "message": "407", "line": 35, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 35, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "408", "line": 35, "column": 22, "nodeType": "358", "messageId": "359", "endLine": 35, "endColumn": 35}, {"ruleId": "356", "severity": 1, "message": "409", "line": 224, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 224, "endColumn": 25}, {"ruleId": "356", "severity": 1, "message": "410", "line": 263, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 263, "endColumn": 23}, {"ruleId": "356", "severity": 1, "message": "411", "line": 5, "column": 37, "nodeType": "358", "messageId": "359", "endLine": 5, "endColumn": 48}, {"ruleId": "356", "severity": 1, "message": "412", "line": 10, "column": 3, "nodeType": "358", "messageId": "359", "endLine": 10, "endColumn": 13}, {"ruleId": "356", "severity": 1, "message": "413", "line": 4, "column": 8, "nodeType": "358", "messageId": "359", "endLine": 4, "endColumn": 14}, {"ruleId": "356", "severity": 1, "message": "414", "line": 7, "column": 74, "nodeType": "358", "messageId": "359", "endLine": 7, "endColumn": 79}, {"ruleId": "356", "severity": 1, "message": "415", "line": 39, "column": 10, "nodeType": "358", "messageId": "359", "endLine": 39, "endColumn": 24}, {"ruleId": "360", "severity": 1, "message": "416", "line": 157, "column": 6, "nodeType": "362", "endLine": 157, "endColumn": 53, "suggestions": "417"}, {"ruleId": "356", "severity": 1, "message": "418", "line": 288, "column": 13, "nodeType": "358", "messageId": "359", "endLine": 288, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "419", "line": 312, "column": 9, "nodeType": "358", "messageId": "359", "endLine": 312, "endColumn": 21}, {"ruleId": "420", "severity": 1, "message": "421", "line": 71, "column": 9, "nodeType": "422", "messageId": "423", "endLine": 79, "endColumn": 10}, {"ruleId": "420", "severity": 1, "message": "421", "line": 83, "column": 26, "nodeType": "422", "messageId": "423", "endLine": 85, "endColumn": 8}, "no-unused-vars", "'hasRole' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["424"], ["425"], "'BsShop' is defined but never used.", "'BsGraphUp' is defined but never used.", "'FaHistory' is defined but never used.", "'locationError' is assigned a value but never used.", "'FaStore' is defined but never used.", "'FaBoxes' is defined but never used.", "'FaShoppingCart' is defined but never used.", "'setIsSidebarOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addProductToSelection'. Either include it or remove the dependency array.", ["426"], "'updateProductQuantity' is assigned a value but never used.", "'isInfoModal' is assigned a value but never used.", "'setIsInfoModal' is assigned a value but never used.", "'getShopName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["427"], ["428"], "'handleMarkDelivered' is assigned a value but never used.", ["429"], "'FaFileInvoice' is defined but never used.", "'FaDownload' is defined but never used.", "'FaChartPie' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'FaEdit' is defined but never used.", "'setUserRole' is assigned a value but never used.", "'editPasswordError' is assigned a value but never used.", "'setEditPasswordError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["430"], ["431"], "React Hook useEffect has a missing dependency: 'timeFrame'. Either include it or remove the dependency array.", ["432"], ["433"], "'getUserRole' is defined but never used.", "'Form' is defined but never used.", "'setIsInventoryManager' is assigned a value but never used.", "'FaTimes' is defined but never used.", "'totalItems' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'FaArrowRight' is defined but never used.", "'isDragging' is assigned a value but never used.", "'setIsDragging' is assigned a value but never used.", "'triggerFileInput' is assigned a value but never used.", "'setActiveImage' is assigned a value but never used.", "'FaChartLine' is defined but never used.", "'FaBuilding' is defined but never used.", "'Select' is defined but never used.", "'Modal' is defined but never used.", "'originalRecord' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'detailErrors'. Either include it or remove the dependency array.", ["434"], "'response' is assigned a value but never used.", "'ColourOption' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'completedFiles'.", "ArrowFunctionExpression", "unsafeRefs", {"desc": "435", "fix": "436"}, {"kind": "437", "justification": "438"}, {"desc": "439", "fix": "440"}, {"desc": "441", "fix": "442"}, {"kind": "437", "justification": "438"}, {"kind": "437", "justification": "438"}, {"desc": "443", "fix": "444"}, {"kind": "437", "justification": "438"}, {"desc": "445", "fix": "446"}, {"kind": "437", "justification": "438"}, {"desc": "447", "fix": "448"}, "Update the dependencies array to be: [fetchProducts]", {"range": "449", "text": "450"}, "directive", "", "Update the dependencies array to be: [addProductToSelection, preSelectedProductId]", {"range": "451", "text": "452"}, "Update the dependencies array to be: [fetchData]", {"range": "453", "text": "454"}, "Update the dependencies array to be: [fetchUsers, userRole]", {"range": "455", "text": "456"}, "Update the dependencies array to be: [timeFrame]", {"range": "457", "text": "458"}, "Update the dependencies array to be: [allFabricVariants, detailErrors, details, originalYardUsage]", {"range": "459", "text": "460"}, [5738, 5740], "[fetchProducts]", [2958, 2980], "[addProductToSelection, preSelectedProductId]", [7406, 7408], "[fetchData]", [3791, 3801], "[fetchUsers, userRole]", [8600, 8602], "[timeFrame]", [6133, 6180], "[allFabricVariants, detailErrors, details, originalYardUsage]"]